"""
Test script for Visual Reinforcement Learning System
===================================================

This script tests all components of the RL system to ensure everything works correctly.

Author: Augment Agent
"""

import sys
import os
import numpy as np
import cv2
import time
from datetime import datetime

def test_imports():
    """Test if all required libraries can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
    except ImportError:
        print("❌ PyTorch not found. Install with: pip install torch")
        return False
    
    try:
        import torchvision
        print(f"✅ TorchVision: {torchvision.__version__}")
    except ImportError:
        print("❌ TorchVision not found. Install with: pip install torchvision")
        return False
    
    try:
        from skimage import segmentation, feature, measure
        print("✅ Scikit-image available")
    except ImportError:
        print("❌ Scikit-image not found. Install with: pip install scikit-image")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print("✅ Matplotlib available")
    except ImportError:
        print("❌ Matplotlib not found. Install with: pip install matplotlib")
        return False
    
    try:
        from sklearn.cluster import KMeans
        print("✅ Scikit-learn available")
    except ImportError:
        print("❌ Scikit-learn not found. Install with: pip install scikit-learn")
        return False
    
    print("✅ All imports successful!")
    return True

def test_visual_learning():
    """Test the visual learning component"""
    print("\n🧪 Testing Visual Learning Component...")
    
    try:
        from rl_visual_learning import UnsupervisedVisualLearner
        
        # Create learner
        learner = UnsupervisedVisualLearner()
        print("✅ Visual learner created")
        
        # Create test images
        test_images = []
        for i in range(5):
            # Create synthetic test image with different patterns
            img = np.zeros((100, 100, 3), dtype=np.uint8)
            
            # Add some shapes and colors
            cv2.rectangle(img, (10, 10), (50, 50), (255, 0, 0), -1)  # Blue rectangle
            cv2.circle(img, (70, 30), 15, (0, 255, 0), -1)  # Green circle
            cv2.line(img, (0, 80), (100, 80), (0, 0, 255), 3)  # Red line
            
            # Add some noise
            noise = np.random.randint(0, 50, (100, 100, 3), dtype=np.uint8)
            img = cv2.add(img, noise)
            
            test_images.append(img)
        
        print(f"✅ Created {len(test_images)} test images")
        
        # Test feature extraction
        features = learner.extract_visual_features(test_images[0])
        print(f"✅ Extracted features: {list(features.keys())}")
        
        # Test image segmentation
        regions, segments = learner.segment_image(test_images[0])
        print(f"✅ Segmented image into {len(regions)} regions")
        
        # Test learning (with small dataset)
        learner.learn_visual_patterns(test_images)
        print("✅ Visual pattern learning completed")
        
        # Test encoding
        encoding, raw_features = learner.encode_image(test_images[0])
        print(f"✅ Visual encoding: {encoding}")
        
        # Test save/load
        learner.save_visual_learning()
        print("✅ Visual learning saved")
        
        new_learner = UnsupervisedVisualLearner()
        loaded = new_learner.load_visual_learning()
        print(f"✅ Visual learning loaded: {loaded}")
        
        return True
        
    except Exception as e:
        print(f"❌ Visual learning test failed: {e}")
        return False

def test_rl_agent():
    """Test the RL agent component"""
    print("\n🧪 Testing RL Agent Component...")
    
    try:
        from rl_visual_learning import ReinforcementLearningAgent
        
        # Create agent
        agent = ReinforcementLearningAgent(state_size=10, action_size=3)
        print("✅ RL agent created")
        
        # Test action selection
        test_state = np.random.random(10)
        action = agent.act(test_state)
        print(f"✅ Selected action: {action}")
        
        # Test memory
        next_state = np.random.random(10)
        agent.remember(test_state, action, 1.0, next_state, False)
        print(f"✅ Memory size: {len(agent.memory)}")
        
        # Test network forward pass
        import torch
        state_tensor = torch.FloatTensor(test_state).unsqueeze(0)
        q_values = agent.q_network(state_tensor)
        print(f"✅ Q-values shape: {q_values.shape}")
        
        # Test save/load
        agent.save_model()
        print("✅ RL model saved")
        
        new_agent = ReinforcementLearningAgent(state_size=10, action_size=3)
        loaded = new_agent.load_model()
        print(f"✅ RL model loaded: {loaded}")
        
        return True
        
    except Exception as e:
        print(f"❌ RL agent test failed: {e}")
        return False

def test_complete_system():
    """Test the complete integrated system"""
    print("\n🧪 Testing Complete System...")
    
    try:
        from rl_visual_learning import VisualReinforcementLearningSystem
        
        # Create system (with test crop box)
        system = VisualReinforcementLearningSystem(crop_box=(0, 0, 100, 100))
        print("✅ Complete system created")
        
        # Test state representation with synthetic image
        test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # First need to create some visual vocabulary
        system.visual_learner.learn_visual_patterns([test_image] * 5)
        
        # Now test state representation
        state = system.create_state_representation(test_image)
        print(f"✅ State representation shape: {state.shape}")
        
        # Test reward calculation
        reward = system.calculate_reward(0, 0)  # Correct prediction
        print(f"✅ Reward for correct prediction: {reward}")
        
        reward = system.calculate_reward(0, 1)  # Incorrect prediction
        print(f"✅ Reward for incorrect prediction: {reward}")
        
        # Test performance report
        report = system.get_performance_report()
        print("✅ Performance report generated")
        
        return True
        
    except Exception as e:
        print(f"❌ Complete system test failed: {e}")
        return False

def test_integration():
    """Test the integration module"""
    print("\n🧪 Testing Integration Module...")
    
    try:
        from rl_integration import HybridPredictionSystem
        
        # Create hybrid system
        hybrid = HybridPredictionSystem()
        print("✅ Hybrid system created")
        
        # Test performance tracking
        hybrid.update_performance(
            {'traditional_ml': {'prediction': 'A', 'confidence': 80}}, 
            'A'
        )
        print("✅ Performance tracking works")
        
        # Test performance report
        report = hybrid.get_performance_report()
        print("✅ Performance report generated")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("🚀 Starting Visual RL System Tests")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Visual Learning Test", test_visual_learning),
        ("RL Agent Test", test_rl_agent),
        ("Complete System Test", test_complete_system),
        ("Integration Test", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        return True
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        return False

def create_demo_data():
    """Create some demo data for testing"""
    print("\n🎨 Creating demo data...")
    
    # Create demo images directory
    demo_dir = "demo_images"
    os.makedirs(demo_dir, exist_ok=True)
    
    # Create various demo images
    for i in range(10):
        img = np.zeros((200, 300, 3), dtype=np.uint8)
        
        # Add different patterns for each image
        if i % 3 == 0:
            # Pattern A: Blue rectangles
            cv2.rectangle(img, (50, 50), (100, 100), (255, 0, 0), -1)
            cv2.rectangle(img, (150, 50), (200, 100), (255, 0, 0), -1)
        elif i % 3 == 1:
            # Pattern B: Green circles
            cv2.circle(img, (75, 75), 25, (0, 255, 0), -1)
            cv2.circle(img, (175, 75), 25, (0, 255, 0), -1)
        else:
            # Pattern C: Red triangles
            pts = np.array([[75, 50], [50, 100], [100, 100]], np.int32)
            cv2.fillPoly(img, [pts], (0, 0, 255))
            pts = np.array([[175, 50], [150, 100], [200, 100]], np.int32)
            cv2.fillPoly(img, [pts], (0, 0, 255))
        
        # Add some noise
        noise = np.random.randint(0, 30, (200, 300, 3), dtype=np.uint8)
        img = cv2.add(img, noise)
        
        # Save image
        cv2.imwrite(f"{demo_dir}/demo_{i:02d}.png", img)
    
    print(f"✅ Created {10} demo images in {demo_dir}/")

if __name__ == "__main__":
    print("🤖 Visual Reinforcement Learning System - Test Suite")
    print("=" * 60)
    print(f"🕐 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create demo data
    create_demo_data()
    
    # Run all tests
    success = run_all_tests()
    
    print(f"\n🕐 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("\n🚀 System is ready! You can now:")
        print("   1. Run: python rl_visual_learning.py")
        print("   2. Run: python rl_integration.py")
        print("   3. Follow the RL_VISUAL_LEARNING_GUIDE.md")
    else:
        print("\n⚠️ Please fix the failing tests before proceeding.")
    
    sys.exit(0 if success else 1)
