"""
Auto Process All Images - No Manual Input Required
=================================================

Automatically processes all images in the images folder without any manual input.
"""

from quick_capture import QuickCapture
import os

def main():
    """Auto-process all images without manual input"""
    print("🚀 AUTO PROCESSING ALL POKER IMAGES")
    print("=" * 50)
    print("🎯 Features:")
    print("   ✅ Auto-detects blue_seat, golden_seat, purple_seat")
    print("   ✅ Maps winning_seat to closest colored seat")
    print("   ✅ Processes ALL images automatically")
    print("   ✅ No manual input required")
    print()
    
    # Initialize capture system
    model_path = "models/capture.pt"
    capture = QuickCapture(model_path)
    
    if not capture.model:
        print("❌ Cannot proceed without model")
        return
    
    # Check if images folder exists
    if not os.path.exists("images"):
        print("❌ Images folder not found")
        return
    
    # Get image count
    image_files = [f for f in os.listdir("images") if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
    print(f"📸 Found {len(image_files)} images to process")
    
    if len(image_files) == 0:
        print("❌ No images found in images folder")
        return
    
    print(f"\n🚀 Starting automatic processing of {len(image_files)} images...")
    print("🔄 This will run continuously without stopping")
    print("=" * 60)
    
    # Start automatic processing
    successful = capture.process_all_images()
    
    print(f"\n🎉 AUTO PROCESSING COMPLETE!")
    print(f"✅ Successfully processed: {successful} images")
    print(f"📊 Database now contains {successful} poker game records")
    
    # Show final database summary
    print(f"\n📋 Final Database Summary:")
    capture.view_database()

if __name__ == "__main__":
    main()
