"""
Advanced OCR-Based Poker Game Capture System
===========================================

Features:
- Brightness-based winning seat detection
- OCR text extraction for card numbers, suits, and game text
- Comprehensive database storage
- Automatic processing of all images

Author: Augment Agent
"""

import cv2
import numpy as np
import sqlite3
import json
from datetime import datetime
import os
from ultralytics import YOLO
import re

# Import PaddleOCR
try:
    from paddleocr import PaddleOCR
    PADDLE_OCR_AVAILABLE = True
    print("✅ PaddleOCR available")
except ImportError:
    print("⚠️ PaddleOCR not available, falling back to Tesseract")
    PADDLE_OCR_AVAILABLE = False

# Fallback OCR if PaddleOCR is not available
try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

class AdvancedOCRCapture:
    """
    Advanced OCR-based poker game capture system
    """
    
    def __init__(self, model_path="models/capture_m.pt"):
        self.model_path = model_path
        self.model = None
        self.db_path = "poker_games_advanced.db"

        # Initialize OCR engine
        self.ocr_engine = None
        self.init_ocr_engine()

        self.init_advanced_database()
        self.load_model()

    def init_ocr_engine(self):
        """Initialize OCR engine (PaddleOCR preferred, Tesseract fallback)"""
        if PADDLE_OCR_AVAILABLE:
            try:
                # Initialize PaddleOCR with English language
                self.ocr_engine = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
                print("✅ PaddleOCR initialized successfully")
                return
            except Exception as e:
                print(f"⚠️ PaddleOCR initialization failed: {e}")

        if TESSERACT_AVAILABLE:
            try:
                # Set Tesseract path (adjust if needed)
                pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
                self.ocr_engine = "tesseract"
                print("✅ Tesseract OCR initialized as fallback")
                return
            except Exception as e:
                print(f"⚠️ Tesseract initialization failed: {e}")

        print("❌ No OCR engine available!")
        self.ocr_engine = None

    def extract_text_with_ocr(self, image_region, config_type="general"):
        """Extract text using available OCR engine"""
        if self.ocr_engine is None:
            return ""

        try:
            if isinstance(self.ocr_engine, PaddleOCR):
                # Use PaddleOCR
                results = self.ocr_engine.ocr(image_region, cls=True)

                if results and results[0]:
                    # Extract text from PaddleOCR results
                    text_parts = []
                    for line in results[0]:
                        if line and len(line) > 1:
                            text_parts.append(line[1][0])  # line[1][0] contains the text

                    return " ".join(text_parts).strip()
                return ""

            elif self.ocr_engine == "tesseract":
                # Use Tesseract
                if len(image_region.shape) == 3:
                    gray = cv2.cvtColor(image_region, cv2.COLOR_BGR2GRAY)
                else:
                    gray = image_region

                # Apply threshold for better text recognition
                _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

                # Different configs for different types
                if config_type == "cards":
                    config = '--psm 8 -c tessedit_char_whitelist=0123456789AJQK♠♥♦♣'
                elif config_type == "title":
                    config = '--psm 7'
                else:
                    config = '--psm 6'

                return pytesseract.image_to_string(thresh, config=config).strip()

        except Exception as e:
            print(f"❌ OCR extraction error: {e}")
            return ""

        return ""
    
    def load_model(self):
        """Load the YOLO model"""
        if not os.path.exists(self.model_path):
            print(f"❌ Model not found: {self.model_path}")
            return False
        
        try:
            self.model = YOLO(self.model_path)
            print(f"✅ Model loaded: {self.model_path}")
            return True
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False
    
    def init_advanced_database(self):
        """Initialize comprehensive database schema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Main games table with poker hand analysis
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS poker_games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                image_path TEXT NOT NULL,
                winning_seat TEXT,
                winning_seat_brightness REAL,
                game_title TEXT,

                -- Seat A (Left) Analysis
                seat_a_hand_type TEXT,
                seat_a_card1_number TEXT,
                seat_a_card1_suit TEXT,
                seat_a_card2_number TEXT,
                seat_a_card2_suit TEXT,
                seat_a_card3_number TEXT,
                seat_a_card3_suit TEXT,
                seat_a_hand_strength INTEGER,
                seat_a_pot TEXT,

                -- Seat B (Center) Analysis
                seat_b_hand_type TEXT,
                seat_b_card1_number TEXT,
                seat_b_card1_suit TEXT,
                seat_b_card2_number TEXT,
                seat_b_card2_suit TEXT,
                seat_b_card3_number TEXT,
                seat_b_card3_suit TEXT,
                seat_b_hand_strength INTEGER,
                seat_b_pot TEXT,

                -- Seat C (Right) Analysis
                seat_c_hand_type TEXT,
                seat_c_card1_number TEXT,
                seat_c_card1_suit TEXT,
                seat_c_card2_number TEXT,
                seat_c_card2_suit TEXT,
                seat_c_card3_number TEXT,
                seat_c_card3_suit TEXT,
                seat_c_hand_strength INTEGER,
                seat_c_pot TEXT,

                middle_text TEXT
            )
        ''')
        
        # Individual cards table with parsed values
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id INTEGER,
                seat TEXT,
                card_position INTEGER,
                card_number TEXT,
                card_suit TEXT,
                card_value INTEGER,
                ocr_text TEXT,
                confidence REAL,
                bbox_x REAL,
                bbox_y REAL,
                bbox_width REAL,
                bbox_height REAL,
                FOREIGN KEY (game_id) REFERENCES poker_games (id)
            )
        ''')

        # Hand rankings reference table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS hand_rankings (
                id INTEGER PRIMARY KEY,
                hand_type TEXT UNIQUE,
                strength INTEGER,
                description TEXT
            )
        ''')

        # Insert hand rankings
        hand_types = [
            (1, 'High Card', 1, 'No matching cards'),
            (2, 'Pair', 2, 'Two cards of same rank'),
            (3, 'Two Pair', 3, 'Two different pairs'),
            (4, 'Three of a Kind', 4, 'Three cards of same rank'),
            (5, 'Straight', 5, 'Five consecutive cards'),
            (6, 'Flush', 6, 'Five cards of same suit'),
            (7, 'Full House', 7, 'Three of a kind + pair'),
            (8, 'Four of a Kind', 8, 'Four cards of same rank'),
            (9, 'Straight Flush', 9, 'Straight + flush'),
            (10, 'Royal Flush', 10, 'A-K-Q-J-10 of same suit'),
            (11, 'Color', 11, 'Three cards of same suit'),
            (12, 'Sequence', 12, 'Three consecutive cards')
        ]

        cursor.executemany('''
            INSERT OR REPLACE INTO hand_rankings (id, hand_type, strength, description)
            VALUES (?, ?, ?, ?)
        ''', hand_types)
        
        # OCR text extractions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ocr_extractions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id INTEGER,
                text_type TEXT,
                extracted_text TEXT,
                confidence REAL,
                bbox_x REAL,
                bbox_y REAL,
                bbox_width REAL,
                bbox_height REAL,
                FOREIGN KEY (game_id) REFERENCES poker_games (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print(f"✅ Advanced database ready: {self.db_path}")
    
    def detect_and_analyze_advanced(self, image_path):
        """Advanced detection with OCR and grayscale brightness analysis"""
        if not self.model:
            print("❌ Model not loaded")
            return None

        if not os.path.exists(image_path):
            print(f"❌ Image not found: {image_path}")
            return None

        print(f"🔍 Advanced Analysis: {os.path.basename(image_path)}")

        # Load original image
        image = cv2.imread(image_path)
        if image is None:
            print("❌ Could not load image")
            return None

        # Run YOLO detection on original image
        results = self.model(image_path, conf=0.3, verbose=False)

        # Parse YOLO detections
        detections = []
        if results and len(results) > 0:
            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        class_id = int(box.cls[0])
                        confidence = float(box.conf[0])
                        bbox = box.xyxy[0].cpu().numpy()

                        class_name = self.model.names[class_id]

                        detections.append({
                            'class': class_name,
                            'confidence': confidence,
                            'bbox': bbox.tolist(),
                            'center_x': float((bbox[0] + bbox[2]) / 2),
                            'center_y': float((bbox[1] + bbox[3]) / 2)
                        })

        print(f"📊 YOLO Detections: {len(detections)}")

        # Analyze card areas brightness
        card_area_analysis = self.analyze_card_areas_brightness(image, detections)

        # Extract OCR text from card regions
        ocr_analysis = self.extract_ocr_from_regions(image, detections)

        # Extract game title and middle text
        game_text = self.extract_game_text(image)

        return {
            'detections': detections,
            'card_area_analysis': card_area_analysis,
            'ocr_analysis': ocr_analysis,
            'game_text': game_text,
            'image_path': image_path,
            'image': image
        }
    
    def analyze_card_areas_brightness(self, image, detections):
        """Analyze card sequence areas brightness - brightest card area wins"""
        # Look for card-related detections that form sequences
        card_areas = []

        # Group card detections by proximity to form card areas
        card_detections = []
        for det in detections:
            class_name = det['class'].lower()
            # Look for card numbers, suits, or any card-related classes
            if class_name in ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'j', 'q', 'k', 'a',
                             'spades', 'hearts', 'diamonds', 'clubs', 'cards', 'card_area']:
                card_detections.append(det)

        if not card_detections:
            print("❌ No card detections found for area analysis")
            # Fallback: create artificial card areas based on image layout
            return self.create_artificial_card_areas(image)

        print(f"🃏 Found {len(card_detections)} card detections for area grouping")

        # Group cards into 3 areas (left, center, right)
        card_areas = self.group_cards_into_areas(card_detections, image)

        if not card_areas:
            print("❌ Could not group cards into areas")
            return self.create_artificial_card_areas(image)

        print(f"🎯 Grouped cards into {len(card_areas)} card areas")

        # Calculate brightness for each card area
        area_brightness = {}

        for i, area in enumerate(card_areas):
            area_name = f"card_area_{i+1}"
            bbox = area['bbox']
            x1, y1, x2, y2 = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])

            # Extract card area region
            area_region = image[y1:y2, x1:x2]

            if area_region.size > 0:
                # Convert to grayscale
                gray_region = cv2.cvtColor(area_region, cv2.COLOR_BGR2GRAY)

                # Calculate average brightness (mean pixel intensity)
                brightness = np.mean(gray_region)

                area_brightness[area_name] = {
                    'brightness': brightness,
                    'bbox': bbox,
                    'card_count': area['card_count'],
                    'center_x': area['center_x'],
                    'center_y': area['center_y']
                }

                print(f"🔍 {area_name}: Brightness = {brightness:.2f} ({area['card_count']} cards)")

        # Find BRIGHTEST card area as winner and map to seat
        if area_brightness:
            winner = max(area_brightness.keys(), key=lambda x: area_brightness[x]['brightness'])
            winner_brightness = area_brightness[winner]['brightness']
            winner_info = area_brightness[winner]

            # Map card area to seat letter
            area_to_seat = {
                'card_area_1': 'A',  # Left area = Seat A
                'card_area_2': 'B',  # Center area = Seat B
                'card_area_3': 'C'   # Right area = Seat C
            }

            winning_seat = area_to_seat.get(winner, winner)

            print(f"🏆 BRIGHTEST CARD AREA WINS: {winner} → SEAT {winning_seat} (brightness: {winner_brightness:.2f})")

            # Show comparison (sorted from brightest to darkest)
            print("📊 Card Area Brightness Comparison (Brightest to Darkest):")
            for area, info in sorted(area_brightness.items(), key=lambda x: x[1]['brightness'], reverse=True):
                seat_letter = area_to_seat.get(area, area)
                status = "🥇 WINNER" if area == winner else "🥈 LOSER"
                print(f"   {area} (Seat {seat_letter}): {info['brightness']:.2f} {status}")

            return {
                'card_areas': card_areas,
                'area_brightness': area_brightness,
                'winning_area': winner,
                'winning_seat': winning_seat,
                'winning_brightness': winner_brightness,
                'winning_area_info': {
                    'area_name': winner,
                    'seat_letter': winning_seat,
                    'brightness': winner_brightness,
                    'card_count': winner_info['card_count']
                }
            }

        return None

    def group_cards_into_areas(self, card_detections, image):
        """Group card detections into 3 areas based on horizontal position"""
        if not card_detections:
            return []

        height, width = image.shape[:2]

        # Sort cards by x-coordinate (left to right)
        sorted_cards = sorted(card_detections, key=lambda x: x['center_x'])

        # Divide into 3 groups
        total_cards = len(sorted_cards)
        cards_per_group = max(1, total_cards // 3)

        areas = []
        for i in range(3):
            start_idx = i * cards_per_group
            end_idx = start_idx + cards_per_group if i < 2 else total_cards

            group_cards = sorted_cards[start_idx:end_idx]

            if group_cards:
                # Calculate bounding box for this group
                min_x = min(card['bbox'][0] for card in group_cards)
                min_y = min(card['bbox'][1] for card in group_cards)
                max_x = max(card['bbox'][2] for card in group_cards)
                max_y = max(card['bbox'][3] for card in group_cards)

                # Expand area slightly
                padding = 20
                min_x = max(0, min_x - padding)
                min_y = max(0, min_y - padding)
                max_x = min(width, max_x + padding)
                max_y = min(height, max_y + padding)

                areas.append({
                    'bbox': [min_x, min_y, max_x, max_y],
                    'card_count': len(group_cards),
                    'center_x': (min_x + max_x) / 2,
                    'center_y': (min_y + max_y) / 2,
                    'cards': group_cards
                })

        return areas

    def create_artificial_card_areas(self, image):
        """Create 3 artificial card areas when no cards detected"""
        height, width = image.shape[:2]

        # Create 3 horizontal areas (left, center, right)
        area_width = width // 3
        area_height = height // 3  # Top third of image typically has cards

        areas = []
        for i in range(3):
            x1 = i * area_width
            x2 = (i + 1) * area_width
            y1 = 0
            y2 = area_height

            areas.append({
                'bbox': [x1, y1, x2, y2],
                'card_count': 0,
                'center_x': (x1 + x2) / 2,
                'center_y': (y1 + y2) / 2,
                'cards': []
            })

        print("🔧 Created 3 artificial card areas (no cards detected)")
        return areas
    
    def extract_ocr_from_regions(self, image, detections):
        """Extract OCR text from detected card and suit regions"""
        ocr_results = []
        
        # Group detections by type
        suits = [d for d in detections if d['class'].lower() in ['spades', 'hearts', 'diamonds', 'clubs']]
        cards = [d for d in detections if d['class'].lower() in ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'j', 'q', 'k', 'a']]
        
        print(f"🔤 OCR Analysis: {len(suits)} suits, {len(cards)} cards")
        
        # Extract text from each detection region
        all_regions = suits + cards
        
        for i, detection in enumerate(all_regions):
            bbox = detection['bbox']
            x1, y1, x2, y2 = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])
            
            # Expand region slightly for better OCR
            padding = 10
            x1 = max(0, x1 - padding)
            y1 = max(0, y1 - padding)
            x2 = min(image.shape[1], x2 + padding)
            y2 = min(image.shape[0], y2 + padding)
            
            # Extract region
            region = image[y1:y2, x1:x2]
            
            if region.size > 0:
                try:
                    # OCR extraction using unified method
                    ocr_text = self.extract_text_with_ocr(region, config_type="cards")

                    if ocr_text:
                        ocr_results.append({
                            'detection_type': detection['class'],
                            'ocr_text': ocr_text,
                            'bbox': bbox,
                            'yolo_confidence': detection['confidence']
                        })

                        print(f"📝 {detection['class']}: '{ocr_text}'")

                except Exception as e:
                    print(f"❌ OCR error for {detection['class']}: {e}")
        
        return ocr_results
    
    def extract_game_text(self, image):
        """Extract game title and middle text using OCR"""
        height, width = image.shape[:2]
        
        # Extract title area (top 15% of image)
        title_region = image[0:int(height * 0.15), :]
        
        # Extract middle area (center 30% of image)
        middle_start = int(height * 0.35)
        middle_end = int(height * 0.65)
        middle_region = image[middle_start:middle_end, int(width * 0.2):int(width * 0.8)]
        
        game_text = {}
        
        try:
            # Extract title using unified OCR
            title_text = self.extract_text_with_ocr(title_region, config_type="title")
            game_text['title'] = title_text
            print(f"🎮 Game Title: '{title_text}'")

            # Extract middle text using unified OCR
            middle_text = self.extract_text_with_ocr(middle_region, config_type="general")
            game_text['middle_text'] = middle_text
            print(f"📄 Middle Text: '{middle_text}'")

        except Exception as e:
            print(f"❌ Game text extraction error: {e}")
            game_text = {'title': '', 'middle_text': ''}
        
        return game_text
    
    def get_seat_name(self, class_name):
        """Return the actual seat color name"""
        class_name = class_name.lower()

        if 'blue' in class_name:
            return 'blue_seat'
        elif 'golden' in class_name or 'yellow' in class_name:
            return 'golden_seat'
        elif 'purple' in class_name:
            return 'purple_seat'
        else:
            return class_name

    def associate_cards_with_areas(self, ocr_results, card_area_analysis):
        """Associate extracted cards with card areas based on proximity"""
        if not card_area_analysis or not ocr_results:
            return {'card_area_1': [], 'card_area_2': [], 'card_area_3': []}

        area_cards = {'card_area_1': [], 'card_area_2': [], 'card_area_3': []}
        card_areas = card_area_analysis['card_areas']

        for ocr_result in ocr_results:
            bbox = ocr_result['bbox']
            card_center_x = (bbox[0] + bbox[2]) / 2
            card_center_y = (bbox[1] + bbox[3]) / 2

            # Find closest card area
            closest_area = None
            min_distance = float('inf')
            closest_area_name = None

            for i, area in enumerate(card_areas):
                area_center_x = area['center_x']
                area_center_y = area['center_y']

                distance = np.sqrt((card_center_x - area_center_x)**2 + (card_center_y - area_center_y)**2)

                if distance < min_distance:
                    min_distance = distance
                    closest_area = area
                    closest_area_name = f"card_area_{i+1}"

            if closest_area and closest_area_name:
                if closest_area_name in area_cards:
                    area_cards[closest_area_name].append({
                        'detection_type': ocr_result['detection_type'],
                        'ocr_text': ocr_result['ocr_text'],
                        'yolo_confidence': ocr_result['yolo_confidence'],
                        'bbox': ocr_result['bbox'],
                        'distance_to_area': min_distance
                    })

                    print(f"🎯 {ocr_result['detection_type']} '{ocr_result['ocr_text']}' -> {closest_area_name}")

        return area_cards

    def extract_pot_amounts(self, image, card_area_analysis):
        """Extract pot amounts for each card area"""
        if not card_area_analysis:
            return {'card_area_1': '', 'card_area_2': '', 'card_area_3': ''}

        pot_amounts = {'card_area_1': '', 'card_area_2': '', 'card_area_3': ''}
        card_areas = card_area_analysis['card_areas']

        height, width = image.shape[:2]

        for i, area in enumerate(card_areas):
            area_name = f"card_area_{i+1}"
            bbox = area['bbox']

            # Look for pot text below the card area
            x1, y1, x2, y2 = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])

            # Expand search area below card area
            pot_y1 = y2
            pot_y2 = min(height, y2 + 150)  # Look 150 pixels below card area
            pot_x1 = max(0, x1 - 50)
            pot_x2 = min(width, x2 + 50)

            pot_region = image[pot_y1:pot_y2, pot_x1:pot_x2]

            if pot_region.size > 0:
                try:
                    # Use unified OCR method
                    pot_text = self.extract_text_with_ocr(pot_region, config_type="general")

                    # Look for pot pattern (Pot:XXXK)
                    pot_match = re.search(r'Pot[:\s]*(\d+[KMB]?)', pot_text, re.IGNORECASE)
                    if pot_match:
                        pot_amounts[area_name] = pot_match.group(1)
                        print(f"💰 {area_name} Pot: {pot_match.group(1)}")
                    else:
                        pot_amounts[area_name] = pot_text[:20] if pot_text else ''

                except Exception as e:
                    print(f"❌ Pot extraction error for {area_name}: {e}")

        return pot_amounts

    def parse_card_value(self, card_text):
        """Parse card text to extract number and suit"""
        if not card_text:
            return None, None, 0

        card_text = str(card_text).upper().strip()

        # Map card numbers to values
        card_values = {
            '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
            'J': 11, 'Q': 12, 'K': 13, 'A': 14
        }

        # Extract number
        number = None
        value = 0
        for card_num, card_val in card_values.items():
            if card_num in card_text:
                number = card_num
                value = card_val
                break

        # Extract suit
        suit = None
        if 'SPADES' in card_text or '♠' in card_text:
            suit = 'Spades'
        elif 'HEARTS' in card_text or '♥' in card_text:
            suit = 'Hearts'
        elif 'DIAMONDS' in card_text or '♦' in card_text:
            suit = 'Diamonds'
        elif 'CLUBS' in card_text or '♣' in card_text:
            suit = 'Clubs'

        return number, suit, value

    def analyze_poker_hand(self, cards):
        """Analyze 3-card poker hand and determine hand type"""
        if not cards or len(cards) == 0:
            return 'No Cards', 0, [], []

        # Parse all cards
        parsed_cards = []
        for card in cards:
            number, suit, value = self.parse_card_value(card.get('ocr_text', ''))
            if number and suit:
                parsed_cards.append({
                    'number': number,
                    'suit': suit,
                    'value': value,
                    'original': card
                })

        if len(parsed_cards) == 0:
            return 'No Valid Cards', 0, [], []

        # Sort by value for analysis
        parsed_cards.sort(key=lambda x: x['value'])

        numbers = [card['number'] for card in parsed_cards]
        suits = [card['suit'] for card in parsed_cards]
        values = [card['value'] for card in parsed_cards]

        # Analyze hand type (3-card poker rules)
        if len(parsed_cards) >= 3:
            # Check for sequence (3 consecutive cards)
            if len(values) >= 3 and values[2] - values[0] == 2 and values[1] - values[0] == 1:
                if len(set(suits[:3])) == 1:  # Same suit
                    return 'Straight Flush', 9, numbers[:3], suits[:3]
                else:
                    return 'Sequence', 12, numbers[:3], suits[:3]

            # Check for color (3 cards same suit)
            if len(set(suits[:3])) == 1:
                return 'Color', 11, numbers[:3], suits[:3]

            # Check for three of a kind
            if values[0] == values[1] == values[2]:
                return 'Three of a Kind', 4, numbers[:3], suits[:3]

            # Check for pair
            if values[0] == values[1] or values[1] == values[2]:
                return 'Pair', 2, numbers[:3], suits[:3]

        elif len(parsed_cards) == 2:
            # Check for pair with 2 cards
            if values[0] == values[1]:
                return 'Pair', 2, numbers, suits

            # Check for same suit
            if suits[0] == suits[1]:
                return 'Color (2 cards)', 11, numbers, suits

        # High card
        return 'High Card', 1, numbers, suits

    def save_to_advanced_database(self, result):
        """Save comprehensive results to advanced database"""
        if not result:
            return None

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        timestamp = datetime.now().isoformat()

        # Extract data
        card_area_analysis = result['card_area_analysis']
        ocr_analysis = result['ocr_analysis']
        game_text = result['game_text']

        # Associate cards with card areas
        area_cards = self.associate_cards_with_areas(ocr_analysis, card_area_analysis)

        # Extract pot amounts
        pot_amounts = self.extract_pot_amounts(result['image'], card_area_analysis)

        # Determine winning seat from brightness analysis
        winning_seat = None
        winning_brightness = None
        if card_area_analysis and card_area_analysis['winning_area_info']:
            winning_seat = card_area_analysis['winning_area_info']['seat_letter']
            winning_brightness = card_area_analysis['winning_area_info']['brightness']

        # Analyze poker hands for each seat
        seat_analyses = {}
        area_to_seat = {'card_area_1': 'A', 'card_area_2': 'B', 'card_area_3': 'C'}

        for area in ['card_area_1', 'card_area_2', 'card_area_3']:
            seat_letter = area_to_seat[area]
            cards = area_cards[area]

            # Analyze poker hand
            hand_type, hand_strength, numbers, suits = self.analyze_poker_hand(cards)

            # Prepare card data (up to 3 cards)
            card_data = {
                'hand_type': hand_type,
                'hand_strength': hand_strength,
                'card1_number': numbers[0] if len(numbers) > 0 else '',
                'card1_suit': suits[0] if len(suits) > 0 else '',
                'card2_number': numbers[1] if len(numbers) > 1 else '',
                'card2_suit': suits[1] if len(suits) > 1 else '',
                'card3_number': numbers[2] if len(numbers) > 2 else '',
                'card3_suit': suits[2] if len(suits) > 2 else '',
            }

            seat_analyses[seat_letter] = card_data

            print(f"🎯 Seat {seat_letter}: {hand_type} - {' '.join(numbers)} ({' '.join(suits)})")

        # Insert main game record with poker hand analysis
        cursor.execute('''
            INSERT INTO poker_games
            (timestamp, image_path, winning_seat, winning_seat_brightness, game_title,
             seat_a_hand_type, seat_a_card1_number, seat_a_card1_suit, seat_a_card2_number, seat_a_card2_suit,
             seat_a_card3_number, seat_a_card3_suit, seat_a_hand_strength, seat_a_pot,
             seat_b_hand_type, seat_b_card1_number, seat_b_card1_suit, seat_b_card2_number, seat_b_card2_suit,
             seat_b_card3_number, seat_b_card3_suit, seat_b_hand_strength, seat_b_pot,
             seat_c_hand_type, seat_c_card1_number, seat_c_card1_suit, seat_c_card2_number, seat_c_card2_suit,
             seat_c_card3_number, seat_c_card3_suit, seat_c_hand_strength, seat_c_pot,
             middle_text)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            timestamp,
            result['image_path'],
            winning_seat,
            winning_brightness,
            game_text.get('title', ''),
            # Seat A data
            seat_analyses['A']['hand_type'],
            seat_analyses['A']['card1_number'],
            seat_analyses['A']['card1_suit'],
            seat_analyses['A']['card2_number'],
            seat_analyses['A']['card2_suit'],
            seat_analyses['A']['card3_number'],
            seat_analyses['A']['card3_suit'],
            seat_analyses['A']['hand_strength'],
            pot_amounts['card_area_1'],
            # Seat B data
            seat_analyses['B']['hand_type'],
            seat_analyses['B']['card1_number'],
            seat_analyses['B']['card1_suit'],
            seat_analyses['B']['card2_number'],
            seat_analyses['B']['card2_suit'],
            seat_analyses['B']['card3_number'],
            seat_analyses['B']['card3_suit'],
            seat_analyses['B']['hand_strength'],
            pot_amounts['card_area_2'],
            # Seat C data
            seat_analyses['C']['hand_type'],
            seat_analyses['C']['card1_number'],
            seat_analyses['C']['card1_suit'],
            seat_analyses['C']['card2_number'],
            seat_analyses['C']['card2_suit'],
            seat_analyses['C']['card3_number'],
            seat_analyses['C']['card3_suit'],
            seat_analyses['C']['hand_strength'],
            pot_amounts['card_area_3'],
            game_text.get('middle_text', '')
        ))

        game_id = cursor.lastrowid

        # Insert individual card records with parsed values
        for area_name, cards in area_cards.items():
            for i, card in enumerate(cards):
                # Parse card value
                number, suit, value = self.parse_card_value(card['ocr_text'])

                cursor.execute('''
                    INSERT INTO cards
                    (game_id, seat, card_position, card_number, card_suit, card_value, ocr_text,
                     confidence, bbox_x, bbox_y, bbox_width, bbox_height)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    game_id,
                    area_name,
                    i + 1,
                    number or '',
                    suit or '',
                    value,
                    card['ocr_text'],
                    card['yolo_confidence'],
                    card['bbox'][0],
                    card['bbox'][1],
                    card['bbox'][2] - card['bbox'][0],
                    card['bbox'][3] - card['bbox'][1]
                ))

        # Insert OCR extractions
        for ocr_result in ocr_analysis:
            cursor.execute('''
                INSERT INTO ocr_extractions
                (game_id, text_type, extracted_text, confidence, bbox_x, bbox_y, bbox_width, bbox_height)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                game_id,
                ocr_result['detection_type'],
                ocr_result['ocr_text'],
                ocr_result['yolo_confidence'],
                ocr_result['bbox'][0],
                ocr_result['bbox'][1],
                ocr_result['bbox'][2] - ocr_result['bbox'][0],
                ocr_result['bbox'][3] - ocr_result['bbox'][1]
            ))

        conn.commit()
        conn.close()

        # Print comprehensive results with poker hand analysis
        print(f"\n💾 SAVED TO ADVANCED DATABASE (Game ID: {game_id})")
        print("=" * 80)
        print(f"🎮 Game Title: {game_text.get('title', 'N/A')}")
        print(f"🏆 Winning Seat: {winning_seat} (Brightness: {winning_brightness:.2f})" if winning_brightness else f"🏆 Winning Seat: {winning_seat or 'Not detected'}")
        print()
        print("🃏 POKER HAND ANALYSIS:")
        print("-" * 50)

        for seat_letter in ['A', 'B', 'C']:
            analysis = seat_analyses[seat_letter]
            area_name = {'A': 'card_area_1', 'B': 'card_area_2', 'C': 'card_area_3'}[seat_letter]

            cards_str = []
            for i in range(1, 4):
                number = analysis[f'card{i}_number']
                suit = analysis[f'card{i}_suit']
                if number and suit:
                    cards_str.append(f"{number}{suit[0]}")  # e.g., "7S" for 7 of Spades

            cards_display = " ".join(cards_str) if cards_str else "No cards"
            pot_display = pot_amounts[area_name][:15] if pot_amounts[area_name] else "No pot"

            print(f"🎯 Seat {seat_letter}: {analysis['hand_type']:15s} | Cards: {cards_display:15s} | Pot: {pot_display}")

        print(f"\n📄 Middle Text: {game_text.get('middle_text', 'N/A')}")

        return game_id

    def process_image_advanced(self, image_path):
        """Process a single image with advanced OCR analysis"""
        result = self.detect_and_analyze_advanced(image_path)
        if result:
            game_id = self.save_to_advanced_database(result)
            return game_id
        return None

    def process_all_images_advanced(self, folder="images"):
        """Process all images with advanced OCR analysis"""
        if not os.path.exists(folder):
            print(f"❌ Folder not found: {folder}")
            return 0

        # Get all image files
        image_files = [f for f in os.listdir(folder) if f.lower().endswith(('.jpg', '.png', '.jpeg'))]

        if not image_files:
            print(f"❌ No images found in {folder}")
            return 0

        print(f"🚀 ADVANCED OCR PROCESSING - {len(image_files)} images")
        print("=" * 70)
        print("🎯 Features:")
        print("   ✅ Grayscale + reduced brightness analysis")
        print("   ✅ DARKEST seat wins (lowest brightness after reduction)")
        print("   ✅ No A/B/C naming - uses actual seat colors")
        print("   ✅ OCR extraction of card numbers and suits")
        print("   ✅ Game title and middle text extraction")
        print("   ✅ Pot amount extraction")
        print("   ✅ Comprehensive database storage")
        print()

        successful = 0
        failed = 0

        for i, filename in enumerate(image_files):
            image_path = os.path.join(folder, filename)

            print(f"\n📷 [{i+1}/{len(image_files)}] Processing: {filename}")
            print("-" * 70)

            try:
                game_id = self.process_image_advanced(image_path)
                if game_id:
                    successful += 1
                    print(f"✅ Success! Game ID: {game_id}")
                else:
                    failed += 1
                    print("❌ Failed to process")
            except Exception as e:
                failed += 1
                print(f"❌ Error: {e}")

            # Small delay for readability
            if i < len(image_files) - 1:
                import time
                time.sleep(1)  # 1 second delay to see results

        print(f"\n🎉 ADVANCED OCR PROCESSING COMPLETE!")
        print("=" * 60)
        print(f"✅ Successfully processed: {successful}")
        print(f"❌ Failed: {failed}")
        print(f"📊 Total: {successful + failed}/{len(image_files)}")

        if successful > 0:
            print(f"\n📋 Advanced database now contains {successful} detailed game records!")
            self.view_advanced_database()

        return successful

    def view_advanced_database(self):
        """View advanced database contents"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT COUNT(*) FROM poker_games")
        count = cursor.fetchone()[0]

        if count == 0:
            print("📊 Advanced database is empty")
            conn.close()
            return

        print(f"\n📊 ADVANCED DATABASE CONTENTS ({count} games)")
        print("=" * 100)

        cursor.execute("""
            SELECT id, timestamp, winning_seat, winning_seat_brightness, game_title,
                   seat_a_combination, seat_b_combination, seat_c_combination,
                   seat_a_pot, seat_b_pot, seat_c_pot, middle_text
            FROM poker_games
            ORDER BY timestamp DESC
            LIMIT 5
        """)

        results = cursor.fetchall()

        for row in results:
            (game_id, timestamp, winning_seat, brightness, title,
             a_combo, b_combo, c_combo, a_pot, b_pot, c_pot, middle_text) = row

            timestamp = timestamp[:19]
            title = title[:20] if title else 'N/A'
            middle_text = middle_text[:30] if middle_text else 'N/A'

            print(f"\nGame {game_id:3d} | {timestamp} | Winner: {winning_seat or 'None':1s} (Brightness: {brightness or 0:.1f})")
            print(f"         Title: {title}")
            print(f"         A: {a_combo[:20]:20s} | Pot: {a_pot[:10]:10s}")
            print(f"         B: {b_combo[:20]:20s} | Pot: {b_pot[:10]:10s}")
            print(f"         C: {c_combo[:20]:20s} | Pot: {c_pot[:10]:10s}")
            print(f"         Middle: {middle_text}")

        # Show card details for latest game
        if results:
            latest_game_id = results[0][0]
            print(f"\n🃏 CARD DETAILS FOR LATEST GAME (ID: {latest_game_id}):")
            print("-" * 60)

            cursor.execute("""
                SELECT seat, card_position, card_number, card_suit, ocr_text, confidence
                FROM cards
                WHERE game_id = ?
                ORDER BY seat, card_position
            """, (latest_game_id,))

            card_results = cursor.fetchall()

            current_seat = None
            for seat, pos, number, suit, ocr_text, conf in card_results:
                if seat != current_seat:
                    print(f"\n🎯 Seat {seat}:")
                    current_seat = seat

                print(f"   Card {pos}: {ocr_text} (Number: {number}, Suit: {suit}, Conf: {conf:.2f})")

        conn.close()


def main():
    """Main function for advanced OCR capture"""
    print("🚀 ADVANCED OCR POKER CAPTURE SYSTEM")
    print("=" * 50)
    print("🎯 Enhanced Features:")
    print("   ✅ Grayscale + reduced brightness analysis")
    print("   ✅ DARKEST seat wins (lowest brightness after reduction)")
    print("   ✅ No A/B/C naming - uses actual seat colors")
    print("   ✅ OCR extraction of card numbers and suits")
    print("   ✅ Game title and combination text extraction")
    print("   ✅ Pot amount extraction")
    print("   ✅ Comprehensive database with individual card records")
    print("   ✅ Automatic processing of all images")
    print()

    # Initialize advanced capture system
    model_path = "models/capture.pt"
    capture = AdvancedOCRCapture(model_path)

    if not capture.model:
        print("❌ Cannot proceed without model")
        return

    # Check if images folder exists
    if not os.path.exists("images"):
        print("❌ Images folder not found")
        return

    # Get image count
    image_files = [f for f in os.listdir("images") if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
    print(f"📸 Found {len(image_files)} images to process")

    if len(image_files) == 0:
        print("❌ No images found in images folder")
        return

    print(f"\n🚀 Starting advanced OCR processing of {len(image_files)} images...")
    print("🔄 This will run automatically with detailed OCR analysis")
    print("=" * 70)

    # Start automatic advanced processing
    successful = capture.process_all_images_advanced()

    print(f"\n🎉 ADVANCED OCR PROCESSING COMPLETE!")
    print(f"✅ Successfully processed: {successful} images")
    print(f"📊 Advanced database now contains detailed records for {successful} games")


if __name__ == "__main__":
    main()
