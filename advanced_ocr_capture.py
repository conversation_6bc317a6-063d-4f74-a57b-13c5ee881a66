"""
Advanced OCR-Based Poker Game Capture System
===========================================

Features:
- Brightness-based winning seat detection
- OCR text extraction for card numbers, suits, and game text
- Comprehensive database storage
- Automatic processing of all images

Author: Augment Agent
"""

import cv2
import numpy as np
import sqlite3
import json
from datetime import datetime
import os
import pytesseract
from ultralytics import YOLO
import re

class AdvancedOCRCapture:
    """
    Advanced OCR-based poker game capture system
    """
    
    def __init__(self, model_path="models/capture.pt"):
        self.model_path = model_path
        self.model = None
        self.db_path = "poker_games_advanced.db"
        
        # Set Tesseract path (adjust if needed)
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'
        
        self.init_advanced_database()
        self.load_model()
    
    def load_model(self):
        """Load the YOLO model"""
        if not os.path.exists(self.model_path):
            print(f"❌ Model not found: {self.model_path}")
            return False
        
        try:
            self.model = YOLO(self.model_path)
            print(f"✅ Model loaded: {self.model_path}")
            return True
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False
    
    def init_advanced_database(self):
        """Initialize comprehensive database schema"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Main games table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS poker_games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                image_path TEXT NOT NULL,
                winning_seat TEXT,
                winning_seat_brightness REAL,
                game_title TEXT,
                seat_a_combination TEXT,
                seat_b_combination TEXT,
                seat_c_combination TEXT,
                seat_a_pot TEXT,
                seat_b_pot TEXT,
                seat_c_pot TEXT,
                middle_text TEXT
            )
        ''')
        
        # Individual cards table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cards (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id INTEGER,
                seat TEXT,
                card_position INTEGER,
                card_number TEXT,
                card_suit TEXT,
                ocr_text TEXT,
                confidence REAL,
                bbox_x REAL,
                bbox_y REAL,
                bbox_width REAL,
                bbox_height REAL,
                FOREIGN KEY (game_id) REFERENCES poker_games (id)
            )
        ''')
        
        # OCR text extractions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS ocr_extractions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id INTEGER,
                text_type TEXT,
                extracted_text TEXT,
                confidence REAL,
                bbox_x REAL,
                bbox_y REAL,
                bbox_width REAL,
                bbox_height REAL,
                FOREIGN KEY (game_id) REFERENCES poker_games (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print(f"✅ Advanced database ready: {self.db_path}")
    
    def detect_and_analyze_advanced(self, image_path):
        """Advanced detection with OCR and brightness analysis"""
        if not self.model:
            print("❌ Model not loaded")
            return None
        
        if not os.path.exists(image_path):
            print(f"❌ Image not found: {image_path}")
            return None
        
        print(f"🔍 Advanced Analysis: {os.path.basename(image_path)}")
        
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print("❌ Could not load image")
            return None
        
        # Run YOLO detection
        results = self.model(image_path, conf=0.3, verbose=False)
        
        # Parse YOLO detections
        detections = []
        if results and len(results) > 0:
            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        class_id = int(box.cls[0])
                        confidence = float(box.conf[0])
                        bbox = box.xyxy[0].cpu().numpy()
                        
                        class_name = self.model.names[class_id]
                        
                        detections.append({
                            'class': class_name,
                            'confidence': confidence,
                            'bbox': bbox.tolist(),
                            'center_x': float((bbox[0] + bbox[2]) / 2),
                            'center_y': float((bbox[1] + bbox[3]) / 2)
                        })
        
        print(f"📊 YOLO Detections: {len(detections)}")
        
        # Analyze seats and determine winner by brightness
        seat_analysis = self.analyze_seats_with_brightness(image, detections)
        
        # Extract OCR text from card regions
        ocr_analysis = self.extract_ocr_from_regions(image, detections)
        
        # Extract game title and middle text
        game_text = self.extract_game_text(image)
        
        return {
            'detections': detections,
            'seat_analysis': seat_analysis,
            'ocr_analysis': ocr_analysis,
            'game_text': game_text,
            'image_path': image_path,
            'image': image
        }
    
    def analyze_seats_with_brightness(self, image, detections):
        """Analyze seats and determine winner by brightness"""
        colored_seats = []
        
        for det in detections:
            class_name = det['class'].lower()
            if class_name in ['blue_seat', 'golden_seat', 'purple_seat']:
                colored_seats.append(det)
        
        if not colored_seats:
            print("❌ No colored seats detected")
            return None
        
        # Calculate brightness for each seat
        seat_brightness = {}
        
        for seat in colored_seats:
            bbox = seat['bbox']
            x1, y1, x2, y2 = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])
            
            # Extract seat region
            seat_region = image[y1:y2, x1:x2]
            
            # Calculate average brightness
            gray_region = cv2.cvtColor(seat_region, cv2.COLOR_BGR2GRAY)
            brightness = np.mean(gray_region)
            
            seat_name = self.get_seat_name(seat['class'])
            seat_brightness[seat_name] = {
                'brightness': brightness,
                'seat_color': seat['class'],
                'bbox': bbox
            }
            
            print(f"💡 {seat['class']} (Seat {seat_name}): Brightness = {brightness:.2f}")
        
        # Find brightest seat as winner
        if seat_brightness:
            winner = max(seat_brightness.keys(), key=lambda x: seat_brightness[x]['brightness'])
            winner_brightness = seat_brightness[winner]['brightness']
            
            print(f"🏆 Brightest seat: Seat {winner} (brightness: {winner_brightness:.2f})")
            
            return {
                'colored_seats': colored_seats,
                'seat_brightness': seat_brightness,
                'winning_seat': winner,
                'winning_brightness': winner_brightness
            }
        
        return None
    
    def extract_ocr_from_regions(self, image, detections):
        """Extract OCR text from detected card and suit regions"""
        ocr_results = []
        
        # Group detections by type
        suits = [d for d in detections if d['class'].lower() in ['spades', 'hearts', 'diamonds', 'clubs']]
        cards = [d for d in detections if d['class'].lower() in ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'j', 'q', 'k', 'a']]
        
        print(f"🔤 OCR Analysis: {len(suits)} suits, {len(cards)} cards")
        
        # Extract text from each detection region
        all_regions = suits + cards
        
        for i, detection in enumerate(all_regions):
            bbox = detection['bbox']
            x1, y1, x2, y2 = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])
            
            # Expand region slightly for better OCR
            padding = 10
            x1 = max(0, x1 - padding)
            y1 = max(0, y1 - padding)
            x2 = min(image.shape[1], x2 + padding)
            y2 = min(image.shape[0], y2 + padding)
            
            # Extract region
            region = image[y1:y2, x1:x2]
            
            if region.size > 0:
                try:
                    # Preprocess for better OCR
                    gray = cv2.cvtColor(region, cv2.COLOR_BGR2GRAY)
                    
                    # Apply threshold for better text recognition
                    _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                    
                    # OCR extraction
                    ocr_text = pytesseract.image_to_string(thresh, config='--psm 8 -c tessedit_char_whitelist=0123456789AJQK♠♥♦♣').strip()
                    
                    if ocr_text:
                        ocr_results.append({
                            'detection_type': detection['class'],
                            'ocr_text': ocr_text,
                            'bbox': bbox,
                            'yolo_confidence': detection['confidence']
                        })
                        
                        print(f"📝 {detection['class']}: '{ocr_text}'")
                
                except Exception as e:
                    print(f"❌ OCR error for {detection['class']}: {e}")
        
        return ocr_results
    
    def extract_game_text(self, image):
        """Extract game title and middle text using OCR"""
        height, width = image.shape[:2]
        
        # Extract title area (top 15% of image)
        title_region = image[0:int(height * 0.15), :]
        
        # Extract middle area (center 30% of image)
        middle_start = int(height * 0.35)
        middle_end = int(height * 0.65)
        middle_region = image[middle_start:middle_end, int(width * 0.2):int(width * 0.8)]
        
        game_text = {}
        
        try:
            # Extract title
            title_gray = cv2.cvtColor(title_region, cv2.COLOR_BGR2GRAY)
            title_text = pytesseract.image_to_string(title_gray, config='--psm 7').strip()
            game_text['title'] = title_text
            print(f"🎮 Game Title: '{title_text}'")
            
            # Extract middle text
            middle_gray = cv2.cvtColor(middle_region, cv2.COLOR_BGR2GRAY)
            middle_text = pytesseract.image_to_string(middle_gray, config='--psm 6').strip()
            game_text['middle_text'] = middle_text
            print(f"📄 Middle Text: '{middle_text}'")
            
        except Exception as e:
            print(f"❌ Game text extraction error: {e}")
            game_text = {'title': '', 'middle_text': ''}
        
        return game_text
    
    def get_seat_name(self, class_name):
        """Convert colored seat class name to seat letter"""
        class_name = class_name.lower()

        if 'blue' in class_name:
            return 'A'
        elif 'golden' in class_name or 'yellow' in class_name:
            return 'B'
        elif 'purple' in class_name:
            return 'C'
        else:
            return 'Unknown'

    def associate_cards_with_seats(self, ocr_results, seat_analysis):
        """Associate extracted cards with seats based on proximity"""
        if not seat_analysis or not ocr_results:
            return {'A': [], 'B': [], 'C': []}

        seat_cards = {'A': [], 'B': [], 'C': []}
        colored_seats = seat_analysis['colored_seats']

        for ocr_result in ocr_results:
            bbox = ocr_result['bbox']
            card_center_x = (bbox[0] + bbox[2]) / 2
            card_center_y = (bbox[1] + bbox[3]) / 2

            # Find closest seat
            closest_seat = None
            min_distance = float('inf')

            for seat in colored_seats:
                seat_center_x = seat['center_x']
                seat_center_y = seat['center_y']

                distance = np.sqrt((card_center_x - seat_center_x)**2 + (card_center_y - seat_center_y)**2)

                if distance < min_distance:
                    min_distance = distance
                    closest_seat = seat

            if closest_seat:
                seat_name = self.get_seat_name(closest_seat['class'])
                if seat_name in seat_cards:
                    seat_cards[seat_name].append({
                        'detection_type': ocr_result['detection_type'],
                        'ocr_text': ocr_result['ocr_text'],
                        'yolo_confidence': ocr_result['yolo_confidence'],
                        'bbox': ocr_result['bbox'],
                        'distance_to_seat': min_distance
                    })

                    print(f"🎯 {ocr_result['detection_type']} '{ocr_result['ocr_text']}' -> Seat {seat_name}")

        return seat_cards

    def extract_pot_amounts(self, image, seat_analysis):
        """Extract pot amounts for each seat"""
        if not seat_analysis:
            return {'A': '', 'B': '', 'C': ''}

        pot_amounts = {'A': '', 'B': '', 'C': ''}
        colored_seats = seat_analysis['colored_seats']

        height, width = image.shape[:2]

        for seat in colored_seats:
            seat_name = self.get_seat_name(seat['class'])
            bbox = seat['bbox']

            # Look for pot text below the seat
            x1, y1, x2, y2 = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])

            # Expand search area below seat
            pot_y1 = y2
            pot_y2 = min(height, y2 + 100)  # Look 100 pixels below seat
            pot_x1 = max(0, x1 - 50)
            pot_x2 = min(width, x2 + 50)

            pot_region = image[pot_y1:pot_y2, pot_x1:pot_x2]

            if pot_region.size > 0:
                try:
                    gray = cv2.cvtColor(pot_region, cv2.COLOR_BGR2GRAY)
                    pot_text = pytesseract.image_to_string(gray, config='--psm 7').strip()

                    # Look for pot pattern (Pot:XXXK)
                    pot_match = re.search(r'Pot[:\s]*(\d+[KMB]?)', pot_text, re.IGNORECASE)
                    if pot_match:
                        pot_amounts[seat_name] = pot_match.group(1)
                        print(f"💰 Seat {seat_name} Pot: {pot_match.group(1)}")
                    else:
                        pot_amounts[seat_name] = pot_text[:20] if pot_text else ''

                except Exception as e:
                    print(f"❌ Pot extraction error for seat {seat_name}: {e}")

        return pot_amounts

    def save_to_advanced_database(self, result):
        """Save comprehensive results to advanced database"""
        if not result:
            return None

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        timestamp = datetime.now().isoformat()

        # Extract data
        seat_analysis = result['seat_analysis']
        ocr_analysis = result['ocr_analysis']
        game_text = result['game_text']

        # Associate cards with seats
        seat_cards = self.associate_cards_with_seats(ocr_analysis, seat_analysis)

        # Extract pot amounts
        pot_amounts = self.extract_pot_amounts(result['image'], seat_analysis)

        # Determine winning seat and brightness
        winning_seat = seat_analysis['winning_seat'] if seat_analysis else None
        winning_brightness = seat_analysis['winning_brightness'] if seat_analysis else None

        # Create card combinations for each seat
        seat_combinations = {}
        for seat in ['A', 'B', 'C']:
            cards = seat_cards[seat]
            if cards:
                # Sort cards by position (you might want to improve this logic)
                card_texts = [card['ocr_text'] for card in cards if card['ocr_text']]
                seat_combinations[seat] = ' '.join(card_texts[:3])  # Take first 3 cards
            else:
                seat_combinations[seat] = ''

        # Insert main game record
        cursor.execute('''
            INSERT INTO poker_games
            (timestamp, image_path, winning_seat, winning_seat_brightness, game_title,
             seat_a_combination, seat_b_combination, seat_c_combination,
             seat_a_pot, seat_b_pot, seat_c_pot, middle_text)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            timestamp,
            result['image_path'],
            winning_seat,
            winning_brightness,
            game_text.get('title', ''),
            seat_combinations['A'],
            seat_combinations['B'],
            seat_combinations['C'],
            pot_amounts['A'],
            pot_amounts['B'],
            pot_amounts['C'],
            game_text.get('middle_text', '')
        ))

        game_id = cursor.lastrowid

        # Insert individual card records
        for seat_name, cards in seat_cards.items():
            for i, card in enumerate(cards):
                cursor.execute('''
                    INSERT INTO cards
                    (game_id, seat, card_position, card_number, card_suit, ocr_text,
                     confidence, bbox_x, bbox_y, bbox_width, bbox_height)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    game_id,
                    seat_name,
                    i + 1,
                    card['ocr_text'] if card['detection_type'] in ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'j', 'q', 'k', 'a'] else '',
                    card['ocr_text'] if card['detection_type'] in ['spades', 'hearts', 'diamonds', 'clubs'] else '',
                    card['ocr_text'],
                    card['yolo_confidence'],
                    card['bbox'][0],
                    card['bbox'][1],
                    card['bbox'][2] - card['bbox'][0],
                    card['bbox'][3] - card['bbox'][1]
                ))

        # Insert OCR extractions
        for ocr_result in ocr_analysis:
            cursor.execute('''
                INSERT INTO ocr_extractions
                (game_id, text_type, extracted_text, confidence, bbox_x, bbox_y, bbox_width, bbox_height)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                game_id,
                ocr_result['detection_type'],
                ocr_result['ocr_text'],
                ocr_result['yolo_confidence'],
                ocr_result['bbox'][0],
                ocr_result['bbox'][1],
                ocr_result['bbox'][2] - ocr_result['bbox'][0],
                ocr_result['bbox'][3] - ocr_result['bbox'][1]
            ))

        conn.commit()
        conn.close()

        # Print comprehensive results
        print(f"\n💾 SAVED TO ADVANCED DATABASE (Game ID: {game_id})")
        print("=" * 60)
        print(f"🎮 Game Title: {game_text.get('title', 'N/A')}")
        print(f"🏆 Winning Seat: {winning_seat} (Brightness: {winning_brightness:.2f})")
        print(f"🎯 Seat A: {seat_combinations['A']} | Pot: {pot_amounts['A']}")
        print(f"🎯 Seat B: {seat_combinations['B']} | Pot: {pot_amounts['B']}")
        print(f"🎯 Seat C: {seat_combinations['C']} | Pot: {pot_amounts['C']}")
        print(f"📄 Middle Text: {game_text.get('middle_text', 'N/A')}")

        return game_id

    def process_image_advanced(self, image_path):
        """Process a single image with advanced OCR analysis"""
        result = self.detect_and_analyze_advanced(image_path)
        if result:
            game_id = self.save_to_advanced_database(result)
            return game_id
        return None

    def process_all_images_advanced(self, folder="images"):
        """Process all images with advanced OCR analysis"""
        if not os.path.exists(folder):
            print(f"❌ Folder not found: {folder}")
            return 0

        # Get all image files
        image_files = [f for f in os.listdir(folder) if f.lower().endswith(('.jpg', '.png', '.jpeg'))]

        if not image_files:
            print(f"❌ No images found in {folder}")
            return 0

        print(f"🚀 ADVANCED OCR PROCESSING - {len(image_files)} images")
        print("=" * 70)
        print("🎯 Features:")
        print("   ✅ Brightness-based winning seat detection")
        print("   ✅ OCR extraction of card numbers and suits")
        print("   ✅ Game title and middle text extraction")
        print("   ✅ Pot amount extraction")
        print("   ✅ Comprehensive database storage")
        print()

        successful = 0
        failed = 0

        for i, filename in enumerate(image_files):
            image_path = os.path.join(folder, filename)

            print(f"\n📷 [{i+1}/{len(image_files)}] Processing: {filename}")
            print("-" * 70)

            try:
                game_id = self.process_image_advanced(image_path)
                if game_id:
                    successful += 1
                    print(f"✅ Success! Game ID: {game_id}")
                else:
                    failed += 1
                    print("❌ Failed to process")
            except Exception as e:
                failed += 1
                print(f"❌ Error: {e}")

            # Small delay for readability
            if i < len(image_files) - 1:
                import time
                time.sleep(1)  # 1 second delay to see results

        print(f"\n🎉 ADVANCED OCR PROCESSING COMPLETE!")
        print("=" * 60)
        print(f"✅ Successfully processed: {successful}")
        print(f"❌ Failed: {failed}")
        print(f"📊 Total: {successful + failed}/{len(image_files)}")

        if successful > 0:
            print(f"\n📋 Advanced database now contains {successful} detailed game records!")
            self.view_advanced_database()

        return successful

    def view_advanced_database(self):
        """View advanced database contents"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute("SELECT COUNT(*) FROM poker_games")
        count = cursor.fetchone()[0]

        if count == 0:
            print("📊 Advanced database is empty")
            conn.close()
            return

        print(f"\n📊 ADVANCED DATABASE CONTENTS ({count} games)")
        print("=" * 100)

        cursor.execute("""
            SELECT id, timestamp, winning_seat, winning_seat_brightness, game_title,
                   seat_a_combination, seat_b_combination, seat_c_combination,
                   seat_a_pot, seat_b_pot, seat_c_pot, middle_text
            FROM poker_games
            ORDER BY timestamp DESC
            LIMIT 5
        """)

        results = cursor.fetchall()

        for row in results:
            (game_id, timestamp, winning_seat, brightness, title,
             a_combo, b_combo, c_combo, a_pot, b_pot, c_pot, middle_text) = row

            timestamp = timestamp[:19]
            title = title[:20] if title else 'N/A'
            middle_text = middle_text[:30] if middle_text else 'N/A'

            print(f"\nGame {game_id:3d} | {timestamp} | Winner: {winning_seat or 'None':1s} (Brightness: {brightness or 0:.1f})")
            print(f"         Title: {title}")
            print(f"         A: {a_combo[:20]:20s} | Pot: {a_pot[:10]:10s}")
            print(f"         B: {b_combo[:20]:20s} | Pot: {b_pot[:10]:10s}")
            print(f"         C: {c_combo[:20]:20s} | Pot: {c_pot[:10]:10s}")
            print(f"         Middle: {middle_text}")

        # Show card details for latest game
        if results:
            latest_game_id = results[0][0]
            print(f"\n🃏 CARD DETAILS FOR LATEST GAME (ID: {latest_game_id}):")
            print("-" * 60)

            cursor.execute("""
                SELECT seat, card_position, card_number, card_suit, ocr_text, confidence
                FROM cards
                WHERE game_id = ?
                ORDER BY seat, card_position
            """, (latest_game_id,))

            card_results = cursor.fetchall()

            current_seat = None
            for seat, pos, number, suit, ocr_text, conf in card_results:
                if seat != current_seat:
                    print(f"\n🎯 Seat {seat}:")
                    current_seat = seat

                print(f"   Card {pos}: {ocr_text} (Number: {number}, Suit: {suit}, Conf: {conf:.2f})")

        conn.close()


def main():
    """Main function for advanced OCR capture"""
    print("🚀 ADVANCED OCR POKER CAPTURE SYSTEM")
    print("=" * 50)
    print("🎯 Enhanced Features:")
    print("   ✅ Brightness-based winning seat detection")
    print("   ✅ OCR extraction of card numbers and suits")
    print("   ✅ Game title and combination text extraction")
    print("   ✅ Pot amount extraction")
    print("   ✅ Comprehensive database with individual card records")
    print("   ✅ Automatic processing of all images")
    print()

    # Initialize advanced capture system
    model_path = "models/capture.pt"
    capture = AdvancedOCRCapture(model_path)

    if not capture.model:
        print("❌ Cannot proceed without model")
        return

    # Check if images folder exists
    if not os.path.exists("images"):
        print("❌ Images folder not found")
        return

    # Get image count
    image_files = [f for f in os.listdir("images") if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
    print(f"📸 Found {len(image_files)} images to process")

    if len(image_files) == 0:
        print("❌ No images found in images folder")
        return

    print(f"\n🚀 Starting advanced OCR processing of {len(image_files)} images...")
    print("🔄 This will run automatically with detailed OCR analysis")
    print("=" * 70)

    # Start automatic advanced processing
    successful = capture.process_all_images_advanced()

    print(f"\n🎉 ADVANCED OCR PROCESSING COMPLETE!")
    print(f"✅ Successfully processed: {successful} images")
    print(f"📊 Advanced database now contains detailed records for {successful} games")


if __name__ == "__main__":
    main()
