import os
import sys
import cv2
import numpy as np
import sqlite3
import pandas as pd
import time
import datetime
import queue
import joblib
import threading
import random
import math
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QPoint, QSize
from PyQt5.QtGui import QIcon, QPixmap, QColor, QPainter, QPen, QLinearGradient, QBrush, QFont, QPainterPath
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QPushButton, QComboBox, QRadioButton, QButtonGroup,
                            QMessageBox, QInputDialog, QFrame, QScrollArea, QGraphicsOpacityEffect,
                            QGraphicsDropShadowEffect, QSizePolicy)
from qfluentwidgets import (NavigationInterface, NavigationItemPosition, MessageBox,
                           FluentIcon, setTheme, Theme, PushButton, ComboBox,
                           RadioButton, BodyLabel, TitleLabel, CaptionLabel,
                           CardWidget, ToolTipFilter, ToolTipPosition,
                           InfoBar, InfoBarPosition, StateToolTip, SwitchButton,
                           ProgressBar, ProgressRing, FluentStyleSheet, isDarkTheme)
from qfluentwidgets import FluentWindow, SubtitleLabel, setFont, setThemeColor, TransparentToolButton

from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Global constants
DB_PATH = "game_data.db"
PATTERN_PATH = "pattern_memory.joblib"
CROP_BOX = (970, 388, 1350, 632)  # Screenshot crop area
UPDATE_INTERVAL = 60000  # Update frequency in milliseconds (1 minute)
DELAY_BETWEEN_CAPTURES = 56  # Seconds between captures

# Custom color scheme
COLORS = {
    'primary': QColor(0, 120, 212),      # Microsoft blue
    'secondary': QColor(0, 99, 177),     # Darker blue
    'accent': QColor(255, 185, 0),       # Gold
    'success': QColor(16, 124, 16),      # Green
    'warning': QColor(197, 134, 7),      # Orange
    'error': QColor(232, 17, 35),        # Red
    'card_a': QColor(0, 120, 212),       # Blue for seat A
    'card_b': QColor(16, 124, 16),       # Green for seat B
    'card_c': QColor(232, 17, 35),       # Red for seat C
}

# Custom animated widgets
class AnimatedCard(CardWidget):
    """A card widget with animation effects"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupAnimation()

    def setupAnimation(self):
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)

        # Prepare for hover animation
        self.setProperty('hovered', False)
        self.installEventFilter(self)

    def eventFilter(self, obj, event):
        if event.type() == event.Enter:
            self.onHoverEnter()
        elif event.type() == event.Leave:
            self.onHoverLeave()
        return super().eventFilter(obj, event)

    def onHoverEnter(self):
        # Create animation for shadow
        shadow = self.graphicsEffect()
        self.shadowAnim = QPropertyAnimation(shadow, b"blurRadius")
        self.shadowAnim.setDuration(200)
        self.shadowAnim.setStartValue(15)
        self.shadowAnim.setEndValue(25)
        self.shadowAnim.setEasingCurve(QEasingCurve.OutCubic)
        self.shadowAnim.start()

        # Create animation for card size
        self.scaleAnim = QPropertyAnimation(self, b"geometry")
        self.scaleAnim.setDuration(200)
        rect = self.geometry()
        self.scaleAnim.setStartValue(rect)
        self.scaleAnim.setEndValue(rect.adjusted(-2, -2, 2, 2))
        self.scaleAnim.setEasingCurve(QEasingCurve.OutCubic)
        self.scaleAnim.start()

    def onHoverLeave(self):
        # Create animation for shadow
        shadow = self.graphicsEffect()
        self.shadowAnim = QPropertyAnimation(shadow, b"blurRadius")
        self.shadowAnim.setDuration(200)
        self.shadowAnim.setStartValue(25)
        self.shadowAnim.setEndValue(15)
        self.shadowAnim.setEasingCurve(QEasingCurve.OutCubic)
        self.shadowAnim.start()

        # Create animation for card size
        self.scaleAnim = QPropertyAnimation(self, b"geometry")
        self.scaleAnim.setDuration(200)
        rect = self.geometry()
        self.scaleAnim.setStartValue(rect)
        self.scaleAnim.setEndValue(rect.adjusted(2, 2, -2, -2))
        self.scaleAnim.setEasingCurve(QEasingCurve.OutCubic)
        self.scaleAnim.start()

class AnimatedButton(PushButton):
    """A button with animation effects"""

    def __init__(self, text="", parent=None, icon=None):
        if icon:
            super().__init__(icon, text, parent)
        else:
            super().__init__(text, parent)
        self.setupAnimation()

    def setupAnimation(self):
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)

        # Prepare for hover and click animations
        self.setProperty('hovered', False)
        self.setProperty('pressed', False)
        self.installEventFilter(self)

    def eventFilter(self, obj, event):
        if event.type() == event.Enter:
            self.onHoverEnter()
        elif event.type() == event.Leave:
            self.onHoverLeave()
        elif event.type() == event.MouseButtonPress:
            self.onPress()
        elif event.type() == event.MouseButtonRelease:
            self.onRelease()
        return super().eventFilter(obj, event)

    def onHoverEnter(self):
        # Create animation for shadow
        shadow = self.graphicsEffect()
        self.shadowAnim = QPropertyAnimation(shadow, b"blurRadius")
        self.shadowAnim.setDuration(150)
        self.shadowAnim.setStartValue(10)
        self.shadowAnim.setEndValue(15)
        self.shadowAnim.setEasingCurve(QEasingCurve.OutCubic)
        self.shadowAnim.start()

    def onHoverLeave(self):
        # Create animation for shadow
        shadow = self.graphicsEffect()
        self.shadowAnim = QPropertyAnimation(shadow, b"blurRadius")
        self.shadowAnim.setDuration(150)
        self.shadowAnim.setStartValue(15)
        self.shadowAnim.setEndValue(10)
        self.shadowAnim.setEasingCurve(QEasingCurve.OutCubic)
        self.shadowAnim.start()

    def onPress(self):
        # Create animation for button press
        self.pressAnim = QPropertyAnimation(self, b"pos")
        self.pressAnim.setDuration(100)
        pos = self.pos()
        self.pressAnim.setStartValue(pos)
        self.pressAnim.setEndValue(QPoint(pos.x(), pos.y() + 2))
        self.pressAnim.setEasingCurve(QEasingCurve.OutCubic)
        self.pressAnim.start()

    def onRelease(self):
        # Create animation for button release
        self.releaseAnim = QPropertyAnimation(self, b"pos")
        self.releaseAnim.setDuration(100)
        pos = self.pos()
        self.releaseAnim.setStartValue(pos)
        self.releaseAnim.setEndValue(QPoint(pos.x(), pos.y() - 2))
        self.releaseAnim.setEasingCurve(QEasingCurve.OutCubic)
        self.releaseAnim.start()

class PredictionVisualizer(QWidget):
    """Custom widget for visualizing prediction patterns"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.data = []
        self.setMinimumHeight(300)

        # Set background color based on theme
        self.setStyleSheet("""
            background-color: """ + (
                "#FFFFFF" if not isDarkTheme() else "#2D2D2D"
            ) + """;
            border-radius: 8px;
        """)

        # Add shadow effect
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)

        # Setup animation
        self.animation_timer = QTimer(self)
        self.animation_timer.timeout.connect(self.update_animation)
        self.animation_timer.start(50)
        self.animation_offset = 0
        self.highlight_index = -1

    def setData(self, data):
        """Set the data to visualize"""
        self.data = data
        self.update()

    def update_animation(self):
        """Update animation state"""
        self.animation_offset += 1
        if self.animation_offset > 360:
            self.animation_offset = 0

        # Cycle through highlighting different patterns
        if self.animation_offset % 120 == 0:
            self.highlight_index = (self.highlight_index + 1) % 3

        self.update()

    def paintEvent(self, event):
        """Paint the visualization"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw background
        painter.fillRect(self.rect(), QColor(
            "#FFFFFF" if not isDarkTheme() else "#2D2D2D"
        ))

        if not self.data:
            # Draw placeholder text
            painter.setPen(QColor("#888888"))
            painter.setFont(QFont("Segoe UI", 12))
            painter.drawText(self.rect(), Qt.AlignCenter, "No data available for visualization")
            return

        # Constants for drawing
        circle_radius = 15
        h_spacing = 60
        v_spacing = 40
        margin_x = 80
        margin_y = 60

        # Draw grid
        for i, row in enumerate(self.data):
            if i >= 20:  # Show only last 20 entries
                break

            y = margin_y + i * v_spacing

            # Draw row label (bet number)
            painter.setPen(QColor(
                "#666666" if not isDarkTheme() else "#AAAAAA"
            ))
            painter.drawText(margin_x - 40, y + 5, str(len(self.data) - i))

            # Draw circles for each seat
            for j, seat in enumerate(['A', 'B', 'C']):
                x = margin_x + j * h_spacing

                # Draw seat label on first row
                if i == 0:
                    # Use seat colors for labels
                    seat_color = COLORS.get(f'card_{seat.lower()}', QColor(100, 100, 100))
                    painter.setPen(seat_color)
                    painter.setFont(QFont("Segoe UI", 12, QFont.Bold))
                    painter.drawText(x - 8, margin_y - 25, seat)

                # Draw circle
                if row.get(seat) == "Win":
                    # Draw filled circle with seat color
                    seat_color = COLORS.get(f'card_{seat.lower()}', QColor(100, 100, 100))

                    # Add animation effect to winning cells
                    if self.animation_timer.isActive():
                        # Create pulsing effect
                        pulse = abs(math.sin(math.radians(self.animation_offset + i * 10 + j * 30))) * 0.3 + 0.7
                        seat_color = QColor(
                            min(255, int(seat_color.red() * pulse)),
                            min(255, int(seat_color.green() * pulse)),
                            min(255, int(seat_color.blue() * pulse)),
                            seat_color.alpha()
                        )

                    # Draw glow effect
                    glow = QPainterPath()
                    glow.addEllipse(x - circle_radius - 3, y - circle_radius - 3,
                                  (circle_radius + 3) * 2, (circle_radius + 3) * 2)
                    glow_gradient = QRadialGradient(x, y, circle_radius * 2)
                    glow_gradient.setColorAt(0, QColor(seat_color.red(), seat_color.green(), seat_color.blue(), 100))
                    glow_gradient.setColorAt(1, QColor(seat_color.red(), seat_color.green(), seat_color.blue(), 0))
                    painter.fillPath(glow, glow_gradient)

                    # Draw filled circle
                    painter.setBrush(QBrush(seat_color))
                    painter.setPen(Qt.NoPen)
                    painter.drawEllipse(x - circle_radius, y - circle_radius,
                                      circle_radius * 2, circle_radius * 2)

                    # Draw white text
                    painter.setPen(QColor(255, 255, 255))
                    painter.setFont(QFont("Segoe UI", 9, QFont.Bold))
                    painter.drawText(x - 5, y + 5, "W")
                else:
                    # Draw empty circle
                    painter.setBrush(Qt.NoBrush)
                    painter.setPen(QPen(QColor(
                        "#CCCCCC" if not isDarkTheme() else "#555555"
                    ), 2))
                    painter.drawEllipse(x - circle_radius, y - circle_radius,
                                      circle_radius * 2, circle_radius * 2)

        # Draw connecting lines for patterns
        if len(self.data) >= 3:
            # Draw regular connecting lines
            painter.setPen(QPen(QColor(
                "#AAAAAA" if not isDarkTheme() else "#666666"
            ), 1.5, Qt.DashLine))

            for i in range(len(self.data) - 1):
                if i >= 19:  # Stay within the 20 visible entries
                    break

                # Find winning seats
                win_seat1 = next((seat for seat, val in self.data[i].items() if val == "Win"), None)
                win_seat2 = next((seat for seat, val in self.data[i+1].items() if val == "Win"), None)

                if win_seat1 and win_seat2:
                    # Calculate positions
                    x1 = margin_x + ['A', 'B', 'C'].index(win_seat1) * h_spacing
                    y1 = margin_y + i * v_spacing
                    x2 = margin_x + ['A', 'B', 'C'].index(win_seat2) * h_spacing
                    y2 = margin_y + (i+1) * v_spacing

                    # Draw line connecting wins
                    painter.drawLine(x1, y1, x2, y2)

            # Draw special patterns with animation
            for i in range(len(self.data) - 2):
                if i >= 18:  # Stay within visible entries
                    break

                # Check for diagonal A -> B -> C
                if (self.data[i].get('A') == 'Win' and
                    self.data[i+1].get('B') == 'Win' and
                    self.data[i+2].get('C') == 'Win'):

                    # Highlight this pattern if it's the current highlight
                    if self.highlight_index == 0:
                        # Draw animated diagonal line
                        painter.setPen(QPen(COLORS['primary'], 3, Qt.SolidLine))

                        # Calculate positions
                        x1 = margin_x + 0 * h_spacing  # A
                        y1 = margin_y + i * v_spacing
                        x2 = margin_x + 1 * h_spacing  # B
                        y2 = margin_y + (i+1) * v_spacing
                        x3 = margin_x + 2 * h_spacing  # C
                        y3 = margin_y + (i+2) * v_spacing

                        # Draw animated path
                        path = QPainterPath()
                        path.moveTo(x1, y1)
                        path.lineTo(x2, y2)
                        path.lineTo(x3, y3)

                        # Draw glow effect
                        glow_pen = QPen(COLORS['primary'])
                        glow_pen.setWidth(6)
                        glow_pen.setColor(QColor(COLORS['primary'].red(),
                                                COLORS['primary'].green(),
                                                COLORS['primary'].blue(),
                                                100))
                        painter.setPen(glow_pen)
                        painter.drawPath(path)

                        # Draw main line
                        main_pen = QPen(COLORS['primary'])
                        main_pen.setWidth(3)
                        painter.setPen(main_pen)
                        painter.drawPath(path)

                        # Draw "ABC" label
                        label_x = (x1 + x3) / 2
                        label_y = (y1 + y3) / 2 - 15
                        painter.setPen(COLORS['primary'])
                        painter.setFont(QFont("Segoe UI", 9, QFont.Bold))
                        painter.drawText(QRectF(label_x - 20, label_y - 10, 40, 20),
                                       Qt.AlignCenter, "A→B→C")

                # Check for diagonal C -> B -> A
                if (self.data[i].get('C') == 'Win' and
                    self.data[i+1].get('B') == 'Win' and
                    self.data[i+2].get('A') == 'Win'):

                    # Highlight this pattern if it's the current highlight
                    if self.highlight_index == 1:
                        # Draw animated diagonal line
                        painter.setPen(QPen(COLORS['accent'], 3, Qt.SolidLine))

                        # Calculate positions
                        x1 = margin_x + 2 * h_spacing  # C
                        y1 = margin_y + i * v_spacing
                        x2 = margin_x + 1 * h_spacing  # B
                        y2 = margin_y + (i+1) * v_spacing
                        x3 = margin_x + 0 * h_spacing  # A
                        y3 = margin_y + (i+2) * v_spacing

                        # Draw animated path
                        path = QPainterPath()
                        path.moveTo(x1, y1)
                        path.lineTo(x2, y2)
                        path.lineTo(x3, y3)

                        # Draw glow effect
                        glow_pen = QPen(COLORS['accent'])
                        glow_pen.setWidth(6)
                        glow_pen.setColor(QColor(COLORS['accent'].red(),
                                                COLORS['accent'].green(),
                                                COLORS['accent'].blue(),
                                                100))
                        painter.setPen(glow_pen)
                        painter.drawPath(path)

                        # Draw main line
                        main_pen = QPen(COLORS['accent'])
                        main_pen.setWidth(3)
                        painter.setPen(main_pen)
                        painter.drawPath(path)

                        # Draw "CBA" label
                        label_x = (x1 + x3) / 2
                        label_y = (y1 + y3) / 2 - 15
                        painter.setPen(COLORS['accent'])
                        painter.setFont(QFont("Segoe UI", 9, QFont.Bold))
                        painter.drawText(QRectF(label_x - 20, label_y - 10, 40, 20),
                                       Qt.AlignCenter, "C→B→A")

                # Check for repeat pattern (e.g., A -> A -> A)
                win_seat1 = next((seat for seat, val in self.data[i].items() if val == "Win"), None)
                win_seat2 = next((seat for seat, val in self.data[i+1].items() if val == "Win"), None)
                win_seat3 = next((seat for seat, val in self.data[i+2].items() if val == "Win"), None)

                if win_seat1 and win_seat1 == win_seat2 and win_seat2 == win_seat3:
                    # Highlight this pattern if it's the current highlight
                    if self.highlight_index == 2:
                        # Get seat color
                        seat_color = COLORS.get(f'card_{win_seat1.lower()}', QColor(100, 100, 100))

                        # Calculate positions
                        col_index = ['A', 'B', 'C'].index(win_seat1)
                        x1 = margin_x + col_index * h_spacing
                        y1 = margin_y + i * v_spacing
                        x2 = margin_x + col_index * h_spacing
                        y2 = margin_y + (i+1) * v_spacing
                        x3 = margin_x + col_index * h_spacing
                        y3 = margin_y + (i+2) * v_spacing

                        # Draw connecting path
                        path = QPainterPath()
                        path.moveTo(x1, y1)
                        path.lineTo(x2, y2)
                        path.lineTo(x3, y3)

                        # Draw glow effect
                        glow_pen = QPen(seat_color)
                        glow_pen.setWidth(6)
                        glow_pen.setColor(QColor(seat_color.red(),
                                                seat_color.green(),
                                                seat_color.blue(),
                                                100))
                        painter.setPen(glow_pen)
                        painter.drawPath(path)

                        # Draw main line
                        main_pen = QPen(seat_color)
                        main_pen.setWidth(3)
                        painter.setPen(main_pen)
                        painter.drawPath(path)

                        # Draw repeat label
                        label_x = x1 + 20
                        label_y = y2
                        painter.setPen(seat_color)
                        painter.setFont(QFont("Segoe UI", 9, QFont.Bold))
                        painter.drawText(QRectF(label_x - 10, label_y - 10, 60, 20),
                                       Qt.AlignLeft, f"{win_seat1} Repeat")

class PredictionThread(QThread):
    """Thread for running predictions without freezing the UI"""
    finished = pyqtSignal(str, str, float)
    progress = pyqtSignal(str)
    error = pyqtSignal(str)

    def __init__(self, parent=None, train_func=None, predict_func=None):
        super().__init__(parent)
        self.train_func = train_func
        self.predict_func = predict_func

    def run(self):
        try:
            self.progress.emit("Training models...")
            success = self.train_func()

            if success:
                self.progress.emit("Making prediction...")
                primary, secondary, confidence = self.predict_func()
                self.finished.emit(primary, secondary, confidence)
            else:
                self.error.emit("Training failed - not enough data")
        except Exception as e:
            self.error.emit(f"Error: {str(e)}")

class PolitePredict(FluentWindow):
    def __init__(self):
        super().__init__()

        # Set theme
        setTheme(Theme.AUTO)
        setThemeColor(COLORS['primary'])  # Use our custom primary color

        # Initialize variables
        self.setup_database()
        self.pattern_memory = self.load_pattern_memory()
        self.data_queue = queue.Queue()
        self.selected_cards = []
        self.winning_seat = None
        self.selected_trend = None
        self.primary_prediction = "(Auto after 60 entries)"
        self.secondary_prediction = "(Auto after 60 entries)"
        self.prediction_accuracy = "(%)"
        self.current_session_count = 0
        self.animation_in_progress = False

        # Setup UI
        self.setup_ui()

        # Apply custom stylesheet
        self.apply_custom_stylesheet()

        # Start data processing thread
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.process_data_queue)
        self.timer.start(100)

        # Schedule updates
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.scheduled_update)
        self.update_timer.start(UPDATE_INTERVAL)

        # Show welcome animation
        QTimer.singleShot(500, self.show_welcome_animation)

    def apply_custom_stylesheet(self):
        """Apply custom stylesheet to enhance the UI"""
        # Apply fluent stylesheet
        self.setStyleSheet(FluentStyleSheet.MENU + """
            QWidget {
                font-family: 'Segoe UI', Arial, sans-serif;
            }

            TitleLabel {
                font-size: 24px;
                font-weight: bold;
                color: """ + (
                    "#333333" if not isDarkTheme() else "#FFFFFF"
                ) + """;
            }

            SubtitleLabel {
                font-size: 16px;
                font-weight: bold;
                color: """ + (
                    "#555555" if not isDarkTheme() else "#DDDDDD"
                ) + """;
            }

            BodyLabel {
                font-size: 14px;
                color: """ + (
                    "#666666" if not isDarkTheme() else "#BBBBBB"
                ) + """;
            }

            CardWidget {
                border-radius: 8px;
                background-color: """ + (
                    "#FFFFFF" if not isDarkTheme() else "#2D2D2D"
                ) + """;
                border: 1px solid """ + (
                    "#DDDDDD" if not isDarkTheme() else "#3D3D3D"
                ) + """;
            }

            PushButton {
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }

            ComboBox {
                border-radius: 4px;
                padding: 6px;
            }
        """)

    def show_welcome_animation(self):
        """Show a welcome animation when the app starts"""
        if self.animation_in_progress:
            return

        self.animation_in_progress = True

        # Create a semi-transparent overlay
        self.overlay = QWidget(self)
        self.overlay.setGeometry(self.rect())
        self.overlay.setStyleSheet("background-color: rgba(0, 0, 0, 150);")

        # Create welcome message
        self.welcome_card = QWidget(self.overlay)
        self.welcome_card.setStyleSheet("""
            background-color: """ + (
                "#FFFFFF" if not isDarkTheme() else "#2D2D2D"
            ) + """;
            border-radius: 12px;
            padding: 20px;
        """)

        welcome_layout = QVBoxLayout(self.welcome_card)

        # Add logo/title
        title = TitleLabel("PolitePredict")
        title.setAlignment(Qt.AlignCenter)
        welcome_layout.addWidget(title)

        # Add subtitle
        subtitle = SubtitleLabel("Advanced Card Game Analyzer")
        subtitle.setAlignment(Qt.AlignCenter)
        welcome_layout.addWidget(subtitle)

        # Add description
        description = BodyLabel("Welcome to PolitePredict, your AI-powered assistant for card game predictions. "
                              "This application uses machine learning to analyze patterns and predict winning seats.")
        description.setAlignment(Qt.AlignCenter)
        description.setWordWrap(True)
        welcome_layout.addWidget(description)

        # Add start button
        start_btn = AnimatedButton("Get Started")
        start_btn.setFixedWidth(200)
        start_btn.clicked.connect(self.close_welcome_animation)

        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        btn_layout.addWidget(start_btn)
        btn_layout.addStretch()
        welcome_layout.addLayout(btn_layout)

        # Position the welcome card
        self.welcome_card.setFixedSize(500, 300)
        self.welcome_card.move(
            (self.width() - self.welcome_card.width()) // 2,
            (self.height() - self.welcome_card.height()) // 2
        )

        # Show the overlay
        self.overlay.show()

        # Create fade-in animation for overlay
        self.overlay_anim = QPropertyAnimation(self.overlay, b"windowOpacity")
        self.overlay_anim.setDuration(500)
        self.overlay_anim.setStartValue(0)
        self.overlay_anim.setEndValue(1)
        self.overlay_anim.setEasingCurve(QEasingCurve.InOutQuad)

        # Create slide-in animation for welcome card
        self.card_anim = QPropertyAnimation(self.welcome_card, b"pos")
        self.card_anim.setDuration(800)
        start_pos = QPoint(
            (self.width() - self.welcome_card.width()) // 2,
            -self.welcome_card.height()
        )
        end_pos = QPoint(
            (self.width() - self.welcome_card.width()) // 2,
            (self.height() - self.welcome_card.height()) // 2
        )
        self.card_anim.setStartValue(start_pos)
        self.card_anim.setEndValue(end_pos)
        self.card_anim.setEasingCurve(QEasingCurve.OutBack)

        # Start animations
        self.overlay_anim.start()
        self.card_anim.start()

    def close_welcome_animation(self):
        """Close the welcome animation"""
        # Create fade-out animation for overlay
        self.overlay_anim = QPropertyAnimation(self.overlay, b"windowOpacity")
        self.overlay_anim.setDuration(500)
        self.overlay_anim.setStartValue(1)
        self.overlay_anim.setEndValue(0)
        self.overlay_anim.setEasingCurve(QEasingCurve.InOutQuad)

        # Create slide-out animation for welcome card
        self.card_anim = QPropertyAnimation(self.welcome_card, b"pos")
        self.card_anim.setDuration(500)
        start_pos = self.welcome_card.pos()
        end_pos = QPoint(
            (self.width() - self.welcome_card.width()) // 2,
            self.height() + 100
        )
        self.card_anim.setStartValue(start_pos)
        self.card_anim.setEndValue(end_pos)
        self.card_anim.setEasingCurve(QEasingCurve.InOutQuad)

        # Connect finished signal to cleanup
        self.overlay_anim.finished.connect(self.cleanup_welcome_animation)

        # Start animations
        self.overlay_anim.start()
        self.card_anim.start()

    def cleanup_welcome_animation(self):
        """Clean up welcome animation resources"""
        if hasattr(self, 'overlay'):
            self.overlay.deleteLater()
        self.animation_in_progress = False

    def setup_database(self):
        """Create database and tables if they don't exist"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Create winfail table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS winfail (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            A TEXT,
            B TEXT,
            C TEXT,
            timestamp TEXT
        )
        ''')

        # Create seat_changing table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS seat_changing (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            A TEXT,
            B TEXT,
            C TEXT,
            timestamp TEXT
        )
        ''')

        # Create backend_data table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS backend_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            winning_seat TEXT,
            card1 TEXT,
            card2 TEXT,
            card3 TEXT,
            color1 TEXT,
            color2 TEXT,
            color3 TEXT,
            trend TEXT,
            timestamp TEXT,
            session_id INTEGER
        )
        ''')

        conn.commit()
        conn.close()
        print("Database setup complete")

    def load_pattern_memory(self):
        """Load saved pattern memory if exists"""
        if os.path.exists(PATTERN_PATH):
            try:
                return joblib.load(PATTERN_PATH)
            except Exception as e:
                print(f"Error loading pattern memory: {str(e)}")

        # Initialize new pattern memory
        return {
            'repeat_patterns': {},
            'block_patterns': {},
            'singles_patterns': {},
            'card_influence': {},
            'visual_patterns': {},
            'seat_change_patterns': {},
            'prediction_accuracy': {
                'overall': [],
                'by_pattern': {}
            },
            'prediction_log': []
        }

    def save_pattern_memory(self):
        """Save pattern memory to disk"""
        try:
            joblib.dump(self.pattern_memory, PATTERN_PATH)
            print(f"Pattern memory saved")
        except Exception as e:
            print(f"Error saving pattern memory: {str(e)}")

    def setup_ui(self):
        """Set up the main UI"""
        # Set window properties
        self.resize(1200, 800)
        self.setWindowTitle("PolitePredict - Card Game Analyzer")
        # Use QIcon instead of FluentIcon for window icon
        self.setWindowIcon(QIcon())

        # Create navigation interface
        self.navigation = NavigationInterface(self, showMenuButton=True)
        self.navigation.setExpandWidth(200)

        # Create pages
        self.dataEntryPage = QWidget()
        self.dataEntryPage.setObjectName("dataEntryPage")

        self.predictionsPage = QWidget()
        self.predictionsPage.setObjectName("predictionsPage")

        self.visualPatternsPage = QWidget()
        self.visualPatternsPage.setObjectName("visualPatternsPage")

        self.settingsPage = QWidget()
        self.settingsPage.setObjectName("settingsPage")

        # Add pages to navigation
        # Use icons that are definitely available in the library (verified from list_icons.py)
        self.addSubInterface(self.dataEntryPage, FluentIcon.EDIT, "Data Entry")
        self.addSubInterface(self.predictionsPage, FluentIcon.LIBRARY, "Predictions")
        self.addSubInterface(self.visualPatternsPage, FluentIcon.VIEW, "Visual Patterns")
        self.addSubInterface(self.settingsPage, FluentIcon.SETTING, "Settings", NavigationItemPosition.BOTTOM)

        # Setup each page
        self.setup_data_entry_page()
        self.setup_predictions_page()
        self.setup_visual_patterns_page()
        self.setup_settings_page()

        # Set default page
        self.navigation.setCurrentItem(self.dataEntryPage.objectName())

    def setup_data_entry_page(self):
        """Set up the data entry page"""
        layout = QVBoxLayout(self.dataEntryPage)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Header with title and animation
        header_layout = QHBoxLayout()

        # Title with animation
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)

        title = TitleLabel("Data Entry")
        title_layout.addWidget(title)

        # Animated subtitle
        subtitle = BodyLabel("Enter game data to train the prediction model")
        subtitle.setStyleSheet("color: " + COLORS['secondary'].name() + ";")
        title_layout.addWidget(subtitle)

        header_layout.addWidget(title_container)
        header_layout.addStretch()

        # Add session counter with progress ring
        session_container = QWidget()
        session_layout = QHBoxLayout(session_container)
        session_layout.setContentsMargins(0, 0, 0, 0)

        self.progress_ring = ProgressRing(self)
        self.progress_ring.setFixedSize(60, 60)
        self.progress_ring.setValue(0)  # Will be updated based on session count
        session_layout.addWidget(self.progress_ring)

        session_text = QWidget()
        session_text_layout = QVBoxLayout(session_text)
        session_text_layout.setContentsMargins(0, 0, 0, 0)

        self.session_label = BodyLabel(f"Session Count: {self.current_session_count}")
        session_text_layout.addWidget(self.session_label)

        self.progress_label = CaptionLabel("0/60 entries")
        session_text_layout.addWidget(self.progress_label)

        session_layout.addWidget(session_text)
        header_layout.addWidget(session_container)

        layout.addLayout(header_layout)

        # Card entry section with animated card
        card_widget = AnimatedCard()
        card_layout = QVBoxLayout(card_widget)

        # Winning seat selection with colored indicators
        seat_layout = QHBoxLayout()
        seat_label = SubtitleLabel("Winning Seat:")
        seat_layout.addWidget(seat_label)

        self.seat_group = QButtonGroup(self)

        # Create a container for the seat buttons with a nice layout
        seat_container = QWidget()
        seat_container.setStyleSheet("""
            QWidget {
                background-color: """ + (
                    "#F5F5F5" if not isDarkTheme() else "#333333"
                ) + """;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        seat_buttons_layout = QHBoxLayout(seat_container)
        seat_buttons_layout.setContentsMargins(10, 5, 10, 5)
        seat_buttons_layout.setSpacing(15)

        # Create stylish radio buttons for each seat
        for seat in ['A', 'B', 'C']:
            radio = RadioButton(seat)

            # Set different colors for each seat
            if seat == 'A':
                color = COLORS['card_a'].name()
            elif seat == 'B':
                color = COLORS['card_b'].name()
            else:
                color = COLORS['card_c'].name()

            radio.setStyleSheet(f"""
                RadioButton:checked {{
                    color: {color};
                }}
            """)

            self.seat_group.addButton(radio)
            seat_buttons_layout.addWidget(radio)

            if seat == 'A':  # Set default
                radio.setChecked(True)
                self.winning_seat = 'A'

        self.seat_group.buttonClicked.connect(self.on_seat_selected)
        seat_layout.addWidget(seat_container)
        seat_layout.addStretch()
        card_layout.addLayout(seat_layout)

        # Card selection with visual card representation
        card_section_title = SubtitleLabel("Cards:")
        card_layout.addWidget(card_section_title)

        self.card_values = []
        self.card_suits = []
        self.card_colors = []
        self.card_displays = []

        # Create a container for the cards with a nice layout
        cards_container = QWidget()
        cards_container.setStyleSheet("""
            QWidget {
                background-color: """ + (
                    "#F5F5F5" if not isDarkTheme() else "#333333"
                ) + """;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        cards_grid = QVBoxLayout(cards_container)
        cards_grid.setContentsMargins(10, 10, 10, 10)
        cards_grid.setSpacing(15)

        for i in range(3):
            card_row = QHBoxLayout()

            # Card number label
            card_label = BodyLabel(f"Card {i+1}:")
            card_label.setFixedWidth(60)
            card_row.addWidget(card_label)

            # Card value dropdown
            value_combo = ComboBox()
            value_combo.addItems(['', '2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'])
            value_combo.setCurrentIndex(0)
            value_combo.setPlaceholderText(f"Value")
            value_combo.currentIndexChanged.connect(lambda idx, i=i: self.update_card_display(i))
            self.card_values.append(value_combo)
            card_row.addWidget(value_combo)

            # Card suit dropdown
            suit_combo = ComboBox()
            suit_combo.addItems(['', '♠ (Hukam)', '♣ (Chirya)', '♥ (Paan)', '♦ (Eant)'])
            suit_combo.setCurrentIndex(0)
            suit_combo.setPlaceholderText(f"Suit")
            suit_combo.currentIndexChanged.connect(lambda idx, combo=suit_combo, i=i: self.update_card_color(combo, i))
            suit_combo.currentIndexChanged.connect(lambda idx, i=i: self.update_card_display(i))
            self.card_suits.append(suit_combo)
            card_row.addWidget(suit_combo)

            # Visual card display
            card_display = QLabel()
            card_display.setFixedSize(40, 60)
            card_display.setStyleSheet("""
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 2px;
            """)
            card_display.setAlignment(Qt.AlignCenter)
            self.card_displays.append(card_display)
            card_row.addWidget(card_display)

            # Color indicator (hidden but kept for compatibility)
            color_label = QLabel()
            color_label.setFixedSize(0, 0)
            color_label.setStyleSheet("background-color: transparent;")
            self.card_colors.append(color_label)

            cards_grid.addLayout(card_row)

        card_layout.addWidget(cards_container)

        # Trend selection with visual indicator
        trend_section = QWidget()
        trend_section.setStyleSheet("""
            QWidget {
                background-color: """ + (
                    "#F5F5F5" if not isDarkTheme() else "#333333"
                ) + """;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        trend_layout = QHBoxLayout(trend_section)
        trend_layout.setContentsMargins(10, 5, 10, 5)

        trend_label = SubtitleLabel("Trend:")
        trend_layout.addWidget(trend_label)

        self.trend_combo = ComboBox()
        self.trend_combo.addItems(['', 'High Card', 'Pair', 'Sequence', 'Colour', 'Pure Sequence', 'Tilt'])
        self.trend_combo.setCurrentIndex(0)
        self.trend_combo.setPlaceholderText("Select Trend")
        self.trend_combo.currentIndexChanged.connect(self.on_trend_selected)
        trend_layout.addWidget(self.trend_combo)

        # Visual trend indicator
        self.trend_indicator = QLabel()
        self.trend_indicator.setFixedSize(24, 24)
        self.trend_indicator.setStyleSheet("background-color: transparent;")
        trend_layout.addWidget(self.trend_indicator)

        trend_layout.addStretch()
        card_layout.addWidget(trend_section)

        # Buttons with animations
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(15)

        save_btn = AnimatedButton("Save Data", icon=FluentIcon.SAVE)
        save_btn.clicked.connect(self.save_data)
        button_layout.addWidget(save_btn)

        clear_btn = AnimatedButton("Clear", icon=FluentIcon.DELETE)
        clear_btn.clicked.connect(self.clear_input)
        button_layout.addWidget(clear_btn)

        capture_btn = AnimatedButton("Capture Screenshot", icon=FluentIcon.CAMERA)
        capture_btn.clicked.connect(self.capture_and_process)
        button_layout.addWidget(capture_btn)

        card_layout.addLayout(button_layout)
        layout.addWidget(card_widget)

        # Action buttons
        action_layout = QHBoxLayout()
        action_layout.setContentsMargins(0, 10, 0, 0)
        action_layout.setSpacing(15)

        train_btn = AnimatedButton("Train Now", icon=FluentIcon.PLAY)
        train_btn.setStyleSheet(f"""
            AnimatedButton {{
                background-color: {COLORS['success'].name()};
                color: white;
            }}
        """)
        train_btn.clicked.connect(self.train_and_predict)
        action_layout.addWidget(train_btn)

        new_session_btn = AnimatedButton("New Session", icon=FluentIcon.ADD)
        new_session_btn.clicked.connect(self.new_session)
        action_layout.addWidget(new_session_btn)

        layout.addLayout(action_layout)
        layout.addStretch()

    def update_card_display(self, card_index):
        """Update the visual display of a card"""
        value = self.card_values[card_index].currentText()
        suit_text = self.card_suits[card_index].currentText()

        if not value or not suit_text:
            self.card_displays[card_index].setText("")
            self.card_displays[card_index].setStyleSheet("""
                background-color: white;
                border: 1px solid #ccc;
                border-radius: 5px;
                padding: 2px;
            """)
            return

        # Extract just the symbol from the suit text
        suit = suit_text.split()[0] if ' ' in suit_text else suit_text

        # Determine color based on suit
        color = "red" if suit in ['♥', '♦'] else "black"

        # Create card display
        self.card_displays[card_index].setText(f"{value}\n{suit}")
        self.card_displays[card_index].setStyleSheet(f"""
            background-color: white;
            color: {color};
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 2px;
            font-weight: bold;
            font-size: 14px;
        """)

    def pulse_progress_ring(self):
        """Create a pulsing animation for the progress ring"""
        # Create animation for progress ring
        self.ring_anim = QPropertyAnimation(self.progress_ring, b"value")
        self.ring_anim.setDuration(1000)
        self.ring_anim.setStartValue(100)
        self.ring_anim.setEndValue(0)
        self.ring_anim.setEasingCurve(QEasingCurve.InOutQuad)

        # Connect to finished signal to restart animation
        self.ring_anim.finished.connect(self.restart_pulse_animation)

        # Start animation
        self.ring_anim.start()

    def restart_pulse_animation(self):
        """Restart the pulsing animation"""
        # Create animation for progress ring
        self.ring_anim = QPropertyAnimation(self.progress_ring, b"value")
        self.ring_anim.setDuration(1000)
        self.ring_anim.setStartValue(0)
        self.ring_anim.setEndValue(100)
        self.ring_anim.setEasingCurve(QEasingCurve.InOutQuad)

        # Connect to finished signal to restart animation
        self.ring_anim.finished.connect(self.pulse_progress_ring)

        # Start animation
        self.ring_anim.start()

    def setup_predictions_page(self):
        """Set up the predictions page"""
        layout = QVBoxLayout(self.predictionsPage)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Header with title and animation
        header_layout = QHBoxLayout()

        # Title with animation
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)

        title = TitleLabel("Predictions")
        title_layout.addWidget(title)

        # Animated subtitle
        subtitle = BodyLabel("AI-powered predictions for winning seats")
        subtitle.setStyleSheet("color: " + COLORS['secondary'].name() + ";")
        title_layout.addWidget(subtitle)

        header_layout.addWidget(title_container)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Predictions card with animation
        pred_widget = AnimatedCard()
        pred_layout = QVBoxLayout(pred_widget)
        pred_layout.setSpacing(15)

        # Add a header to the card
        card_header = QWidget()
        card_header.setStyleSheet(f"""
            background-color: {COLORS['primary'].name()};
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 10px;
        """)
        card_header_layout = QHBoxLayout(card_header)
        card_header_layout.setContentsMargins(10, 5, 10, 5)

        header_label = SubtitleLabel("Current Predictions")
        header_label.setStyleSheet("color: white; font-weight: bold;")
        card_header_layout.addWidget(header_label)

        pred_layout.addWidget(card_header)

        # Content container
        content_container = QWidget()
        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(15)

        # Primary prediction with visual indicator
        primary_container = QWidget()
        primary_container.setStyleSheet(f"""
            background-color: {COLORS['card_a'].name()};
            border-radius: 8px;
            padding: 10px;
            color: white;
        """)
        primary_layout = QHBoxLayout(primary_container)
        primary_layout.setContentsMargins(15, 10, 15, 10)

        primary_icon = QLabel()
        primary_icon.setFixedSize(32, 32)
        primary_icon.setStyleSheet("""
            background-color: white;
            border-radius: 16px;
            padding: 5px;
        """)
        primary_layout.addWidget(primary_icon)

        primary_text = QWidget()
        primary_text_layout = QVBoxLayout(primary_text)
        primary_text_layout.setContentsMargins(0, 0, 0, 0)

        primary_title = SubtitleLabel("Primary Prediction")
        primary_title.setStyleSheet("color: white; font-weight: bold;")
        primary_text_layout.addWidget(primary_title)

        self.primary_label = BodyLabel(self.primary_prediction)
        self.primary_label.setStyleSheet("color: white;")
        primary_text_layout.addWidget(self.primary_label)

        primary_layout.addWidget(primary_text)
        primary_layout.addStretch()

        content_layout.addWidget(primary_container)

        # Secondary prediction with visual indicator
        secondary_container = QWidget()
        secondary_container.setStyleSheet(f"""
            background-color: {COLORS['card_b'].name()};
            border-radius: 8px;
            padding: 10px;
            color: white;
        """)
        secondary_layout = QHBoxLayout(secondary_container)
        secondary_layout.setContentsMargins(15, 10, 15, 10)

        secondary_icon = QLabel()
        secondary_icon.setFixedSize(32, 32)
        secondary_icon.setStyleSheet("""
            background-color: white;
            border-radius: 16px;
            padding: 5px;
        """)
        secondary_layout.addWidget(secondary_icon)

        secondary_text = QWidget()
        secondary_text_layout = QVBoxLayout(secondary_text)
        secondary_text_layout.setContentsMargins(0, 0, 0, 0)

        secondary_title = SubtitleLabel("Secondary Prediction")
        secondary_title.setStyleSheet("color: white; font-weight: bold;")
        secondary_text_layout.addWidget(secondary_title)

        self.secondary_label = BodyLabel(self.secondary_prediction)
        self.secondary_label.setStyleSheet("color: white;")
        secondary_text_layout.addWidget(self.secondary_label)

        secondary_layout.addWidget(secondary_text)
        secondary_layout.addStretch()

        content_layout.addWidget(secondary_container)

        # Accuracy with progress bar
        accuracy_container = QWidget()
        accuracy_container.setStyleSheet("""
            background-color: """ + (
                "#F5F5F5" if not isDarkTheme() else "#333333"
            ) + """;
            border-radius: 8px;
            padding: 10px;
        """)
        accuracy_layout = QVBoxLayout(accuracy_container)
        accuracy_layout.setContentsMargins(15, 10, 15, 10)

        accuracy_header = QHBoxLayout()
        accuracy_header.addWidget(SubtitleLabel("Prediction Accuracy:"))
        self.accuracy_label = BodyLabel(self.prediction_accuracy)
        accuracy_header.addWidget(self.accuracy_label)
        accuracy_header.addStretch()
        accuracy_layout.addLayout(accuracy_header)

        # Add progress bar for accuracy
        self.accuracy_bar = ProgressBar()
        self.accuracy_bar.setFixedHeight(10)
        self.accuracy_bar.setValue(0)  # Will be updated when predictions are made
        accuracy_layout.addWidget(self.accuracy_bar)

        content_layout.addWidget(accuracy_container)

        pred_layout.addWidget(content_container)
        layout.addWidget(pred_widget)

        # Prediction history with animated cards
        history_container = QWidget()
        history_layout = QVBoxLayout(history_container)
        history_layout.setContentsMargins(0, 10, 0, 0)

        history_header = QHBoxLayout()
        history_title = SubtitleLabel("Prediction History")
        history_header.addWidget(history_title)

        # Add refresh button
        refresh_btn = TransparentToolButton(FluentIcon.SYNC)
        refresh_btn.setToolTip("Refresh History")
        refresh_btn.clicked.connect(self.update_prediction_history)
        history_header.addWidget(refresh_btn)

        history_header.addStretch()
        history_layout.addLayout(history_header)

        # Scrollable history
        self.history_widget = QWidget()
        self.history_layout = QVBoxLayout(self.history_widget)
        self.history_layout.setContentsMargins(0, 0, 0, 0)
        self.history_layout.setSpacing(10)

        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.history_widget)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 10px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #c0c0c0;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        history_layout.addWidget(scroll_area)
        layout.addWidget(history_container)

        # Add placeholder text if no history
        if not hasattr(self, 'pattern_memory') or not self.pattern_memory.get('prediction_log'):
            placeholder = BodyLabel("No prediction history yet. Start making predictions to see the history here.")
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setStyleSheet("color: #888; margin: 20px;")
            self.history_layout.addWidget(placeholder)

    def setup_visual_patterns_page(self):
        """Set up the visual patterns page"""
        layout = QVBoxLayout(self.visualPatternsPage)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Header with title and animation
        header_layout = QHBoxLayout()

        # Title with animation
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)

        title = TitleLabel("Visual Patterns")
        title_layout.addWidget(title)

        # Animated subtitle
        subtitle = BodyLabel("Visualize winning patterns and trends")
        subtitle.setStyleSheet("color: " + COLORS['secondary'].name() + ";")
        title_layout.addWidget(subtitle)

        header_layout.addWidget(title_container)
        header_layout.addStretch()

        # Add refresh button
        refresh_btn = AnimatedButton("Refresh", icon=FluentIcon.SYNC)
        refresh_btn.clicked.connect(self.update_visual_display)
        header_layout.addWidget(refresh_btn)

        layout.addLayout(header_layout)

        # Pattern visualization with custom widget
        self.pattern_widget = PredictionVisualizer()
        self.pattern_widget.setMinimumHeight(400)

        # Add to a card for better visual appearance
        pattern_card = AnimatedCard()
        pattern_layout = QVBoxLayout(pattern_card)
        pattern_layout.setContentsMargins(0, 0, 0, 0)
        pattern_layout.addWidget(self.pattern_widget)

        layout.addWidget(pattern_card)

        # Pattern statistics
        stats_card = AnimatedCard()
        stats_layout = QVBoxLayout(stats_card)

        # Add a header to the card
        stats_header = QWidget()
        stats_header.setStyleSheet(f"""
            background-color: {COLORS['primary'].name()};
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 10px;
        """)
        stats_header_layout = QHBoxLayout(stats_header)
        stats_header_layout.setContentsMargins(10, 5, 10, 5)

        header_label = SubtitleLabel("Pattern Statistics")
        header_label.setStyleSheet("color: white; font-weight: bold;")
        stats_header_layout.addWidget(header_label)

        stats_layout.addWidget(stats_header)

        # Stats content
        stats_content = QWidget()
        stats_content_layout = QVBoxLayout(stats_content)
        stats_content_layout.setContentsMargins(20, 20, 20, 20)

        # Create grid for statistics
        stats_grid = QHBoxLayout()

        # Repeat patterns stats
        repeat_stats = QWidget()
        repeat_layout = QVBoxLayout(repeat_stats)
        repeat_layout.setContentsMargins(0, 0, 0, 0)

        repeat_title = SubtitleLabel("Repeat Patterns")
        repeat_layout.addWidget(repeat_title)

        self.repeat_stats_label = BodyLabel("No data yet")
        repeat_layout.addWidget(self.repeat_stats_label)

        stats_grid.addWidget(repeat_stats)

        # Block patterns stats
        block_stats = QWidget()
        block_layout = QVBoxLayout(block_stats)
        block_layout.setContentsMargins(0, 0, 0, 0)

        block_title = SubtitleLabel("Block Patterns")
        block_layout.addWidget(block_title)

        self.block_stats_label = BodyLabel("No data yet")
        block_layout.addWidget(self.block_stats_label)

        stats_grid.addWidget(block_stats)

        # Visual patterns stats
        visual_stats = QWidget()
        visual_layout = QVBoxLayout(visual_stats)
        visual_layout.setContentsMargins(0, 0, 0, 0)

        visual_title = SubtitleLabel("Visual Patterns")
        visual_layout.addWidget(visual_title)

        self.visual_stats_label = BodyLabel("No data yet")
        visual_layout.addWidget(self.visual_stats_label)

        stats_grid.addWidget(visual_stats)

        stats_content_layout.addLayout(stats_grid)
        stats_layout.addWidget(stats_content)

        layout.addWidget(stats_card)

        # Add a button to export statistics
        export_btn = AnimatedButton("Export Statistics", icon=FluentIcon.SAVE)
        export_btn.clicked.connect(self.export_statistics)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(export_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)
        layout.addStretch()

    def export_statistics(self):
        """Export statistics to a file"""
        # This would be implemented to export statistics to a file
        InfoBar.info(
            title="Export Statistics",
            content="Statistics export would be implemented here.",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

    def setup_settings_page(self):
        """Set up the settings page"""
        layout = QVBoxLayout(self.settingsPage)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Header with title and animation
        header_layout = QHBoxLayout()

        # Title with animation
        title_container = QWidget()
        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(0, 0, 0, 0)

        title = TitleLabel("Settings")
        title_layout.addWidget(title)

        # Animated subtitle
        subtitle = BodyLabel("Customize your application preferences")
        subtitle.setStyleSheet("color: " + COLORS['secondary'].name() + ";")
        title_layout.addWidget(subtitle)

        header_layout.addWidget(title_container)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Database management
        db_widget = AnimatedCard()
        db_layout = QVBoxLayout(db_widget)

        # Add a header to the card
        db_header = QWidget()
        db_header.setStyleSheet(f"""
            background-color: {COLORS['primary'].name()};
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 10px;
        """)
        db_header_layout = QHBoxLayout(db_header)
        db_header_layout.setContentsMargins(10, 5, 10, 5)

        db_header_label = SubtitleLabel("Database Management")
        db_header_label.setStyleSheet("color: white; font-weight: bold;")
        db_header_layout.addWidget(db_header_label)

        db_layout.addWidget(db_header)

        # Database content
        db_content = QWidget()
        db_content_layout = QVBoxLayout(db_content)
        db_content_layout.setContentsMargins(20, 20, 20, 20)
        db_content_layout.setSpacing(15)

        # Database info
        db_info = BodyLabel(f"Database: {DB_PATH}")
        db_content_layout.addWidget(db_info)

        # Database actions
        db_actions = QHBoxLayout()

        delete_btn = AnimatedButton("Delete Entries", icon=FluentIcon.DELETE)
        delete_btn.clicked.connect(self.delete_entries)
        db_actions.addWidget(delete_btn)

        backup_btn = AnimatedButton("Backup Database", icon=FluentIcon.SAVE)
        backup_btn.clicked.connect(self.backup_database)
        db_actions.addWidget(backup_btn)

        reset_btn = AnimatedButton("Reset Database", icon=FluentIcon.CANCEL)
        reset_btn.setStyleSheet(f"""
            AnimatedButton {{
                background-color: {COLORS['error'].name()};
                color: white;
            }}
        """)
        reset_btn.clicked.connect(self.reset_database)
        db_actions.addWidget(reset_btn)

        db_content_layout.addLayout(db_actions)
        db_layout.addWidget(db_content)

        layout.addWidget(db_widget)

        # Appearance settings
        appearance_widget = AnimatedCard()
        appearance_layout = QVBoxLayout(appearance_widget)

        # Add a header to the card
        appearance_header = QWidget()
        appearance_header.setStyleSheet(f"""
            background-color: {COLORS['primary'].name()};
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 10px;
        """)
        appearance_header_layout = QHBoxLayout(appearance_header)
        appearance_header_layout.setContentsMargins(10, 5, 10, 5)

        appearance_header_label = SubtitleLabel("Appearance")
        appearance_header_label.setStyleSheet("color: white; font-weight: bold;")
        appearance_header_layout.addWidget(appearance_header_label)

        appearance_layout.addWidget(appearance_header)

        # Appearance content
        appearance_content = QWidget()
        appearance_content_layout = QVBoxLayout(appearance_content)
        appearance_content_layout.setContentsMargins(20, 20, 20, 20)
        appearance_content_layout.setSpacing(15)

        # Theme settings
        theme_layout = QHBoxLayout()
        theme_layout.addWidget(SubtitleLabel("Theme:"))

        theme_combo = ComboBox()
        theme_combo.addItems(["Light", "Dark", "Auto"])
        theme_combo.setCurrentIndex(2)  # Auto by default
        theme_combo.currentIndexChanged.connect(self.change_theme)
        theme_layout.addWidget(theme_combo)
        theme_layout.addStretch()

        appearance_content_layout.addLayout(theme_layout)

        # Animation toggle
        animation_layout = QHBoxLayout()
        animation_layout.addWidget(SubtitleLabel("Enable Animations:"))

        self.animation_switch = SwitchButton()
        self.animation_switch.setChecked(True)
        self.animation_switch.checkedChanged.connect(self.toggle_animations)
        animation_layout.addWidget(self.animation_switch)
        animation_layout.addStretch()

        appearance_content_layout.addLayout(animation_layout)

        appearance_layout.addWidget(appearance_content)

        layout.addWidget(appearance_widget)

        # Capture settings
        capture_widget = AnimatedCard()
        capture_layout = QVBoxLayout(capture_widget)

        # Add a header to the card
        capture_header = QWidget()
        capture_header.setStyleSheet(f"""
            background-color: {COLORS['primary'].name()};
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 10px;
        """)
        capture_header_layout = QHBoxLayout(capture_header)
        capture_header_layout.setContentsMargins(10, 5, 10, 5)

        capture_header_label = SubtitleLabel("Screenshot Capture")
        capture_header_label.setStyleSheet("color: white; font-weight: bold;")
        capture_header_layout.addWidget(capture_header_label)

        capture_layout.addWidget(capture_header)

        # Capture content
        capture_content = QWidget()
        capture_content_layout = QVBoxLayout(capture_content)
        capture_content_layout.setContentsMargins(20, 20, 20, 20)
        capture_content_layout.setSpacing(15)

        # Capture area settings
        capture_area_layout = QHBoxLayout()
        capture_area_layout.addWidget(SubtitleLabel("Capture Area:"))

        capture_area_label = BodyLabel(f"({CROP_BOX[0]}, {CROP_BOX[1]}, {CROP_BOX[2]}, {CROP_BOX[3]})")
        capture_area_layout.addWidget(capture_area_label)

        edit_area_btn = AnimatedButton("Edit", icon=FluentIcon.EDIT)
        edit_area_btn.clicked.connect(self.edit_capture_area)
        capture_area_layout.addWidget(edit_area_btn)
        capture_area_layout.addStretch()

        capture_content_layout.addLayout(capture_area_layout)

        # Capture delay settings
        capture_delay_layout = QHBoxLayout()
        capture_delay_layout.addWidget(SubtitleLabel("Capture Delay:"))

        capture_delay_label = BodyLabel(f"{DELAY_BETWEEN_CAPTURES} seconds")
        capture_delay_layout.addWidget(capture_delay_label)

        edit_delay_btn = AnimatedButton("Edit", icon=FluentIcon.EDIT)
        edit_delay_btn.clicked.connect(self.edit_capture_delay)
        capture_delay_layout.addWidget(edit_delay_btn)
        capture_delay_layout.addStretch()

        capture_content_layout.addLayout(capture_delay_layout)

        capture_layout.addWidget(capture_content)

        layout.addWidget(capture_widget)

        # About section
        about_widget = AnimatedCard()
        about_layout = QVBoxLayout(about_widget)

        # Add a header to the card
        about_header = QWidget()
        about_header.setStyleSheet(f"""
            background-color: {COLORS['primary'].name()};
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            padding: 10px;
        """)
        about_header_layout = QHBoxLayout(about_header)
        about_header_layout.setContentsMargins(10, 5, 10, 5)

        about_header_label = SubtitleLabel("About")
        about_header_label.setStyleSheet("color: white; font-weight: bold;")
        about_header_layout.addWidget(about_header_label)

        about_layout.addWidget(about_header)

        # About content
        about_content = QWidget()
        about_content_layout = QVBoxLayout(about_content)
        about_content_layout.setContentsMargins(20, 20, 20, 20)
        about_content_layout.setSpacing(10)

        app_name = TitleLabel("PolitePredict")
        app_name.setAlignment(Qt.AlignCenter)
        about_content_layout.addWidget(app_name)

        app_version = BodyLabel("Version 1.0.0")
        app_version.setAlignment(Qt.AlignCenter)
        about_content_layout.addWidget(app_version)

        app_description = BodyLabel("An advanced card game prediction system using machine learning and pattern recognition.")
        app_description.setAlignment(Qt.AlignCenter)
        app_description.setWordWrap(True)
        about_content_layout.addWidget(app_description)

        about_layout.addWidget(about_content)

        layout.addWidget(about_widget)
        layout.addStretch()

    def backup_database(self):
        """Backup the database"""
        try:
            # Create backup filename with timestamp
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"game_data_backup_{timestamp}.db"

            # Show progress
            state_tooltip = StateToolTip("Backing Up", "Creating database backup...", self)
            state_tooltip.show()

            # Copy database file
            import shutil
            shutil.copy2(DB_PATH, backup_path)

            # Show success message
            state_tooltip.setState(True)
            state_tooltip.setContent(f"Backup created: {backup_path}")
            QTimer.singleShot(2000, state_tooltip.close)

            InfoBar.success(
                title="Backup Complete",
                content=f"Database backed up to {backup_path}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )

        except Exception as e:
            MessageBox("Backup Error", str(e), self).exec()

    def reset_database(self):
        """Reset the database"""
        # Ask for confirmation
        result = MessageBox(
            "Reset Database",
            "Are you sure you want to reset the database? This will delete ALL data and cannot be undone.",
            self
        )

        if not result.exec():
            return

        try:
            # Show progress
            state_tooltip = StateToolTip("Resetting", "Resetting database...", self)
            state_tooltip.show()

            # Close database connection
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Drop all tables
            cursor.execute("DROP TABLE IF EXISTS winfail")
            cursor.execute("DROP TABLE IF EXISTS seat_changing")
            cursor.execute("DROP TABLE IF EXISTS backend_data")

            conn.commit()
            conn.close()

            # Recreate database
            self.setup_database()

            # Reset session counter
            self.current_session_count = 0
            self.session_label.setText(f"Session Count: {self.current_session_count}")

            # Reset progress ring
            if hasattr(self, 'progress_ring'):
                self.progress_ring.setValue(0)

            if hasattr(self, 'progress_label'):
                self.progress_label.setText("0/60 entries")

            # Clear predictions
            self.primary_prediction = "(Auto after 60 entries)"
            self.secondary_prediction = "(Auto after 60 entries)"
            self.prediction_accuracy = "(%)"

            if hasattr(self, 'primary_label'):
                self.primary_label.setText(self.primary_prediction)

            if hasattr(self, 'secondary_label'):
                self.secondary_label.setText(self.secondary_prediction)

            if hasattr(self, 'accuracy_label'):
                self.accuracy_label.setText(self.prediction_accuracy)

            # Reset pattern memory
            self.pattern_memory = {
                'repeat_patterns': {},
                'block_patterns': {},
                'singles_patterns': {},
                'card_influence': {},
                'visual_patterns': {},
                'seat_change_patterns': {},
                'prediction_accuracy': {
                    'overall': [],
                    'by_pattern': {}
                },
                'prediction_log': []
            }
            self.save_pattern_memory()

            # Show success message
            state_tooltip.setState(True)
            state_tooltip.setContent("Database reset complete")
            QTimer.singleShot(2000, state_tooltip.close)

            InfoBar.success(
                title="Reset Complete",
                content="Database has been reset successfully",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )

        except Exception as e:
            MessageBox("Reset Error", str(e), self).exec()

    def toggle_animations(self, enabled):
        """Toggle animations on/off"""
        # This would control whether animations are enabled
        InfoBar.info(
            title="Animations",
            content=f"Animations {'enabled' if enabled else 'disabled'}",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

    def edit_capture_area(self):
        """Edit the screenshot capture area"""
        # This would allow editing the capture area
        InfoBar.info(
            title="Capture Area",
            content="Capture area editing would be implemented here",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

    def edit_capture_delay(self):
        """Edit the capture delay"""
        # This would allow editing the capture delay
        InfoBar.info(
            title="Capture Delay",
            content="Capture delay editing would be implemented here",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

    def reset_prediction_values(self):
        """Reset prediction values and fade them back in"""
        # Update text values
        self.primary_label.setText("(Auto after 60 entries)")
        self.secondary_label.setText("(Auto after 60 entries)")
        self.accuracy_label.setText("(%)")

        self.primary_prediction = "(Auto after 60 entries)"
        self.secondary_prediction = "(Auto after 60 entries)"
        self.prediction_accuracy = "(%)"

        # Create fade-in animation
        self.fade_in_anim = QPropertyAnimation(self.primary_label, b"windowOpacity")
        self.fade_in_anim.setDuration(300)
        self.fade_in_anim.setStartValue(0.0)
        self.fade_in_anim.setEndValue(1.0)
        self.fade_in_anim.setEasingCurve(QEasingCurve.OutQuad)
        self.fade_in_anim.start()

    def on_seat_selected(self, button):
        """Handle seat selection"""
        self.winning_seat = button.text()

    def on_trend_selected(self, index):
        """Handle trend selection"""
        if index > 0:
            self.selected_trend = self.trend_combo.currentText()
        else:
            self.selected_trend = None

    def update_card_color(self, combo, index):
        """Update card color based on selected suit"""
        suit = combo.currentText()
        if '♥' in suit or '♦' in suit:  # Hearts or Diamonds
            self.card_colors[index].setStyleSheet("background-color: red; border: 1px solid #ccc;")
        elif '♠' in suit or '♣' in suit:  # Spades or Clubs
            self.card_colors[index].setStyleSheet("background-color: black; border: 1px solid #ccc;")
        else:
            self.card_colors[index].setStyleSheet("background-color: transparent; border: 1px solid #ccc;")

    def clear_input(self):
        """Clear all input fields"""
        for combo in self.card_values:
            combo.setCurrentIndex(0)

        for combo in self.card_suits:
            combo.setCurrentIndex(0)

        for label in self.card_colors:
            label.setStyleSheet("background-color: transparent; border: 1px solid #ccc;")

        self.trend_combo.setCurrentIndex(0)
        self.selected_trend = None

    def save_data(self):
        """Save entered data to database"""
        if not self.winning_seat:
            MessageBox("Error", "Please select a winning seat", self).exec()
            return

        if not self.selected_trend:
            MessageBox("Error", "Please select a trend", self).exec()
            return

        # Collect card data
        cards = []
        for i in range(3):
            value = self.card_values[i].currentText()
            suit_text = self.card_suits[i].currentText()

            if not value or not suit_text:
                MessageBox("Error", f"Please select value and suit for card {i+1}", self).exec()
                return

            # Extract just the symbol from the suit text
            suit = suit_text.split()[0] if ' ' in suit_text else suit_text

            # Determine color based on suit
            color = "red" if suit in ['♥', '♦'] else "black"
            cards.append((value, color))

        try:
            # Show progress
            state_tooltip = StateToolTip("Saving", "Saving data to database...", self)
            state_tooltip.show()

            # Connect to database
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Check if we have a prediction to validate
            actual_seat = self.winning_seat
            self.validate_prediction(actual_seat)

            # Insert into backend_data
            cursor.execute("""
                INSERT INTO backend_data (winning_seat, card1, card2, card3, color1, color2, color3, trend, timestamp, session_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), ?)
            """, (
                actual_seat,
                cards[0][0], cards[1][0], cards[2][0],
                cards[0][1], cards[1][1], cards[2][1],
                self.selected_trend,
                1  # Default session ID
            ))

            # Update winfail table
            winfail_row = []
            for seat in ['A', 'B', 'C']:
                if seat == actual_seat:
                    winfail_row.append("Win")
                else:
                    winfail_row.append("Fail")

            cursor.execute("""
                INSERT INTO winfail (A, B, C, timestamp)
                VALUES (?, ?, ?, datetime('now', 'localtime'))
            """, tuple(winfail_row))

            # Get previous winning seat for seat_changing table
            cursor.execute("SELECT winning_seat FROM backend_data ORDER BY id DESC LIMIT 1 OFFSET 1")
            result = cursor.fetchone()
            previous_win = result[0] if result else None

            # Update seat_changing table if we have a previous win
            if previous_win:
                self.update_seat_changing_table(actual_seat, previous_win)

            conn.commit()
            conn.close()

            # Update session count
            self.current_session_count += 1
            self.session_label.setText(f"Session Count: {self.current_session_count}")

            # Update progress ring and label
            progress_value = min(100, int((self.current_session_count / 60) * 100))
            self.progress_ring.setValue(progress_value)
            self.progress_label.setText(f"{self.current_session_count}/60 entries")

            # Add a pulsing animation to the progress ring when reaching 100%
            if progress_value == 100:
                self.pulse_progress_ring()

            # Auto-trigger prediction if we have 60+ entries
            if self.current_session_count >= 60:
                self.train_and_predict()

            # Clear input fields
            self.clear_input()

            # Update visual display
            self.update_visual_display()

            # Show success message
            state_tooltip.setContent("Data saved successfully")
            state_tooltip.setState(True)
            QTimer.singleShot(2000, state_tooltip.close)

            # Show info bar
            InfoBar.success(
                title="Success",
                content=f"Data saved successfully. Session count: {self.current_session_count}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )

        except Exception as e:
            MessageBox("Database Error", str(e), self).exec()

    def update_seat_changing_table(self, current_win, previous_win):
        """Update seat_changing table based on win transition"""
        if not previous_win:
            return

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Create a row with the transition (e.g., "AB" means A to B)
        row = {"A": None, "B": None, "C": None}
        transition = previous_win + current_win
        row[previous_win] = transition

        cursor.execute("""
            INSERT INTO seat_changing (A, B, C, timestamp)
            VALUES (?, ?, ?, datetime('now', 'localtime'))
        """, (row["A"], row["B"], row["C"]))

        conn.commit()
        conn.close()

    def validate_prediction(self, actual_seat):
        """Validate the previous prediction against the actual result"""
        # Check if we have a prediction to validate
        if not hasattr(self, 'last_primary') or not hasattr(self, 'last_secondary'):
            return

        # Check if prediction was correct
        primary_correct = self.last_primary == actual_seat
        secondary_correct = self.last_secondary == actual_seat
        any_correct = primary_correct or secondary_correct

        # Update prediction accuracy in pattern memory
        if 'prediction_accuracy' not in self.pattern_memory:
            self.pattern_memory['prediction_accuracy'] = {
                'overall': [],
                'primary': [],
                'secondary': [],
                'any': []
            }

        # Add result (1 for correct, 0 for incorrect)
        self.pattern_memory['prediction_accuracy']['primary'].append(1 if primary_correct else 0)
        self.pattern_memory['prediction_accuracy']['secondary'].append(1 if secondary_correct else 0)
        self.pattern_memory['prediction_accuracy']['any'].append(1 if any_correct else 0)

        # Calculate overall accuracy
        primary_accuracy = sum(self.pattern_memory['prediction_accuracy']['primary']) / len(self.pattern_memory['prediction_accuracy']['primary'])
        secondary_accuracy = sum(self.pattern_memory['prediction_accuracy']['secondary']) / len(self.pattern_memory['prediction_accuracy']['secondary'])
        any_accuracy = sum(self.pattern_memory['prediction_accuracy']['any']) / len(self.pattern_memory['prediction_accuracy']['any'])

        overall_accuracy = (primary_accuracy * 0.7) + (secondary_accuracy * 0.3)
        self.pattern_memory['prediction_accuracy']['overall'].append(overall_accuracy)

        # Log the prediction result
        self.pattern_memory['prediction_log'].append({
            'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'primary': self.last_primary,
            'secondary': self.last_secondary,
            'actual': actual_seat,
            'primary_correct': primary_correct,
            'secondary_correct': secondary_correct,
            'any_correct': any_correct
        })

        # Save pattern memory
        self.save_pattern_memory()

        # Update accuracy display
        accuracy_pct = round(overall_accuracy * 100, 2)
        self.accuracy_label.setText(f"Accuracy: {accuracy_pct}%")
        self.prediction_accuracy = f"Accuracy: {accuracy_pct}%"

        # Update prediction history
        self.update_prediction_history()

        # If prediction was wrong, trigger retraining
        if not any_correct and len(self.pattern_memory['prediction_accuracy']['any']) >= 5:
            # Only retrain if we have enough data and consistent errors
            recent_accuracy = sum(self.pattern_memory['prediction_accuracy']['any'][-5:]) / 5
            if recent_accuracy < 0.6:  # Less than 60% accuracy in last 5 predictions
                print("Auto-retraining due to low recent accuracy")
                self.train_and_predict()

    def update_prediction_history(self):
        """Update the prediction history display"""
        # Clear existing items
        for i in reversed(range(self.history_layout.count())):
            widget = self.history_layout.itemAt(i).widget()
            if widget:
                widget.deleteLater()

        # Check if we have any history
        if not hasattr(self, 'pattern_memory') or not self.pattern_memory.get('prediction_log'):
            placeholder = BodyLabel("No prediction history yet. Start making predictions to see the history here.")
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setStyleSheet("color: #888; margin: 20px;")
            self.history_layout.addWidget(placeholder)
            return

        # Add history items (most recent first)
        for i, entry in enumerate(reversed(self.pattern_memory['prediction_log'][-10:])):
            if i >= 10:  # Show only last 10 entries
                break

            # Create animated card with appropriate color based on result
            if entry['primary_correct']:
                history_card = AnimatedCard(color=COLORS['success'])
            elif entry['secondary_correct']:
                history_card = AnimatedCard(color=COLORS['warning'])
            else:
                history_card = AnimatedCard(color=COLORS['error'])

            card_layout = QVBoxLayout(history_card)
            card_layout.setContentsMargins(15, 15, 15, 15)

            # Header with timestamp and result
            header_layout = QHBoxLayout()

            # Timestamp with icon
            time_container = QWidget()
            time_layout = QHBoxLayout(time_container)
            time_layout.setContentsMargins(0, 0, 0, 0)
            time_layout.setSpacing(5)

            time_icon = QLabel()
            time_icon.setFixedSize(16, 16)
            time_icon.setStyleSheet("""
                background-color: transparent;
                border: none;
            """)
            time_layout.addWidget(time_icon)

            time_label = CaptionLabel(entry['timestamp'])
            time_label.setStyleSheet("color: #888;")
            time_layout.addWidget(time_label)

            header_layout.addWidget(time_container)
            header_layout.addStretch()

            # Result indicator
            if entry['primary_correct']:
                result_label = BodyLabel("✓ Primary Correct")
                result_label.setStyleSheet("color: " + COLORS['success'].name() + "; font-weight: bold;")
            elif entry['secondary_correct']:
                result_label = BodyLabel("✓ Secondary Correct")
                result_label.setStyleSheet("color: " + COLORS['warning'].name() + "; font-weight: bold;")
            else:
                result_label = BodyLabel("✗ Incorrect")
                result_label.setStyleSheet("color: " + COLORS['error'].name() + "; font-weight: bold;")

            header_layout.addWidget(result_label)
            card_layout.addLayout(header_layout)

            # Add separator
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            separator.setStyleSheet("""
                background-color: #ddd;
                height: 1px;
                margin: 5px 0;
            """)
            card_layout.addWidget(separator)

            # Prediction details
            details_layout = QHBoxLayout()

            # Predictions
            predictions_container = QWidget()
            predictions_layout = QVBoxLayout(predictions_container)
            predictions_layout.setContentsMargins(0, 0, 0, 0)
            predictions_layout.setSpacing(5)

            predictions_title = CaptionLabel("Predictions:")
            predictions_title.setStyleSheet("color: #888;")
            predictions_layout.addWidget(predictions_title)

            primary_label = BodyLabel(f"Primary: Seat {entry['primary']}")
            primary_label.setStyleSheet(f"color: {COLORS['card_a'].name()}; font-weight: bold;")
            predictions_layout.addWidget(primary_label)

            secondary_label = BodyLabel(f"Secondary: Seat {entry['secondary']}")
            secondary_label.setStyleSheet(f"color: {COLORS['card_b'].name()}; font-weight: bold;")
            predictions_layout.addWidget(secondary_label)

            details_layout.addWidget(predictions_container)

            # Actual result
            actual_container = QWidget()
            actual_layout = QVBoxLayout(actual_container)
            actual_layout.setContentsMargins(0, 0, 0, 0)
            actual_layout.setSpacing(5)

            actual_title = CaptionLabel("Actual Result:")
            actual_title.setStyleSheet("color: #888;")
            actual_layout.addWidget(actual_title)

            # Determine color based on which seat won
            if entry['actual'] == 'A':
                actual_color = COLORS['card_a'].name()
            elif entry['actual'] == 'B':
                actual_color = COLORS['card_b'].name()
            else:
                actual_color = COLORS['card_c'].name()

            actual_label = BodyLabel(f"Seat {entry['actual']}")
            actual_label.setStyleSheet(f"color: {actual_color}; font-weight: bold; font-size: 16px;")
            actual_layout.addWidget(actual_label)

            details_layout.addWidget(actual_container)

            card_layout.addLayout(details_layout)

            # Add to history with animation
            self.history_layout.addWidget(history_card)

            # Add animation to make the card appear with a slight delay based on index
            history_card.setWindowOpacity(0.0)
            anim = QPropertyAnimation(history_card, b"windowOpacity")
            anim.setDuration(300)
            anim.setStartValue(0.0)
            anim.setEndValue(1.0)
            anim.setEasingCurve(QEasingCurve.InOutQuad)
            anim.setStartTime(i * 100)  # Stagger the animations
            anim.start()

    def capture_and_process(self):
        """Capture screenshot and process it"""
        # Show info message
        InfoBar.info(
            title="Screenshot",
            content="Screenshot capture would happen here. For now, please enter data manually.",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

    def train_and_predict(self):
        """Train models and make prediction"""
        # Show progress
        self.state_tooltip = StateToolTip("Training", "Training models...", self)
        self.state_tooltip.show()

        # Create and start prediction thread
        self.pred_thread = PredictionThread(
            self,
            train_func=self.train_models,
            predict_func=self.predict_next_win
        )

        self.pred_thread.progress.connect(self.update_prediction_progress)
        self.pred_thread.finished.connect(self.update_prediction_result)
        self.pred_thread.error.connect(self.show_prediction_error)

        self.pred_thread.start()

    def update_prediction_progress(self, message):
        """Update prediction progress message"""
        if hasattr(self, 'state_tooltip'):
            self.state_tooltip.setContent(message)

    def update_prediction_result(self, primary, secondary, confidence):
        """Update prediction results in UI"""
        # Store predictions for later validation
        self.last_primary = primary
        self.last_secondary = secondary
        self.last_confidence = confidence

        # Update UI with animation
        self.animate_prediction_update(primary, secondary, confidence)

        # Close progress tooltip
        if hasattr(self, 'state_tooltip'):
            self.state_tooltip.setState(True)
            self.state_tooltip.setContent("Prediction complete")
            QTimer.singleShot(2000, self.state_tooltip.close)

        # Show success message
        InfoBar.success(
            title="Prediction Complete",
            content=f"Primary: {primary}, Secondary: {secondary}, Confidence: {confidence:.2f}%",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

        # Update prediction history
        self.update_prediction_history()

    def animate_prediction_update(self, primary, secondary, confidence):
        """Animate the prediction update"""
        # Create fade-out animation for current values
        self.fade_out_anim = QPropertyAnimation(self.primary_label, b"windowOpacity")
        self.fade_out_anim.setDuration(300)
        self.fade_out_anim.setStartValue(1.0)
        self.fade_out_anim.setEndValue(0.0)
        self.fade_out_anim.setEasingCurve(QEasingCurve.InQuad)

        # Connect to finished signal to update values and fade back in
        self.fade_out_anim.finished.connect(lambda: self.update_prediction_values(primary, secondary, confidence))

        # Start animation
        self.fade_out_anim.start()

        # Animate accuracy bar
        if hasattr(self, 'accuracy_bar'):
            self.accuracy_bar_anim = QPropertyAnimation(self.accuracy_bar, b"value")
            self.accuracy_bar_anim.setDuration(1000)
            self.accuracy_bar_anim.setStartValue(0)
            self.accuracy_bar_anim.setEndValue(int(confidence))
            self.accuracy_bar_anim.setEasingCurve(QEasingCurve.OutCubic)
            self.accuracy_bar_anim.start()

    def update_prediction_values(self, primary, secondary, confidence):
        """Update prediction values and fade them back in"""
        # Update text values
        self.primary_label.setText(f"Seat {primary}")
        self.secondary_label.setText(f"Seat {secondary}")
        self.accuracy_label.setText(f"Confidence: {confidence:.2f}%")

        self.primary_prediction = f"Seat {primary}"
        self.secondary_prediction = f"Seat {secondary}"
        self.prediction_accuracy = f"Confidence: {confidence:.2f}%"

        # Create fade-in animation
        self.fade_in_anim = QPropertyAnimation(self.primary_label, b"windowOpacity")
        self.fade_in_anim.setDuration(300)
        self.fade_in_anim.setStartValue(0.0)
        self.fade_in_anim.setEndValue(1.0)
        self.fade_in_anim.setEasingCurve(QEasingCurve.OutQuad)
        self.fade_in_anim.start()

    def show_prediction_error(self, error_message):
        """Show prediction error message"""
        # Close progress tooltip
        if hasattr(self, 'state_tooltip'):
            self.state_tooltip.setState(False)
            self.state_tooltip.setContent(error_message)
            QTimer.singleShot(2000, self.state_tooltip.close)

        # Show error message
        InfoBar.error(
            title="Prediction Error",
            content=error_message,
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

    def delete_entries(self):
        """Delete recent entries from database"""
        try:
            # Ask how many entries to delete
            num, ok = QInputDialog.getInt(
                self, "Delete Entries",
                "How many recent entries do you want to delete?",
                1, 1, 10, 1
            )

            if not ok:
                return

            # Ask for confirmation
            result = MessageBox(
                "Delete Entries",
                f"Are you sure you want to delete the last {num} entries? This cannot be undone.",
                self
            )

            if not result.exec():
                return

            # Show progress
            state_tooltip = StateToolTip("Deleting", "Deleting entries...", self)
            state_tooltip.show()

            # Connect to the database
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Get the IDs to delete from backend_data
            cursor.execute(f"SELECT id FROM backend_data ORDER BY id DESC LIMIT {num}")
            ids_to_delete = [row[0] for row in cursor.fetchall()]

            if not ids_to_delete:
                state_tooltip.close()
                InfoBar.warning(
                    title="No Data",
                    content="No entries found to delete.",
                    orient=Qt.Horizontal,
                    isClosable=True,
                    position=InfoBarPosition.TOP,
                    duration=3000,
                    parent=self
                )
                conn.close()
                return

            # Delete from backend_data
            for id_to_delete in ids_to_delete:
                cursor.execute("DELETE FROM backend_data WHERE id = ?", (id_to_delete,))

            # Delete from winfail
            cursor.execute(f"DELETE FROM winfail WHERE id IN (SELECT id FROM winfail ORDER BY id DESC LIMIT {num})")

            # Delete from seat_changing
            cursor.execute(f"DELETE FROM seat_changing WHERE id IN (SELECT id FROM seat_changing ORDER BY id DESC LIMIT {num})")

            conn.commit()
            conn.close()

            # Update session count
            self.current_session_count = max(0, self.current_session_count - num)
            self.session_label.setText(f"Current Session Count: {self.current_session_count}")

            # Update visual display
            self.update_visual_display()

            # Show success message
            state_tooltip.setContent(f"Deleted {num} entries")
            state_tooltip.setState(True)
            QTimer.singleShot(2000, state_tooltip.close)

            InfoBar.success(
                title="Entries Deleted",
                content=f"Deleted {num} entries. Session count: {self.current_session_count}",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )

        except Exception as e:
            MessageBox("Delete Error", str(e), self).exec()

    def new_session(self):
        """Start a new session"""
        result = MessageBox(
            "New Session",
            "Are you sure you want to start a new session? This will reset the session counter.",
            self
        )

        if not result.exec():
            return

        # Reset session counter
        self.current_session_count = 0
        self.session_label.setText(f"Session Count: {self.current_session_count}")

        # Reset progress ring and label
        if hasattr(self, 'progress_ring'):
            self.progress_ring.setValue(0)

        if hasattr(self, 'progress_label'):
            self.progress_label.setText("0/60 entries")

        # Clear predictions with animation
        if hasattr(self, 'primary_label') and hasattr(self, 'secondary_label') and hasattr(self, 'accuracy_label'):
            # Create fade-out animation
            self.fade_out_anim = QPropertyAnimation(self.primary_label, b"windowOpacity")
            self.fade_out_anim.setDuration(300)
            self.fade_out_anim.setStartValue(1.0)
            self.fade_out_anim.setEndValue(0.0)
            self.fade_out_anim.setEasingCurve(QEasingCurve.InQuad)

            # Connect to finished signal to update values and fade back in
            self.fade_out_anim.finished.connect(self.reset_prediction_values)

            # Start animation
            self.fade_out_anim.start()

            # Reset accuracy bar if it exists
            if hasattr(self, 'accuracy_bar'):
                self.accuracy_bar_anim = QPropertyAnimation(self.accuracy_bar, b"value")
                self.accuracy_bar_anim.setDuration(500)
                self.accuracy_bar_anim.setStartValue(self.accuracy_bar.value())
                self.accuracy_bar_anim.setEndValue(0)
                self.accuracy_bar_anim.setEasingCurve(QEasingCurve.OutQuad)
                self.accuracy_bar_anim.start()
        else:
            # Fallback if widgets don't exist yet
            self.primary_prediction = "(Auto after 60 entries)"
            self.secondary_prediction = "(Auto after 60 entries)"
            self.prediction_accuracy = "(%)"

        # Show success message
        InfoBar.success(
            title="New Session",
            content="New session started",
            orient=Qt.Horizontal,
            isClosable=True,
            position=InfoBarPosition.TOP,
            duration=3000,
            parent=self
        )

    def update_visual_display(self):
        """Update the visual pattern display"""
        # Load recent data
        data = self.load_data(limit=20)  # Show last 20 records
        if not data or len(data.get('winfail', pd.DataFrame())) == 0:
            InfoBar.warning(
                title="No Data",
                content="No data available for visualization. Please enter some data first.",
                orient=Qt.Horizontal,
                isClosable=True,
                position=InfoBarPosition.TOP,
                duration=3000,
                parent=self
            )
            return

        # Show loading animation
        loading_tooltip = StateToolTip("Visualizing", "Processing data for visualization...", self)
        loading_tooltip.show()

        try:
            # Convert winfail data to format needed by visualizer
            winfail_df = data['winfail']
            visual_data = []

            for _, row in winfail_df.iterrows():
                visual_data.append({
                    'A': row['A'],
                    'B': row['B'],
                    'C': row['C']
                })

            # Update the visualizer with the data
            self.pattern_widget.setData(visual_data)

            # Update statistics labels
            self.update_statistics_labels(data)

            # Show success message
            loading_tooltip.setState(True)
            loading_tooltip.setContent("Visualization updated")
            QTimer.singleShot(1000, loading_tooltip.close)

        except Exception as e:
            # Show error message
            loading_tooltip.setState(False)
            loading_tooltip.setContent(f"Error: {str(e)}")
            QTimer.singleShot(2000, loading_tooltip.close)

            print(f"Error updating visualization: {str(e)}")

    def update_statistics_labels(self, data):
        """Update the statistics labels with pattern data"""
        if not data:
            return

        # Update repeat patterns stats
        if 'repeat_patterns' in self.pattern_memory and self.pattern_memory['repeat_patterns']:
            repeat_stats = []
            for seat, patterns in self.pattern_memory['repeat_patterns'].items():
                avg_length = patterns.get('avg_length', 0)
                total_repeats = patterns.get('total_repeats', 0)
                repeat_stats.append(f"Seat {seat}: Avg {avg_length:.1f} repeats ({total_repeats} total)")

            self.repeat_stats_label.setText("\n".join(repeat_stats))
        else:
            self.repeat_stats_label.setText("No repeat patterns detected yet")

        # Update block patterns stats
        if 'block_patterns' in self.pattern_memory and self.pattern_memory['block_patterns']:
            block_stats = []
            for seat, patterns in self.pattern_memory['block_patterns'].items():
                avg_length = patterns.get('avg_length', 0)
                total_blocks = patterns.get('total_blocks', 0)
                block_stats.append(f"Seat {seat}: Avg {avg_length:.1f} blocks ({total_blocks} total)")

            self.block_stats_label.setText("\n".join(block_stats))
        else:
            self.block_stats_label.setText("No block patterns detected yet")

        # Update visual patterns stats
        if 'visual_patterns' in self.pattern_memory and self.pattern_memory['visual_patterns']:
            visual_stats = []

            diagonals = self.pattern_memory['visual_patterns'].get('diagonals', [])
            if diagonals:
                visual_stats.append(f"Diagonals: {len(diagonals)} detected")

            l_shapes = self.pattern_memory['visual_patterns'].get('l_shapes', [])
            if l_shapes:
                visual_stats.append(f"L-shapes: {len(l_shapes)} detected")

            if visual_stats:
                self.visual_stats_label.setText("\n".join(visual_stats))
            else:
                self.visual_stats_label.setText("No visual patterns detected yet")
        else:
            self.visual_stats_label.setText("No visual patterns detected yet")

    def process_data_queue(self):
        """Process data from the queue (for background tasks)"""
        try:
            while not self.data_queue.empty():
                item = self.data_queue.get_nowait()
                # Process the item
                # ...
        except Exception as e:
            print(f"Error processing queue: {str(e)}")

    def scheduled_update(self):
        """Perform scheduled updates"""
        # Update visual display
        self.update_visual_display()

    def change_theme(self, index):
        """Change application theme"""
        if index == 0:  # Light
            setTheme(Theme.LIGHT)
        elif index == 1:  # Dark
            setTheme(Theme.DARK)
        else:  # Auto
            setTheme(Theme.AUTO)

    def load_data(self, limit=None):
        """Load data from database with optional limit"""
        try:
            conn = sqlite3.connect(DB_PATH)

            # Load backend_data
            query = "SELECT * FROM backend_data ORDER BY id DESC"
            if limit:
                query += f" LIMIT {limit}"
            backend_df = pd.read_sql_query(query, conn)

            # Load winfail data
            winfail_query = "SELECT * FROM winfail ORDER BY id DESC"
            if limit:
                winfail_query += f" LIMIT {limit}"
            winfail_df = pd.read_sql_query(winfail_query, conn)

            # Load seat_changing data
            seat_query = "SELECT * FROM seat_changing ORDER BY id DESC"
            if limit:
                seat_query += f" LIMIT {limit}"
            seat_df = pd.read_sql_query(seat_query, conn)

            conn.close()

            return {
                'backend_data': backend_df,
                'winfail': winfail_df,
                'seat_changing': seat_df
            }
        except Exception as e:
            print(f"Error loading data: {str(e)}")
            return None

    def check_similarity(self, current_data, previous_data):
        """Check if previous data has 80%+ similarity with last 60 bets"""
        # This compares patterns in winfail and seat_changing tables
        if len(current_data) < 60 or len(previous_data) < 60:
            return False

        # Get last 60 records from current data
        current_60 = current_data.iloc[:60]

        # Compare winning seats (winfail table)
        seat_similarity_score = 0
        for i in range(60):
            if i < len(previous_data):
                if current_60.iloc[i]['winning_seat'] == previous_data.iloc[i]['winning_seat']:
                    seat_similarity_score += 1

        seat_similarity_percentage = (seat_similarity_score / 60) * 100

        # For now, we'll use just the seat similarity
        return seat_similarity_percentage >= 80

    def analyze_patterns(self):
        """Analyze patterns in the data for prediction"""
        data = self.load_data(limit=1000)
        if not data or len(data['backend_data']) < 60:
            return

        # Get the data
        backend_df = data['backend_data']
        winfail_df = data['winfail']

        # Analyze repeat patterns
        self.analyze_repeat_patterns(backend_df)

        # Analyze block patterns
        self.analyze_block_patterns(backend_df)

        # Analyze card influence
        self.analyze_card_influence(backend_df)

        # Analyze visual patterns in winfail
        self.analyze_visual_patterns(winfail_df)

        # Save pattern memory
        self.save_pattern_memory()

    def analyze_repeat_patterns(self, df):
        """Analyze patterns of repeats in the data"""
        if len(df) < 10:
            return

        # Get winning seats
        seats = df['winning_seat'].tolist()

        # Find repeats
        repeats = []
        current_repeat = 1

        for i in range(1, len(seats)):
            if seats[i] == seats[i-1]:
                current_repeat += 1
            else:
                if current_repeat > 1:
                    repeats.append((seats[i-1], current_repeat))
                current_repeat = 1

        # Add the last repeat if there is one
        if current_repeat > 1:
            repeats.append((seats[-1], current_repeat))

        # Analyze repeat frequencies
        repeat_counts = {'A': [], 'B': [], 'C': []}
        for seat, count in repeats:
            repeat_counts[seat].append(count)

        # Calculate average repeat lengths
        for seat in repeat_counts:
            if repeat_counts[seat]:
                avg_repeat = sum(repeat_counts[seat]) / len(repeat_counts[seat])
                self.pattern_memory['repeat_patterns'][seat] = {
                    'avg_length': avg_repeat,
                    'counts': repeat_counts[seat],
                    'total_repeats': len(repeat_counts[seat])
                }

    def analyze_block_patterns(self, df):
        """Analyze patterns of blocks (seats not winning for 7+ rounds)"""
        if len(df) < 20:
            return

        # Get winning seats
        seats = df['winning_seat'].tolist()

        # Track last win for each seat
        last_win = {'A': -1, 'B': -1, 'C': -1}
        blocks = {'A': [], 'B': [], 'C': []}

        for i, seat in enumerate(seats):
            # Update last win for current seat
            last_win[seat] = i

            # Check for blocks (7+ rounds without winning)
            for s in ['A', 'B', 'C']:
                if s != seat and i - last_win[s] >= 7:
                    blocks[s].append(i - last_win[s])

        # Store block patterns
        for seat in blocks:
            if blocks[seat]:
                self.pattern_memory['block_patterns'][seat] = {
                    'avg_length': sum(blocks[seat]) / len(blocks[seat]) if blocks[seat] else 0,
                    'counts': blocks[seat],
                    'total_blocks': len(blocks[seat])
                }

    def analyze_card_influence(self, df):
        """Analyze how cards influence winning seats"""
        if len(df) < 20:
            return

        # Look for patterns in card combinations
        card_wins = {}

        for _, row in df.iterrows():
            seat = row['winning_seat']
            cards = (row['card1'], row['card2'], row['card3'])
            trend = row['trend']

            # Track wins by card combination
            key = str(cards)
            if key not in card_wins:
                card_wins[key] = {'A': 0, 'B': 0, 'C': 0}
            card_wins[key][seat] += 1

            # Track wins by trend
            if trend not in self.pattern_memory['card_influence']:
                self.pattern_memory['card_influence'][trend] = {'A': 0, 'B': 0, 'C': 0}
            self.pattern_memory['card_influence'][trend][seat] += 1

        # Find significant card combinations
        significant_combos = {}
        for combo, wins in card_wins.items():
            total = sum(wins.values())
            if total >= 3:  # Only consider combinations that appear at least 3 times
                max_seat = max(wins, key=wins.get)
                max_pct = (wins[max_seat] / total) * 100
                if max_pct >= 70:  # Only consider combinations with strong bias
                    significant_combos[combo] = {
                        'seat': max_seat,
                        'confidence': max_pct,
                        'occurrences': total
                    }

        self.pattern_memory['card_influence']['significant_combos'] = significant_combos

    def analyze_visual_patterns(self, df):
        """Analyze visual patterns in the winfail table"""
        if len(df) < 20:
            return

        # Look for diagonal patterns
        diagonals = []
        for i in range(len(df) - 3):
            # Check for diagonal A -> B -> C
            if (df.iloc[i]['A'] == 'Win' and
                df.iloc[i+1]['B'] == 'Win' and
                df.iloc[i+2]['C'] == 'Win'):
                diagonals.append(('A->B->C', i))

            # Check for diagonal C -> B -> A
            if (df.iloc[i]['C'] == 'Win' and
                df.iloc[i+1]['B'] == 'Win' and
                df.iloc[i+2]['A'] == 'Win'):
                diagonals.append(('C->B->A', i))

        # Look for L shapes
        l_shapes = []
        for i in range(len(df) - 3):
            # Check for L shape A -> A -> B
            if (df.iloc[i]['A'] == 'Win' and
                df.iloc[i+1]['A'] == 'Win' and
                df.iloc[i+2]['B'] == 'Win'):
                l_shapes.append(('A->A->B', i))

        # Store visual patterns
        self.pattern_memory['visual_patterns'] = {
            'diagonals': diagonals,
            'l_shapes': l_shapes
        }

    def train_models(self):
        """Train ML models on the data"""
        data = self.load_data(limit=1000)  # Load last 1000 records
        if not data or len(data['backend_data']) < 60:
            print("Not enough data for training")
            return False

        # First analyze patterns in the data
        self.analyze_patterns()

        # Prepare features and target
        df = data['backend_data']

        # Feature engineering with advanced pattern recognition
        features = []
        for i in range(len(df) - 60):
            window = df.iloc[i:i+60]

            # Basic features
            feature_row = [
                # Count of each seat in the window
                sum(window['winning_seat'] == 'A'),
                sum(window['winning_seat'] == 'B'),
                sum(window['winning_seat'] == 'C'),

                # Last 5 winning seats (more context)
                *[1 if s == 'A' else (2 if s == 'B' else 3) for s in window.iloc[:5]['winning_seat']],

                # Repeat features
                # Is the last win a repeat?
                1 if i >= 1 and window.iloc[0]['winning_seat'] == window.iloc[1]['winning_seat'] else 0,

                # How many repeats in the last 10 bets?
                sum(1 for j in range(1, min(10, len(window))) if window.iloc[j-1]['winning_seat'] == window.iloc[j]['winning_seat']),

                # Block features
                # How many bets since each seat last won
                i - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'A'] + [-1]),
                i - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'B'] + [-1]),
                i - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'C'] + [-1]),
            ]

            # Card features
            # Add the trend of the last winning hand
            trends = ['High Card', 'Pair', 'Sequence', 'Colour', 'Pure Sequence', 'Tilt']
            for trend in trends:
                feature_row.append(1 if window.iloc[0]['trend'] == trend else 0)

            features.append(feature_row)

        # Target is the next winning seat
        targets = [1 if s == 'A' else (2 if s == 'B' else 3) for s in df.iloc[60:60+len(features)]['winning_seat']]

        if len(features) < 10 or len(targets) < 10:
            print("Not enough processed data for training")
            return False

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(features, targets, test_size=0.2, random_state=42)

        # Scale features
        scaler = StandardScaler()
        X_train = scaler.fit_transform(X_train)
        X_test = scaler.transform(X_test)

        # Train models with more advanced configurations
        models = {
            'random_forest': RandomForestClassifier(
                n_estimators=200,
                max_depth=None,
                min_samples_split=2,
                min_samples_leaf=1,
                random_state=42
            ),
            'gradient_boosting': GradientBoostingClassifier(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=5,
                random_state=42
            ),
            'neural_network': MLPClassifier(
                hidden_layer_sizes=(200, 100, 50),
                activation='relu',
                solver='adam',
                alpha=0.0001,
                batch_size='auto',
                learning_rate='adaptive',
                max_iter=2000,
                random_state=42
            )
        }

        # Train and evaluate models
        model_accuracies = {}
        for name, model in models.items():
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            model_accuracies[name] = accuracy
            print(f"{name} accuracy: {accuracy:.4f}")

        # Save the best model
        best_model_name = max(model_accuracies, key=model_accuracies.get)
        self.best_model = models[best_model_name]
        self.scaler = scaler
        self.feature_format = len(features[0])  # Save feature format for prediction

        # Save pattern memory with model info
        self.pattern_memory['best_model'] = {
            'name': best_model_name,
            'accuracy': model_accuracies[best_model_name],
            'feature_count': len(features[0])
        }
        self.save_pattern_memory()

        return True

    def predict_next_win(self):
        """Predict the next winning seat"""
        data = self.load_data(limit=1000)
        if not data or len(data['backend_data']) < 60:
            return None, None, 0

        df = data['backend_data']

        # Prepare features for prediction (same as in training)
        window = df.iloc[:60]

        # Basic features
        features = [
            # Count of each seat in the window
            sum(window['winning_seat'] == 'A'),
            sum(window['winning_seat'] == 'B'),
            sum(window['winning_seat'] == 'C'),

            # Last 5 winning seats (more context)
            *[1 if s == 'A' else (2 if s == 'B' else 3) for s in window.iloc[:5]['winning_seat']],

            # Repeat features
            # Is the last win a repeat?
            1 if len(window) >= 2 and window.iloc[0]['winning_seat'] == window.iloc[1]['winning_seat'] else 0,

            # How many repeats in the last 10 bets?
            sum(1 for j in range(1, min(10, len(window))) if window.iloc[j-1]['winning_seat'] == window.iloc[j]['winning_seat']),

            # Block features
            # How many bets since each seat last won
            0 - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'A'] + [-1]),
            0 - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'B'] + [-1]),
            0 - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'C'] + [-1]),
        ]

        # Card features
        # Add the trend of the last winning hand
        trends = ['High Card', 'Pair', 'Sequence', 'Colour', 'Pure Sequence', 'Tilt']
        for trend in trends:
            features.append(1 if window.iloc[0]['trend'] == trend else 0)

        # Check if we have the right number of features
        if hasattr(self, 'feature_format') and len(features) != self.feature_format:
            print(f"Feature mismatch: expected {self.feature_format}, got {len(features)}")
            # Pad with zeros if needed
            while len(features) < self.feature_format:
                features.append(0)
            # Truncate if too many
            if len(features) > self.feature_format:
                features = features[:self.feature_format]

        # Scale features
        if hasattr(self, 'scaler') and hasattr(self, 'best_model'):
            features = self.scaler.transform([features])
            prediction = self.best_model.predict(features)[0]
            probabilities = self.best_model.predict_proba(features)[0]

            # Convert prediction to seat
            primary_seat = 'A' if prediction == 1 else ('B' if prediction == 2 else 'C')

            # Get second highest probability for secondary prediction
            sorted_probs = sorted(enumerate(probabilities), key=lambda x: x[1], reverse=True)
            secondary_idx = sorted_probs[1][0] + 1  # +1 because our classes are 1, 2, 3
            secondary_seat = 'A' if secondary_idx == 1 else ('B' if secondary_idx == 2 else 'C')

            # Calculate confidence
            confidence = probabilities[prediction-1] * 100

            # Apply special rules from the requirements

            # Check for repeat pattern
            last_seat = window.iloc[0]['winning_seat']
            repeat_confidence = self.calculate_repeat_confidence(window)

            # Rule: If repeat is highly confident (90%+ confirmed) → make repeat seat primary
            if repeat_confidence >= 90:
                primary_seat = last_seat
                # Select next best seat as secondary
                if primary_seat == secondary_seat:
                    # Find the third best seat
                    tertiary_idx = sorted_probs[2][0] + 1
                    secondary_seat = 'A' if tertiary_idx == 1 else ('B' if tertiary_idx == 2 else 'C')

            # Rule: If repeat is uncertain (below 90%) → make repeat seat secondary
            elif repeat_confidence > 0:
                if primary_seat != last_seat:
                    secondary_seat = last_seat

            # Check for blocks (seats that haven't won for 7+ rounds)
            blocks = self.detect_blocks(window)
            if blocks:
                # Avoid predicting blocked seats if possible
                if primary_seat in blocks and secondary_seat not in blocks:
                    # Swap primary and secondary
                    primary_seat, secondary_seat = secondary_seat, primary_seat
                elif primary_seat in blocks and secondary_seat in blocks:
                    # Find a non-blocked seat
                    available_seats = [s for s in ['A', 'B', 'C'] if s not in blocks]
                    if available_seats:
                        primary_seat = available_seats[0]

            return primary_seat, secondary_seat, confidence
        else:
            return None, None, 0

    def calculate_repeat_confidence(self, window):
        """Calculate confidence in a repeat pattern"""
        if len(window) < 10:
            return 0

        last_seat = window.iloc[0]['winning_seat']

        # Check if we're in a repeat streak
        current_streak = 1
        for i in range(1, len(window)):
            if window.iloc[i]['winning_seat'] == last_seat:
                current_streak += 1
            else:
                break

        # If already in a streak, higher chance of continuing
        if current_streak > 1:
            # Look at historical data for this seat
            if last_seat in self.pattern_memory['repeat_patterns']:
                pattern = self.pattern_memory['repeat_patterns'][last_seat]
                avg_length = pattern.get('avg_length', 0)

                # If current streak is less than average, higher chance of continuing
                if current_streak < avg_length:
                    return min(95, 70 + (current_streak * 5))
                else:
                    # Decreasing chance as we exceed average
                    return max(30, 90 - ((current_streak - avg_length) * 10))

            # No historical data, use heuristic
            return min(90, 50 + (current_streak * 10))

        # Not in a streak, check for patterns that lead to repeats
        # This would be more sophisticated in a full implementation
        return 30  # Base chance of repeat

    def detect_blocks(self, window):
        """Detect seats that are in a block (7+ rounds without winning)"""
        if len(window) < 7:
            return []

        blocks = []
        last_win = {'A': -1, 'B': -1, 'C': -1}

        # Find last win for each seat
        for i, row in window.iterrows():
            seat = row['winning_seat']
            if last_win[seat] == -1:
                last_win[seat] = i

        # Check for blocks
        for seat, idx in last_win.items():
            if idx == -1 or idx >= 7:  # Never won or won more than 7 bets ago
                blocks.append(seat)

        return blocks


# Main execution
if __name__ == "__main__":
    try:
        # Create application
        app = QApplication(sys.argv)

        # Set application style
        app.setStyle('Fusion')

        # Create and show main window
        window = PolitePredict()
        window.show()

        # Start event loop
        sys.exit(app.exec_())
    except Exception as e:
        print(f"Error starting application: {str(e)}")
        import traceback
        traceback.print_exc()
