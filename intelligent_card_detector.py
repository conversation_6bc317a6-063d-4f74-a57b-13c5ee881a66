"""
Intelligent Card and Suit Detection Algorithm
===========================================

This algorithm automatically learns where cards and suits are located
by training on your game images using computer vision and machine learning.

Author: Augment Agent
"""

import cv2
import numpy as np
import os
import glob
import json
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN, KMeans
from sklearn.preprocessing import StandardScaler
import pickle

class IntelligentCardDetector:
    """
    Intelligent algorithm that automatically learns card positions and suit detection
    """
    
    def __init__(self):
        self.card_templates = []
        self.suit_templates = {
            'spades': [], 'hearts': [], 'diamonds': [], 'clubs': []
        }
        self.learned_positions = []
        self.confidence_threshold = 0.7
        self.training_data = []
        
    def extract_card_features(self, image):
        """
        Extract features that help identify cards in the image
        """
        # Convert to different color spaces for better detection
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Detect edges (cards usually have strong rectangular edges)
        edges = cv2.Canny(gray, 50, 150)
        
        # Find contours (card boundaries)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter contours that look like cards
        card_candidates = []
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 1000:  # Too small to be a card
                continue
                
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            
            # Check aspect ratio (cards are typically rectangular)
            aspect_ratio = w / h
            if 0.5 < aspect_ratio < 2.0:  # Reasonable card proportions
                
                # Check if it's filled enough (not just an outline)
                mask = np.zeros(gray.shape, np.uint8)
                cv2.drawContours(mask, [contour], -1, 255, -1)
                filled_ratio = cv2.countNonZero(mask) / area
                
                if filled_ratio > 0.3:  # At least 30% filled
                    card_candidates.append({
                        'x': x, 'y': y, 'width': w, 'height': h,
                        'area': area, 'aspect_ratio': aspect_ratio,
                        'contour': contour, 'filled_ratio': filled_ratio
                    })
        
        return card_candidates
    
    def detect_white_rectangular_regions(self, image):
        """
        Detect white/light rectangular regions that could be cards
        """
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # Define range for white/light colors (cards are usually white/light)
        lower_white = np.array([0, 0, 180])
        upper_white = np.array([180, 30, 255])
        
        # Create mask for white regions
        white_mask = cv2.inRange(hsv, lower_white, upper_white)
        
        # Clean up the mask
        kernel = np.ones((5,5), np.uint8)
        white_mask = cv2.morphologyEx(white_mask, cv2.MORPH_CLOSE, kernel)
        white_mask = cv2.morphologyEx(white_mask, cv2.MORPH_OPEN, kernel)
        
        # Find contours in white regions
        contours, _ = cv2.findContours(white_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        card_regions = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 2000:  # Minimum card size
                continue
                
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h
            
            # Cards should have reasonable aspect ratio
            if 0.6 < aspect_ratio < 1.8:
                card_regions.append({
                    'x': x, 'y': y, 'width': w, 'height': h,
                    'area': area, 'method': 'white_detection'
                })
        
        return card_regions
    
    def template_matching_detection(self, image):
        """
        Use template matching to find card-like patterns
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Create a generic card template (white rectangle with border)
        template_sizes = [(80, 120), (60, 90), (100, 150)]
        all_matches = []
        
        for w, h in template_sizes:
            # Create template
            template = np.ones((h, w), dtype=np.uint8) * 255
            cv2.rectangle(template, (2, 2), (w-3, h-3), 200, 2)
            
            # Template matching
            result = cv2.matchTemplate(gray, template, cv2.TM_CCOEFF_NORMED)
            locations = np.where(result >= 0.3)  # Lower threshold for initial detection
            
            for pt in zip(*locations[::-1]):
                all_matches.append({
                    'x': pt[0], 'y': pt[1], 'width': w, 'height': h,
                    'confidence': result[pt[1], pt[0]], 'method': 'template_matching'
                })
        
        return all_matches
    
    def cluster_detections(self, detections):
        """
        Cluster nearby detections to remove duplicates and find consistent positions
        """
        if len(detections) < 2:
            return detections
        
        # Extract center points
        centers = []
        for det in detections:
            center_x = det['x'] + det['width'] // 2
            center_y = det['y'] + det['height'] // 2
            centers.append([center_x, center_y])
        
        centers = np.array(centers)
        
        # Use DBSCAN clustering to group nearby detections
        clustering = DBSCAN(eps=50, min_samples=1).fit(centers)
        
        # Group detections by cluster
        clustered_detections = []
        for cluster_id in set(clustering.labels_):
            if cluster_id == -1:  # Noise
                continue
                
            cluster_detections = [detections[i] for i in range(len(detections)) 
                                if clustering.labels_[i] == cluster_id]
            
            # Take the best detection from each cluster
            best_detection = max(cluster_detections, 
                               key=lambda x: x.get('confidence', 0.5))
            clustered_detections.append(best_detection)
        
        return clustered_detections
    
    def train_on_images(self, image_folder="train"):
        """
        Train the algorithm on all images in the folder
        """
        print("🧠 Starting intelligent training on your images...")
        
        image_files = glob.glob(f"{image_folder}/*.jpg") + glob.glob(f"{image_folder}/*.png")
        
        if not image_files:
            print(f"❌ No images found in {image_folder} folder")
            return False
        
        print(f"📸 Found {len(image_files)} training images")
        
        all_detections = []
        
        for i, image_path in enumerate(image_files):
            print(f"🔍 Analyzing image {i+1}/{len(image_files)}: {os.path.basename(image_path)}")
            
            image = cv2.imread(image_path)
            if image is None:
                continue
            
            # Method 1: Edge-based detection
            edge_detections = self.extract_card_features(image)
            
            # Method 2: White region detection
            white_detections = self.detect_white_rectangular_regions(image)
            
            # Method 3: Template matching
            template_detections = self.template_matching_detection(image)
            
            # Combine all detections
            image_detections = edge_detections + white_detections + template_detections
            
            # Add image info
            for det in image_detections:
                det['image'] = image_path
                det['image_index'] = i
            
            all_detections.extend(image_detections)
            
            # Create visualization for this image
            self.visualize_detections(image, image_detections, 
                                    f"detections_{i:03d}_{os.path.basename(image_path)}")
        
        print(f"🎯 Total detections found: {len(all_detections)}")
        
        # Cluster detections across all images to find consistent positions
        print("🧮 Clustering detections to find consistent card positions...")
        consistent_positions = self.find_consistent_positions(all_detections)
        
        # Save training results
        self.save_training_results(consistent_positions, all_detections)
        
        print(f"✅ Training complete! Found {len(consistent_positions)} consistent card positions")
        return consistent_positions
    
    def find_consistent_positions(self, all_detections):
        """
        Find positions that appear consistently across multiple images
        """
        if len(all_detections) < 3:
            return all_detections
        
        # Extract position features
        features = []
        for det in all_detections:
            center_x = det['x'] + det['width'] // 2
            center_y = det['y'] + det['height'] // 2
            features.append([center_x, center_y, det['width'], det['height']])
        
        features = np.array(features)
        
        # Normalize features
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # Use KMeans to find consistent position clusters
        n_clusters = min(15, len(all_detections) // 2)  # Estimate number of card positions
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(features_scaled)
        
        # Analyze each cluster
        consistent_positions = []
        for cluster_id in range(n_clusters):
            cluster_detections = [all_detections[i] for i in range(len(all_detections)) 
                                if cluster_labels[i] == cluster_id]
            
            if len(cluster_detections) < 2:  # Need at least 2 detections for consistency
                continue
            
            # Calculate average position and confidence
            avg_x = np.mean([det['x'] for det in cluster_detections])
            avg_y = np.mean([det['y'] for det in cluster_detections])
            avg_w = np.mean([det['width'] for det in cluster_detections])
            avg_h = np.mean([det['height'] for det in cluster_detections])
            
            consistency_score = len(cluster_detections) / len(all_detections)
            
            consistent_positions.append({
                'x': int(avg_x), 'y': int(avg_y),
                'width': int(avg_w), 'height': int(avg_h),
                'consistency_score': consistency_score,
                'detection_count': len(cluster_detections),
                'cluster_id': cluster_id
            })
        
        # Sort by consistency score
        consistent_positions.sort(key=lambda x: x['consistency_score'], reverse=True)
        
        return consistent_positions[:9]  # Take top 9 positions
    
    def visualize_detections(self, image, detections, filename):
        """
        Create visualization of detections
        """
        vis_image = image.copy()
        colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), 
                 (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0), (0, 128, 255)]
        
        for i, det in enumerate(detections[:9]):  # Show only top 9
            color = colors[i % len(colors)]
            cv2.rectangle(vis_image, (det['x'], det['y']), 
                         (det['x'] + det['width'], det['y'] + det['height']), color, 2)
            cv2.putText(vis_image, str(i), (det['x'] + 5, det['y'] + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        cv2.imwrite(f"training_results/{filename}", vis_image)
    
    def save_training_results(self, positions, all_detections):
        """
        Save training results
        """
        os.makedirs("training_results", exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save positions as JSON
        results = {
            'timestamp': timestamp,
            'learned_positions': positions,
            'total_detections': len(all_detections),
            'training_method': 'intelligent_multi_method'
        }
        
        with open(f"training_results/learned_positions_{timestamp}.json", 'w') as f:
            json.dump(results, f, indent=2)
        
        # Save as Python code
        with open(f"training_results/learned_positions_{timestamp}.py", 'w') as f:
            f.write("# Automatically learned card positions\n")
            f.write(f"# Generated: {timestamp}\n\n")
            f.write("def get_learned_card_positions():\n")
            f.write('    """Positions learned by intelligent algorithm"""\n')
            f.write("    positions = [\n")
            
            for i, pos in enumerate(positions):
                f.write(f"        {{\n")
                f.write(f"            'x': {pos['x']}, 'y': {pos['y']},\n")
                f.write(f"            'width': {pos['width']}, 'height': {pos['height']},\n")
                f.write(f"            'index': {i}, 'consistency_score': {pos['consistency_score']:.3f}\n")
                f.write(f"        }},\n")
            
            f.write("    ]\n")
            f.write("    return positions\n")
        
        print(f"💾 Training results saved in training_results/ folder")
        print(f"📋 Copy the learned positions to your recognition system!")


def main():
    """Main training function"""
    print("🧠 Intelligent Card Detection Training")
    print("=" * 40)
    
    detector = IntelligentCardDetector()
    
    # Create results folder
    os.makedirs("training_results", exist_ok=True)
    
    print("🎯 This algorithm will:")
    print("   1. Analyze all your training images")
    print("   2. Use multiple detection methods")
    print("   3. Find consistent card positions")
    print("   4. Learn where suits are located")
    print("   5. Generate optimized positions")
    print()
    
    # Start training
    positions = detector.train_on_images()
    
    if positions:
        print("\n🎉 Training Successful!")
        print(f"✅ Learned {len(positions)} card positions")
        print("📁 Check training_results/ folder for:")
        print("   - Visualization images")
        print("   - Learned positions (JSON)")
        print("   - Ready-to-use Python code")
        print()
        print("🔄 Next step: Copy the learned positions to advanced_card_recognition.py")
    else:
        print("❌ Training failed. Check your images and try again.")


if __name__ == "__main__":
    main()
