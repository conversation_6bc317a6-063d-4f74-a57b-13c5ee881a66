#!/usr/bin/env python3
"""
Tesseract OCR Setup Script for PolitePredict
This script helps install and configure Tesseract OCR for the application.
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil

def check_tesseract():
    """Check if Tesseract is already installed and accessible"""
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Tesseract is already installed!")
            print(f"Version: {result.stdout.split()[1]}")
            return True
    except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    print("❌ Tesseract not found or not accessible")
    return False

def install_tesseract_windows():
    """Install Tesseract on Windows"""
    print("🔧 Installing Tesseract for Windows...")
    
    # Download URL for Tesseract Windows installer
    tesseract_url = "https://github.com/UB-<PERSON>heim/tesseract/releases/download/v5.3.3.20231005/tesseract-ocr-w64-setup-5.3.3.20231005.exe"
    installer_path = "tesseract_installer.exe"
    
    try:
        print("📥 Downloading Tesseract installer...")
        urllib.request.urlretrieve(tesseract_url, installer_path)
        
        print("🚀 Running installer...")
        print("⚠️  Please follow the installation wizard and note the installation path!")
        subprocess.run([installer_path], check=True)
        
        # Clean up
        if os.path.exists(installer_path):
            os.remove(installer_path)
        
        # Common installation paths
        common_paths = [
            r"C:\Program Files\Tesseract-OCR",
            r"C:\Program Files (x86)\Tesseract-OCR",
            r"C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR"
        ]
        
        print("\n📝 Adding Tesseract to PATH...")
        print("If the installer didn't add Tesseract to PATH automatically, please:")
        print("1. Open System Properties > Environment Variables")
        print("2. Add one of these paths to your PATH variable:")
        for path in common_paths:
            print(f"   - {path}")
        print("3. Restart your command prompt/IDE")
        
        return True
        
    except Exception as e:
        print(f"❌ Error installing Tesseract: {e}")
        return False

def install_tesseract_mac():
    """Install Tesseract on macOS"""
    print("🔧 Installing Tesseract for macOS...")
    
    try:
        # Check if Homebrew is installed
        subprocess.run(['brew', '--version'], capture_output=True, check=True)
        print("📦 Installing via Homebrew...")
        subprocess.run(['brew', 'install', 'tesseract'], check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Homebrew not found. Please install Homebrew first:")
        print("   /bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"")
        print("   Then run: brew install tesseract")
        return False

def install_tesseract_linux():
    """Install Tesseract on Linux"""
    print("🔧 Installing Tesseract for Linux...")
    
    try:
        # Try apt-get (Ubuntu/Debian)
        subprocess.run(['sudo', 'apt-get', 'update'], check=True)
        subprocess.run(['sudo', 'apt-get', 'install', '-y', 'tesseract-ocr'], check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        try:
            # Try yum (CentOS/RHEL)
            subprocess.run(['sudo', 'yum', 'install', '-y', 'tesseract'], check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Could not install automatically. Please install manually:")
            print("   Ubuntu/Debian: sudo apt-get install tesseract-ocr")
            print("   CentOS/RHEL: sudo yum install tesseract")
            print("   Arch: sudo pacman -S tesseract")
            return False

def setup_tesseract():
    """Main setup function"""
    print("🎯 PolitePredict Tesseract OCR Setup")
    print("=" * 40)
    
    # Check if already installed
    if check_tesseract():
        print("✅ Setup complete! Tesseract is ready to use.")
        return True
    
    # Detect OS and install
    system = platform.system().lower()
    
    if system == "windows":
        success = install_tesseract_windows()
    elif system == "darwin":  # macOS
        success = install_tesseract_mac()
    elif system == "linux":
        success = install_tesseract_linux()
    else:
        print(f"❌ Unsupported operating system: {system}")
        return False
    
    if success:
        print("\n🔄 Verifying installation...")
        if check_tesseract():
            print("✅ Setup complete! Tesseract is ready to use.")
            print("\n🚀 You can now run PolitePredict with OCR support!")
            return True
        else:
            print("⚠️  Installation completed but Tesseract is not accessible.")
            print("   You may need to restart your terminal/IDE or add Tesseract to PATH manually.")
            return False
    else:
        print("❌ Setup failed. Please install Tesseract manually.")
        return False

def create_fallback_ocr():
    """Create a fallback OCR function for when Tesseract is not available"""
    fallback_code = '''
def fallback_ocr_extract(image_path):
    """Fallback OCR when Tesseract is not available"""
    print("⚠️  Tesseract not available, using fallback mode")
    
    # Return dummy data for testing
    return {
        'winning_seat': 'A',  # Default to A
        'cards': [
            {'value': '10', 'suit_name': 'Spade', 'color': 'Black'},
            {'value': 'J', 'suit_name': 'Heart', 'color': 'Red'},
            {'value': 'Q', 'suit_name': 'Club', 'color': 'Black'}
        ],
        'trend': 'High Card',
        'error': 'Tesseract not available - using fallback data'
    }
'''
    
    with open('fallback_ocr.py', 'w') as f:
        f.write(fallback_code)
    
    print("📝 Created fallback_ocr.py for testing without Tesseract")

if __name__ == "__main__":
    print("🎮 Welcome to PolitePredict OCR Setup!")
    print("\nThis script will help you install Tesseract OCR for the application.")
    
    choice = input("\nDo you want to proceed with Tesseract installation? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        success = setup_tesseract()
        if not success:
            print("\n🔧 Would you like to create a fallback OCR for testing?")
            fallback_choice = input("This allows the app to run without Tesseract (y/n): ").lower().strip()
            if fallback_choice in ['y', 'yes']:
                create_fallback_ocr()
    else:
        print("🔧 Creating fallback OCR for testing without Tesseract...")
        create_fallback_ocr()
    
    print("\n📚 For more information, see:")
    print("   - Tesseract GitHub: https://github.com/tesseract-ocr/tesseract")
    print("   - Windows installer: https://github.com/UB-Mannheim/tesseract/releases")
    print("   - PolitePredict documentation: README.md")
