"""
Card Suit Recognition System
============================

This system learns to recognize card suits (♠, ♣, ♦, ♥) from unlabeled images
using unsupervised learning and computer vision techniques.

Author: Augment Agent
"""

import cv2
import numpy as np
import os
import glob
from datetime import datetime
import pickle
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from skimage.feature import hog, local_binary_pattern
from skimage.measure import regionprops
import matplotlib.pyplot as plt
from collections import Counter
import time

class CardSuitRecognizer:
    """
    Recognizes card suits using unsupervised learning
    """
    
    def __init__(self, save_path="card_recognition/"):
        self.save_path = save_path
        os.makedirs(save_path, exist_ok=True)
        
        # Card detection parameters
        self.card_positions = self.calculate_card_positions()
        
        # Feature extractors
        self.suit_clusters = None
        self.scaler = StandardScaler()
        self.pca = PCA(n_components=20)
        
        # Suit vocabulary
        self.suit_vocabulary = {}
        self.learned_patterns = {}
        
        # Recognition parameters
        self.min_card_area = 500
        self.max_card_area = 5000
        
    def calculate_card_positions(self):
        """
        Calculate expected positions of 9 cards in 3x3 grid
        Based on your game layout
        """
        # These coordinates should be adjusted based on your actual game layout
        positions = []
        
        # Assuming image size around 380x244 (from crop box)
        card_width = 100
        card_height = 80
        start_x = 40
        start_y = 30
        spacing_x = 120
        spacing_y = 90
        
        for row in range(3):
            for col in range(3):
                x = start_x + col * spacing_x
                y = start_y + row * spacing_y
                positions.append({
                    'x': x,
                    'y': y,
                    'width': card_width,
                    'height': card_height,
                    'position': row * 3 + col  # 0-8
                })
        
        return positions
    
    def extract_cards_from_image(self, image):
        """
        Extract individual card regions from the game image
        """
        cards = []
        
        for i, pos in enumerate(self.card_positions):
            # Extract card region
            x, y, w, h = pos['x'], pos['y'], pos['width'], pos['height']
            
            # Ensure coordinates are within image bounds
            x = max(0, min(x, image.shape[1] - w))
            y = max(0, min(y, image.shape[0] - h))
            w = min(w, image.shape[1] - x)
            h = min(h, image.shape[0] - y)
            
            card_region = image[y:y+h, x:x+w]
            
            if card_region.size > 0:
                cards.append({
                    'image': card_region,
                    'position': i,
                    'coordinates': (x, y, w, h)
                })
        
        return cards
    
    def extract_suit_features(self, card_image):
        """
        Extract features that help identify card suits
        """
        if card_image.size == 0:
            return np.zeros(50)  # Return zero vector for empty images
        
        # Convert to grayscale
        if len(card_image.shape) == 3:
            gray = cv2.cvtColor(card_image, cv2.COLOR_BGR2GRAY)
        else:
            gray = card_image
        
        features = []
        
        # 1. Shape-based features using contours
        # Apply threshold to isolate suit symbols
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Extract shape features from largest contours
        shape_features = []
        for contour in sorted(contours, key=cv2.contourArea, reverse=True)[:3]:
            if cv2.contourArea(contour) > 50:  # Filter small noise
                # Geometric features
                area = cv2.contourArea(contour)
                perimeter = cv2.arcLength(contour, True)
                
                if perimeter > 0:
                    circularity = 4 * np.pi * area / (perimeter * perimeter)
                    
                    # Aspect ratio
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h if h > 0 else 0
                    
                    # Extent
                    extent = area / (w * h) if w * h > 0 else 0
                    
                    # Solidity
                    hull = cv2.convexHull(contour)
                    hull_area = cv2.contourArea(hull)
                    solidity = area / hull_area if hull_area > 0 else 0
                    
                    shape_features.extend([area, perimeter, circularity, aspect_ratio, extent, solidity])
        
        # Pad or truncate to fixed size
        shape_features = shape_features[:18]  # Max 3 contours * 6 features
        while len(shape_features) < 18:
            shape_features.append(0)
        
        features.extend(shape_features)
        
        # 2. Texture features using LBP
        lbp = local_binary_pattern(gray, 8, 1, method='uniform')
        lbp_hist, _ = np.histogram(lbp.ravel(), bins=10, range=(0, 10))
        lbp_hist = lbp_hist / (lbp_hist.sum() + 1e-8)  # Normalize
        features.extend(lbp_hist)
        
        # 3. HOG features for shape detection
        try:
            hog_features = hog(gray, orientations=8, pixels_per_cell=(8, 8),
                              cells_per_block=(1, 1), feature_vector=True)
            # Take first 12 HOG features
            features.extend(hog_features[:12])
        except:
            features.extend([0] * 12)
        
        # 4. Color-based features (if color image)
        if len(card_image.shape) == 3:
            # Red vs Black detection
            hsv = cv2.cvtColor(card_image, cv2.COLOR_BGR2HSV)
            
            # Red mask (hearts and diamonds)
            red_mask1 = cv2.inRange(hsv, (0, 50, 50), (10, 255, 255))
            red_mask2 = cv2.inRange(hsv, (170, 50, 50), (180, 255, 255))
            red_mask = red_mask1 + red_mask2
            
            # Black mask (spades and clubs)
            black_mask = cv2.inRange(hsv, (0, 0, 0), (180, 255, 50))
            
            red_ratio = np.sum(red_mask > 0) / (card_image.shape[0] * card_image.shape[1])
            black_ratio = np.sum(black_mask > 0) / (card_image.shape[0] * card_image.shape[1])
            
            features.extend([red_ratio, black_ratio])
        else:
            features.extend([0, 0])
        
        # Ensure fixed feature vector size
        features = features[:50]
        while len(features) < 50:
            features.append(0)
        
        return np.array(features, dtype=np.float64)
    
    def learn_suit_patterns(self, images):
        """
        Learn suit patterns from unlabeled images
        """
        print("🃏 Learning card suit patterns...")
        
        all_card_features = []
        card_positions = []
        
        for img_idx, image in enumerate(images):
            if img_idx % 20 == 0:
                print(f"   Processing image {img_idx+1}/{len(images)}")
            
            # Extract cards from image
            cards = self.extract_cards_from_image(image)
            
            for card in cards:
                # Extract features for each card
                features = self.extract_suit_features(card['image'])
                all_card_features.append(features)
                card_positions.append({
                    'image_idx': img_idx,
                    'position': card['position'],
                    'features': features
                })
        
        print(f"📊 Extracted features from {len(all_card_features)} cards")
        
        if len(all_card_features) < 20:
            print("❌ Not enough card data for learning")
            return False
        
        # Convert to array
        all_card_features = np.array(all_card_features)
        
        # Scale features
        scaled_features = self.scaler.fit_transform(all_card_features)
        
        # Apply PCA for dimensionality reduction
        pca_features = self.pca.fit_transform(scaled_features)
        
        # Cluster into 4 groups (for 4 suits)
        self.suit_clusters = KMeans(n_clusters=4, random_state=42, n_init=10)
        cluster_labels = self.suit_clusters.fit_predict(pca_features)
        
        # Analyze clusters
        print("🔍 Analyzing learned suit patterns...")
        
        cluster_analysis = {}
        for i in range(4):
            cluster_mask = cluster_labels == i
            cluster_cards = np.array(card_positions)[cluster_mask]
            
            cluster_analysis[i] = {
                'count': np.sum(cluster_mask),
                'positions': [card['position'] for card in cluster_cards],
                'avg_features': np.mean(all_card_features[cluster_mask], axis=0)
            }
            
            print(f"   Cluster {i}: {cluster_analysis[i]['count']} cards")
        
        # Build suit vocabulary
        self.suit_vocabulary = {
            'clusters': cluster_analysis,
            'total_cards': len(all_card_features),
            'feature_scaler': self.scaler,
            'pca': self.pca,
            'kmeans': self.suit_clusters
        }
        
        # Save learned patterns
        self.save_suit_learning()
        
        print("✅ Suit pattern learning completed!")
        return True
    
    def predict_card_suits(self, image):
        """
        Predict suits for all 9 cards in the image
        """
        if self.suit_clusters is None:
            print("❌ No suit patterns learned. Run learning first.")
            return None
        
        # Extract cards
        cards = self.extract_cards_from_image(image)
        
        predictions = []
        
        for card in cards:
            # Extract features
            features = self.extract_suit_features(card['image'])
            
            # Scale and transform
            scaled_features = self.scaler.transform([features])
            pca_features = self.pca.transform(scaled_features)
            
            # Predict cluster
            cluster = self.suit_clusters.predict(pca_features)[0]
            
            # Map cluster to suit (this is learned automatically)
            suit_names = ['♠', '♣', '♦', '♥']  # Will be mapped based on learning
            predicted_suit = suit_names[cluster]
            
            predictions.append({
                'position': card['position'],
                'suit': predicted_suit,
                'cluster': cluster,
                'confidence': self.calculate_prediction_confidence(pca_features[0], cluster)
            })
        
        return predictions
    
    def calculate_prediction_confidence(self, features, predicted_cluster):
        """
        Calculate confidence of prediction based on distance to cluster center
        """
        try:
            cluster_center = self.suit_clusters.cluster_centers_[predicted_cluster]
            distance = np.linalg.norm(features - cluster_center)
            
            # Convert distance to confidence (0-100)
            max_distance = 5.0  # Adjust based on your data
            confidence = max(0, 100 - (distance / max_distance) * 100)
            return min(100, confidence)
        except:
            return 50  # Default confidence
    
    def save_suit_learning(self):
        """Save learned suit patterns"""
        save_data = {
            'suit_vocabulary': self.suit_vocabulary,
            'scaler': self.scaler,
            'pca': self.pca,
            'suit_clusters': self.suit_clusters,
            'card_positions': self.card_positions
        }
        
        with open(os.path.join(self.save_path, 'suit_learning.pkl'), 'wb') as f:
            pickle.dump(save_data, f)
        
        print("💾 Suit learning saved")
    
    def load_suit_learning(self):
        """Load previously learned suit patterns"""
        try:
            with open(os.path.join(self.save_path, 'suit_learning.pkl'), 'rb') as f:
                save_data = pickle.load(f)
            
            self.suit_vocabulary = save_data['suit_vocabulary']
            self.scaler = save_data['scaler']
            self.pca = save_data['pca']
            self.suit_clusters = save_data['suit_clusters']
            self.card_positions = save_data.get('card_positions', self.card_positions)
            
            print("📚 Suit learning loaded")
            return True
        except FileNotFoundError:
            print("🆕 No previous suit learning found")
            return False


def load_training_images(train_folder="train"):
    """Load images from train folder"""
    print(f"📁 Loading images from {train_folder}...")
    
    image_files = glob.glob(os.path.join(train_folder, "*.jpg"))
    images = []
    
    for img_path in image_files[:100]:  # Use first 100 images for faster training
        img = cv2.imread(img_path)
        if img is not None:
            images.append(img)
    
    print(f"✅ Loaded {len(images)} images")
    return images


def main():
    """Main function for card suit recognition"""
    print("🃏 Card Suit Recognition System")
    print("=" * 40)
    
    recognizer = CardSuitRecognizer()
    
    while True:
        print("\n📋 Choose an option:")
        print("1. 🎓 Learn suit patterns from train images")
        print("2. 🎯 Test suit recognition on sample image")
        print("3. 🎮 Live suit prediction mode")
        print("4. 📊 Show learning status")
        print("5. ❌ Exit")
        
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == '1':
            print("\n🎓 Starting suit pattern learning...")
            images = load_training_images()
            
            if len(images) > 0:
                success = recognizer.learn_suit_patterns(images)
                if success:
                    print("✅ Learning completed successfully!")
                else:
                    print("❌ Learning failed")
            else:
                print("❌ No images found in train folder")
        
        elif choice == '2':
            print("\n🧪 Testing suit recognition...")
            images = load_training_images()
            
            if len(images) > 0 and recognizer.suit_clusters is not None:
                test_image = images[0]  # Test on first image
                predictions = recognizer.predict_card_suits(test_image)
                
                if predictions:
                    print("🎯 Predicted suits:")
                    for pred in predictions:
                        print(f"   Card {pred['position']}: {pred['suit']} (confidence: {pred['confidence']:.1f}%)")
                else:
                    print("❌ Prediction failed")
            else:
                print("❌ No trained model or images available")
        
        elif choice == '3':
            print("\n🎮 Starting live prediction mode...")
            print("Press Ctrl+C to stop")
            
            if recognizer.suit_clusters is None:
                loaded = recognizer.load_suit_learning()
                if not loaded:
                    print("❌ No trained model found. Please train first.")
                    continue
            
            try:
                from PIL import ImageGrab
                crop_box = (970, 388, 1350, 632)  # Your game coordinates
                
                while True:
                    # Capture game
                    img = ImageGrab.grab(bbox=crop_box)
                    img_np = np.array(img)
                    img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
                    
                    # Predict suits
                    predictions = recognizer.predict_card_suits(img_bgr)
                    
                    if predictions:
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        print(f"\n[{timestamp}] 🃏 Card Suits:")
                        
                        # Display in 3x3 grid format
                        for row in range(3):
                            row_suits = []
                            for col in range(3):
                                pos = row * 3 + col
                                pred = next((p for p in predictions if p['position'] == pos), None)
                                if pred:
                                    row_suits.append(f"{pred['suit']}")
                                else:
                                    row_suits.append("?")
                            print(f"   {' '.join(row_suits)}")
                    
                    time.sleep(10)  # Check every 10 seconds
                    
            except KeyboardInterrupt:
                print("\n🛑 Live prediction stopped")
            except Exception as e:
                print(f"❌ Error: {e}")
        
        elif choice == '4':
            print("\n📊 System Status:")
            loaded = recognizer.load_suit_learning()
            
            if loaded:
                vocab = recognizer.suit_vocabulary
                print(f"✅ Suit patterns learned")
                print(f"📊 Total cards analyzed: {vocab['total_cards']}")
                print(f"🎯 Clusters found: {len(vocab['clusters'])}")
                
                for i, cluster in vocab['clusters'].items():
                    print(f"   Cluster {i}: {cluster['count']} cards")
            else:
                print("❌ No trained model found")
        
        elif choice == '5':
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    main()
