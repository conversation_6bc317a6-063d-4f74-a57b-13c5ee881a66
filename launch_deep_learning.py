"""
Launch Deep Learning Suit Predictor
===================================

Easy launcher for the advanced CNN suit prediction system.
"""

import os
import sys
import subprocess

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = ['tensorflow', 'opencv-python', 'scikit-learn', 'matplotlib']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def main():
    """Main launcher"""
    print("🚀 Deep Learning Suit Predictor Launcher")
    print("=" * 45)
    
    # Check dependencies
    print("🔍 Checking dependencies...")
    missing = check_dependencies()
    
    if missing:
        print(f"❌ Missing packages: {', '.join(missing)}")
        print("\n📦 Installing missing packages...")
        
        install_choice = input("Install missing packages? (y/n): ").strip().lower()
        if install_choice == 'y':
            subprocess.run([sys.executable, "install_deep_learning.py"])
        else:
            print("❌ Cannot proceed without required packages")
            return
    else:
        print("✅ All dependencies found!")
    
    print("\n🧠 Deep Learning Options:")
    print("1. 🚀 Start Continuous Learning (Train CNN)")
    print("2. 🔮 Predict Suits (Use Trained Model)")
    print("3. 📊 View Training Progress")
    print("4. ❌ Exit")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == '1':
        print("\n🧠 Starting Deep Learning Training...")
        print("🎯 Target: 98% accuracy")
        print("⏱️ This may take 30-60 minutes depending on your hardware")
        print("🔥 The model will continuously improve until target is reached")
        
        proceed = input("\nProceed with training? (y/n): ").strip().lower()
        if proceed == 'y':
            subprocess.run([sys.executable, "deep_suit_predictor.py"])
        
    elif choice == '2':
        print("\n🔮 Suit Prediction Mode")
        
        # Check if trained model exists
        model_files = [f for f in os.listdir('.') if f.startswith('deep_suit_model_') and f.endswith('.h5')]
        
        if not model_files:
            print("❌ No trained model found!")
            print("🧠 Please train the model first (option 1)")
            return
        
        latest_model = max(model_files, key=os.path.getctime)
        print(f"📁 Using model: {latest_model}")
        
        # Run prediction
        from deep_suit_predictor import DeepSuitPredictor
        predictor = DeepSuitPredictor()
        
        if predictor.load_model(latest_model):
            # Get test image
            test_images = [f for f in os.listdir('train') if f.endswith(('.jpg', '.png'))]
            if test_images:
                test_image = f"train/{test_images[0]}"
                print(f"🧪 Testing on: {test_images[0]}")
                predictions = predictor.predict_suits_from_image(test_image)
                
                print(f"\n🎯 Predicted {len(predictions)} cards:")
                for pred in predictions:
                    print(f"   Card {pred['card_index']}: {pred['suit']} ({pred['confidence']*100:.1f}%)")
            else:
                print("❌ No test images found in train folder")
        
    elif choice == '3':
        print("\n📊 Training Progress")
        
        # Look for training history files
        history_files = [f for f in os.listdir('.') if f.startswith('model_metadata_') and f.endswith('.json')]
        
        if not history_files:
            print("❌ No training history found")
            return
        
        import json
        latest_history = max(history_files, key=os.path.getctime)
        
        with open(latest_history, 'r') as f:
            metadata = json.load(f)
        
        print(f"📁 Latest training: {metadata['timestamp']}")
        print(f"🎯 Current accuracy: {metadata['accuracy']*100:.2f}%")
        print(f"🏆 Target accuracy: {metadata['target_accuracy']*100}%")
        
        if 'training_history' in metadata and metadata['training_history']:
            history = metadata['training_history']
            print(f"📈 Training epochs: {len(history)}")
            print(f"📊 Best accuracy: {max([h['accuracy'] for h in history])*100:.2f}%")
        
    elif choice == '4':
        print("👋 Goodbye!")
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
