# Auto-Detection Feature Guide

## Overview
The Auto-Detection feature enables PolitePredict to automatically monitor any card game running on your screen, detect game state changes, extract data using OCR, and optionally auto-save the results. This creates a fully autonomous prediction system.

## Key Features

### 🎯 **Automatic Game Detection**
- **Full Screen Scanning**: Searches entire screen for game windows
- **Template Matching**: Uses saved game templates for precise detection
- **Pattern Recognition**: Detects card-like patterns when templates aren't available
- **Dynamic Window Tracking**: Automatically adjusts to game window movement

### 🔄 **Continuous Monitoring**
- **Real-time Monitoring**: Checks for game changes every 3 seconds
- **Change Detection**: Uses image comparison to detect new game states
- **Background Processing**: Runs in separate thread without blocking GUI
- **Error Recovery**: Automatically handles temporary capture failures

### 🤖 **Intelligent Auto-Processing**
- **OCR Integration**: Automatically extracts winning seat, cards, and trends
- **Data Validation**: Verifies extracted data quality before auto-saving
- **Manual Override**: Falls back to manual verification for uncertain data
- **Confidence Scoring**: Only auto-saves when confidence is high

## Setup Instructions

### Step 1: Initial Calibration
1. **Open your card game** in any window on your screen
2. **Position the game** so all cards and winning indicators are clearly visible
3. **Click "🎯 Calibrate Game"** in PolitePredict
4. **Verify calibration** - you should see a success message with template creation

### Step 2: Start Auto-Monitoring
1. **Click "▶️ Start Auto-Monitor"** to begin continuous monitoring
2. **Check status indicator** - should show "Auto: ON" in green
3. **Monitor status bar** - will show "Auto-detection: ACTIVE - Monitoring game..."

### Step 3: Play and Monitor
1. **Play your game normally** - the system monitors in the background
2. **Watch for notifications** - GUI flashes green when new data is detected
3. **Verify auto-saved data** - check session count and last win info
4. **Manual verification** - review any data that requires manual confirmation

## Usage Modes

### 🟢 **Fully Automatic Mode**
- **High Confidence Auto-Save**: Data is automatically saved when OCR confidence is high
- **Requirements**: All cards, winning seat, and trend must be clearly detected
- **Benefits**: Completely hands-free operation
- **Use Case**: Stable game display with good lighting and clear text

### 🟡 **Semi-Automatic Mode**
- **Detection + Manual Verification**: System detects changes and fills GUI fields
- **User Action Required**: Click "Save Data" after verifying extracted information
- **Benefits**: OCR assistance with human oversight
- **Use Case**: Variable game display quality or when maximum accuracy is needed

### 🔴 **Manual Fallback Mode**
- **Detection Only**: System detects game changes but OCR fails
- **User Action Required**: Manually enter all data
- **Benefits**: Still provides change detection timing
- **Use Case**: Poor OCR conditions or unsupported game layouts

## Configuration Options

### Detection Sensitivity
```python
AUTO_DETECTION_INTERVAL = 3000  # Check every 3 seconds (adjustable)
MIN_CHANGE_THRESHOLD = 0.1      # Minimum change to trigger processing (0.1 = 10%)
GAME_DETECTION_THRESHOLD = 0.8  # Template matching confidence (80%)
```

### Auto-Save Criteria
The system auto-saves when ALL conditions are met:
- ✅ Winning seat detected (A, B, or C)
- ✅ All 3 cards have valid values (2-A)
- ✅ All 3 cards have recognized suits (♠♣♥♦)
- ✅ Trend matches known patterns (High Card, Pair, etc.)
- ✅ No OCR errors reported

## Troubleshooting

### Common Issues

#### 🔍 **Game Not Detected**
**Symptoms**: "Could not find game window" message
**Solutions**:
1. Ensure game window is fully visible on screen
2. Try calibrating again with better game positioning
3. Check if game has unique visual elements for detection
4. Manually adjust `CROP_BOX` coordinates in code if needed

#### 📷 **OCR Extraction Fails**
**Symptoms**: "OCR failed" or incomplete data extraction
**Solutions**:
1. Improve game display quality (higher resolution, better lighting)
2. Ensure cards and text are clearly visible
3. Check if game fonts are supported by Tesseract
4. Manually verify and save data when OCR fails

#### ⚡ **High CPU Usage**
**Symptoms**: System slowdown during monitoring
**Solutions**:
1. Increase `AUTO_DETECTION_INTERVAL` (check less frequently)
2. Close unnecessary applications
3. Reduce game window size
4. Use lower resolution display settings

#### 🔄 **False Change Detection**
**Symptoms**: System processes same game state repeatedly
**Solutions**:
1. Increase `MIN_CHANGE_THRESHOLD` (require more change)
2. Ensure game display is stable (no animations/flickering)
3. Check for screen savers or overlays interfering

### Performance Optimization

#### For Low-End Systems
- Set `AUTO_DETECTION_INTERVAL = 5000` (5 seconds)
- Set `MIN_CHANGE_THRESHOLD = 0.15` (15% change required)
- Close other resource-intensive applications

#### For High-End Systems
- Set `AUTO_DETECTION_INTERVAL = 2000` (2 seconds)
- Set `MIN_CHANGE_THRESHOLD = 0.05` (5% change required)
- Enable multiple monitoring threads (advanced users)

## Advanced Features

### 📊 **Multi-Game Support**
- Create multiple templates for different games
- System automatically detects which game is active
- Separate pattern memories for each game type

### 🔄 **Template Management**
- Templates are automatically saved with timestamps
- Old templates can be manually deleted
- Templates improve detection accuracy over time

### 📈 **Performance Monitoring**
- Track detection accuracy and timing
- Monitor OCR success rates
- Automatic performance optimization suggestions

## Best Practices

### 🎮 **Game Setup**
1. **Stable Display**: Ensure game window doesn't move or resize
2. **Good Lighting**: Adequate screen brightness for clear text
3. **Minimal Overlays**: Remove unnecessary UI elements
4. **Consistent Layout**: Use same game settings/themes

### 🖥️ **System Setup**
1. **Dedicated Monitor**: Use separate monitor for game if possible
2. **Stable Internet**: Ensure stable connection for online games
3. **Resource Management**: Close unnecessary applications
4. **Regular Calibration**: Recalibrate if game appearance changes

### 📊 **Data Quality**
1. **Regular Verification**: Periodically check auto-saved data accuracy
2. **Manual Backup**: Keep manual records for critical games
3. **Pattern Validation**: Verify prediction accuracy regularly
4. **Error Reporting**: Note any systematic OCR errors for improvement

## Security and Privacy

### 🔒 **Data Protection**
- All processing is done locally on your machine
- No game data is transmitted over the internet
- Screenshots are stored temporarily and automatically cleaned up
- Database files remain on your local system

### 🛡️ **Game Compliance**
- System only observes publicly visible game information
- No game modification or interference
- Complies with screen reading accessibility standards
- Respects game terms of service for observation tools

## Future Enhancements

### Planned Features
- **Multi-Monitor Support**: Detect games across multiple screens
- **Cloud Sync**: Optional cloud backup of pattern data
- **Mobile Integration**: Companion mobile app for remote monitoring
- **Advanced Analytics**: Detailed performance and accuracy reporting
- **Custom OCR Training**: Train OCR for specific game fonts/layouts

## Support

For technical support with auto-detection:
1. Check this guide for common solutions
2. Verify system requirements are met
3. Test with manual capture first
4. Report persistent issues with screenshots and error logs
