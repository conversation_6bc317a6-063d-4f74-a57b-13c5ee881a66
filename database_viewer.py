import sqlite3
import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
from datetime import datetime
import os

class PokerDatabaseViewer:
    def __init__(self, root):
        self.root = root
        self.root.title("🎮 Poker Games Database Viewer")
        self.root.geometry("1400x800")
        
        # Database path
        self.db_path = "poker_games_advanced.db"
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        root.columnconfigure(0, weight=1)
        root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="🎮 Poker Games Database Viewer", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 10))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Create tabs
        self.create_games_tab()
        self.create_cards_tab()
        self.create_stats_tab()
        
        # Status bar
        self.status_var = tk.StringVar()
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Load data
        self.refresh_data()
    
    def create_games_tab(self):
        """Create the main games tab"""
        games_frame = ttk.Frame(self.notebook)
        self.notebook.add(games_frame, text="🎮 Games Overview")
        
        # Control frame
        control_frame = ttk.Frame(games_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Refresh button
        ttk.Button(control_frame, text="🔄 Refresh", 
                  command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 10))
        
        # Filter by winning seat
        ttk.Label(control_frame, text="Filter by Winner:").pack(side=tk.LEFT, padx=(0, 5))
        self.winner_filter = ttk.Combobox(control_frame, values=["All", "A", "B", "C"], 
                                         state="readonly", width=10)
        self.winner_filter.set("All")
        self.winner_filter.pack(side=tk.LEFT, padx=(0, 10))
        self.winner_filter.bind("<<ComboboxSelected>>", self.filter_games)
        
        # Export button
        ttk.Button(control_frame, text="📊 Export CSV", 
                  command=self.export_games).pack(side=tk.RIGHT)
        
        # Games treeview with poker hand columns
        columns = ("ID", "Timestamp", "Image", "Winner", "Brightness", "Game Title",
                  "Seat A Hand", "Seat A Cards", "Seat B Hand", "Seat B Cards",
                  "Seat C Hand", "Seat C Cards", "Pot A", "Pot B", "Pot C")

        self.games_tree = ttk.Treeview(games_frame, columns=columns, show="headings", height=20)

        # Configure columns
        column_widths = {"ID": 50, "Timestamp": 130, "Image": 200, "Winner": 60,
                        "Brightness": 80, "Game Title": 100, "Seat A Hand": 120,
                        "Seat A Cards": 100, "Seat B Hand": 120, "Seat B Cards": 100,
                        "Seat C Hand": 120, "Seat C Cards": 100, "Pot A": 80, "Pot B": 80, "Pot C": 80}
        
        for col in columns:
            self.games_tree.heading(col, text=col)
            self.games_tree.column(col, width=column_widths.get(col, 100))
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(games_frame, orient=tk.VERTICAL, command=self.games_tree.yview)
        h_scrollbar = ttk.Scrollbar(games_frame, orient=tk.HORIZONTAL, command=self.games_tree.xview)
        self.games_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.games_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X, padx=5)
    
    def create_cards_tab(self):
        """Create the individual cards tab"""
        cards_frame = ttk.Frame(self.notebook)
        self.notebook.add(cards_frame, text="🃏 Individual Cards")
        
        # Control frame
        control_frame = ttk.Frame(cards_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(control_frame, text="Game ID:").pack(side=tk.LEFT, padx=(0, 5))
        self.game_id_entry = ttk.Entry(control_frame, width=10)
        self.game_id_entry.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="🔍 Show Cards", 
                  command=self.show_cards_for_game).pack(side=tk.LEFT)
        
        # Cards treeview with parsed values
        card_columns = ("Game ID", "Seat", "Position", "Card Number", "Card Suit",
                       "Card Value", "OCR Text", "Confidence", "BBox")
        
        self.cards_tree = ttk.Treeview(cards_frame, columns=card_columns, show="headings", height=20)
        
        for col in card_columns:
            self.cards_tree.heading(col, text=col)
            self.cards_tree.column(col, width=100)
        
        # Scrollbars for cards
        cards_v_scrollbar = ttk.Scrollbar(cards_frame, orient=tk.VERTICAL, command=self.cards_tree.yview)
        self.cards_tree.configure(yscrollcommand=cards_v_scrollbar.set)
        
        self.cards_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        cards_v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def create_stats_tab(self):
        """Create statistics tab"""
        stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(stats_frame, text="📊 Statistics")
        
        # Stats text widget
        self.stats_text = tk.Text(stats_frame, wrap=tk.WORD, font=("Courier", 11))
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
    
    def refresh_data(self):
        """Refresh all data from database"""
        try:
            if not os.path.exists(self.db_path):
                self.status_var.set("❌ Database not found!")
                return
            
            conn = sqlite3.connect(self.db_path)
            
            # Load games data with poker hand analysis
            games_query = """
                SELECT id, timestamp, image_path, winning_seat, winning_seat_brightness,
                       game_title,
                       seat_a_hand_type, seat_a_card1_number, seat_a_card1_suit,
                       seat_a_card2_number, seat_a_card2_suit, seat_a_card3_number, seat_a_card3_suit,
                       seat_b_hand_type, seat_b_card1_number, seat_b_card1_suit,
                       seat_b_card2_number, seat_b_card2_suit, seat_b_card3_number, seat_b_card3_suit,
                       seat_c_hand_type, seat_c_card1_number, seat_c_card1_suit,
                       seat_c_card2_number, seat_c_card2_suit, seat_c_card3_number, seat_c_card3_suit,
                       seat_a_pot, seat_b_pot, seat_c_pot, middle_text
                FROM poker_games
                ORDER BY id DESC
            """
            
            self.games_df = pd.read_sql_query(games_query, conn)
            
            # Load cards data with parsed values
            cards_query = """
                SELECT game_id, seat, card_position, card_number, card_suit, card_value,
                       ocr_text, confidence,
                       PRINTF('(%d,%d,%d,%d)', bbox_x, bbox_y, bbox_width, bbox_height) as bbox
                FROM cards
                ORDER BY game_id DESC, seat, card_position
            """
            
            self.cards_df = pd.read_sql_query(cards_query, conn)
            conn.close()
            
            # Update displays
            self.update_games_display()
            self.update_stats_display()
            
            self.status_var.set(f"✅ Loaded {len(self.games_df)} games and {len(self.cards_df)} cards")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load data: {str(e)}")
            self.status_var.set("❌ Failed to load data")
    
    def update_games_display(self):
        """Update the games treeview"""
        # Clear existing items
        for item in self.games_tree.get_children():
            self.games_tree.delete(item)
        
        # Filter data if needed
        display_df = self.games_df.copy()
        winner_filter = self.winner_filter.get()
        if winner_filter != "All":
            display_df = display_df[display_df['winning_seat'] == winner_filter]
        
        # Add data to treeview
        for _, row in display_df.iterrows():
            # Format timestamp
            try:
                timestamp = datetime.fromisoformat(row['timestamp']).strftime("%Y-%m-%d %H:%M:%S")
            except:
                timestamp = str(row['timestamp'])[:19]

            # Format image name (just filename)
            image_name = os.path.basename(str(row['image_path']))

            # Format card combinations for each seat
            def format_cards(c1_num, c1_suit, c2_num, c2_suit, c3_num, c3_suit):
                cards = []
                for num, suit in [(c1_num, c1_suit), (c2_num, c2_suit), (c3_num, c3_suit)]:
                    if num and suit:
                        cards.append(f"{num}{suit[0] if suit else ''}")
                return " ".join(cards) if cards else "No cards"

            seat_a_cards = format_cards(
                row.get('seat_a_card1_number', ''), row.get('seat_a_card1_suit', ''),
                row.get('seat_a_card2_number', ''), row.get('seat_a_card2_suit', ''),
                row.get('seat_a_card3_number', ''), row.get('seat_a_card3_suit', '')
            )

            seat_b_cards = format_cards(
                row.get('seat_b_card1_number', ''), row.get('seat_b_card1_suit', ''),
                row.get('seat_b_card2_number', ''), row.get('seat_b_card2_suit', ''),
                row.get('seat_b_card3_number', ''), row.get('seat_b_card3_suit', '')
            )

            seat_c_cards = format_cards(
                row.get('seat_c_card1_number', ''), row.get('seat_c_card1_suit', ''),
                row.get('seat_c_card2_number', ''), row.get('seat_c_card2_suit', ''),
                row.get('seat_c_card3_number', ''), row.get('seat_c_card3_suit', '')
            )

            values = (
                row['id'],
                timestamp,
                image_name,
                row['winning_seat'] or 'N/A',
                f"{row['winning_seat_brightness']:.1f}" if row['winning_seat_brightness'] else 'N/A',
                str(row['game_title'])[:20] if row['game_title'] else 'N/A',
                str(row.get('seat_a_hand_type', ''))[:15],
                seat_a_cards[:15],
                str(row.get('seat_b_hand_type', ''))[:15],
                seat_b_cards[:15],
                str(row.get('seat_c_hand_type', ''))[:15],
                seat_c_cards[:15],
                str(row['seat_a_pot'])[:10] if row['seat_a_pot'] else '',
                str(row['seat_b_pot'])[:10] if row['seat_b_pot'] else '',
                str(row['seat_c_pot'])[:10] if row['seat_c_pot'] else ''
            )
            
            # Color code by winner
            tags = ()
            if row['winning_seat'] == 'A':
                tags = ('winner_a',)
            elif row['winning_seat'] == 'B':
                tags = ('winner_b',)
            elif row['winning_seat'] == 'C':
                tags = ('winner_c',)
            
            self.games_tree.insert("", tk.END, values=values, tags=tags)
        
        # Configure tags for colors
        self.games_tree.tag_configure('winner_a', background='#ffcccc')  # Light red
        self.games_tree.tag_configure('winner_b', background='#ccffcc')  # Light green
        self.games_tree.tag_configure('winner_c', background='#ccccff')  # Light blue

    def filter_games(self, event=None):
        """Filter games by winning seat"""
        self.update_games_display()

    def show_cards_for_game(self):
        """Show cards for specific game ID"""
        try:
            game_id = int(self.game_id_entry.get())

            # Clear existing items
            for item in self.cards_tree.get_children():
                self.cards_tree.delete(item)

            # Filter cards for this game
            game_cards = self.cards_df[self.cards_df['game_id'] == game_id]

            if game_cards.empty:
                messagebox.showinfo("No Cards", f"No cards found for Game ID {game_id}")
                return

            # Add cards to treeview
            for _, row in game_cards.iterrows():
                values = (
                    row['game_id'],
                    row['seat'],
                    row['card_position'],
                    row['card_number'] or '',
                    row['card_suit'] or '',
                    row['card_value'] if row['card_value'] else '',
                    row['ocr_text'] or '',
                    f"{row['confidence']:.2f}" if row['confidence'] else '',
                    row['bbox']
                )
                self.cards_tree.insert("", tk.END, values=values)

            self.status_var.set(f"✅ Showing {len(game_cards)} cards for Game ID {game_id}")

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid Game ID number")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load cards: {str(e)}")

    def update_stats_display(self):
        """Update statistics display"""
        if self.games_df.empty:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, "No data available")
            return

        stats_text = "🎮 POKER GAMES STATISTICS\n"
        stats_text += "=" * 50 + "\n\n"

        # Basic stats
        total_games = len(self.games_df)
        stats_text += f"📊 Total Games Processed: {total_games}\n\n"

        # Winner statistics
        winner_counts = self.games_df['winning_seat'].value_counts()
        stats_text += "🏆 WINNING SEAT STATISTICS:\n"
        stats_text += "-" * 30 + "\n"

        for seat in ['A', 'B', 'C']:
            count = winner_counts.get(seat, 0)
            percentage = (count / total_games * 100) if total_games > 0 else 0
            stats_text += f"Seat {seat}: {count:3d} wins ({percentage:5.1f}%)\n"

        # Brightness statistics
        stats_text += f"\n💡 BRIGHTNESS STATISTICS:\n"
        stats_text += "-" * 30 + "\n"

        brightness_data = self.games_df['winning_seat_brightness'].dropna()
        if not brightness_data.empty:
            stats_text += f"Average Winning Brightness: {brightness_data.mean():.2f}\n"
            stats_text += f"Min Winning Brightness: {brightness_data.min():.2f}\n"
            stats_text += f"Max Winning Brightness: {brightness_data.max():.2f}\n"

        # Card detection statistics
        stats_text += f"\n🃏 CARD DETECTION STATISTICS:\n"
        stats_text += "-" * 30 + "\n"

        total_cards = len(self.cards_df)
        stats_text += f"Total Cards Detected: {total_cards}\n"

        if total_cards > 0:
            cards_per_game = total_cards / total_games
            stats_text += f"Average Cards per Game: {cards_per_game:.1f}\n"

            # Cards by seat
            seat_counts = self.cards_df['seat'].value_counts()
            for seat in ['card_area_1', 'card_area_2', 'card_area_3']:
                count = seat_counts.get(seat, 0)
                seat_letter = {'card_area_1': 'A', 'card_area_2': 'B', 'card_area_3': 'C'}[seat]
                stats_text += f"Seat {seat_letter} Cards: {count}\n"

        # Recent activity
        stats_text += f"\n📅 RECENT ACTIVITY:\n"
        stats_text += "-" * 30 + "\n"

        if total_games > 0:
            latest_game = self.games_df.iloc[0]  # Already sorted by ID DESC
            try:
                latest_time = datetime.fromisoformat(latest_game['timestamp']).strftime("%Y-%m-%d %H:%M:%S")
            except:
                latest_time = str(latest_game['timestamp'])[:19]

            stats_text += f"Latest Game: ID {latest_game['id']}\n"
            stats_text += f"Processed: {latest_time}\n"
            stats_text += f"Winner: Seat {latest_game['winning_seat']}\n"

        # Poker hand analysis
        stats_text += f"\n🃏 POKER HAND ANALYSIS:\n"
        stats_text += "-" * 30 + "\n"

        # Count hand types for each seat
        for seat in ['A', 'B', 'C']:
            hand_col = f'seat_{seat.lower()}_hand_type'
            if hand_col in self.games_df.columns:
                hand_counts = self.games_df[hand_col].value_counts().head(3)
                stats_text += f"\nSeat {seat} Hand Types:\n"
                for hand_type, count in hand_counts.items():
                    if hand_type and str(hand_type).strip():
                        stats_text += f"  {hand_type}: {count} times\n"

        # Game titles analysis
        stats_text += f"\n🎮 GAME TITLES:\n"
        stats_text += "-" * 30 + "\n"

        title_counts = self.games_df['game_title'].value_counts().head(5)
        for title, count in title_counts.items():
            if title and str(title).strip():
                stats_text += f"'{title}': {count} times\n"

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, stats_text)

    def export_games(self):
        """Export games data to CSV"""
        try:
            filename = f"poker_games_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            self.games_df.to_csv(filename, index=False)
            messagebox.showinfo("Export Success", f"Data exported to {filename}")
            self.status_var.set(f"✅ Exported to {filename}")
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export: {str(e)}")

def main():
    root = tk.Tk()
    app = PokerDatabaseViewer(root)
    root.mainloop()

if __name__ == "__main__":
    main()
