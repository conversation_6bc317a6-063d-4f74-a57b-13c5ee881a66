"""
Install Deep Learning Dependencies
=================================

Installs TensorFlow and other required packages for the CNN suit predictor.
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        print(f"📦 Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    """Install all required packages"""
    print("🚀 Installing Deep Learning Dependencies")
    print("=" * 45)
    
    packages = [
        "tensorflow",
        "scikit-learn", 
        "matplotlib",
        "opencv-python",
        "numpy",
        "pillow"
    ]
    
    print("📋 Required packages:")
    for pkg in packages:
        print(f"   - {pkg}")
    print()
    
    success_count = 0
    
    for package in packages:
        if install_package(package):
            success_count += 1
        print()
    
    print("=" * 45)
    if success_count == len(packages):
        print("🎉 All packages installed successfully!")
        print("🧠 Ready to run deep learning suit predictor!")
        print()
        print("🚀 Next step: Run the CNN trainer")
        print("   python deep_suit_predictor.py")
    else:
        print(f"⚠️ {len(packages) - success_count} packages failed to install")
        print("🔧 Try installing manually or check your Python environment")

if __name__ == "__main__":
    main()
