#!/usr/bin/env python3
"""
Game Coordinate Finder for PolitePredict
This tool helps you find the exact coordinates for your game window
"""

import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageGrab, ImageDraw
import cv2
import numpy as np

class CoordinateFinder:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("PolitePredict - Game Coordinate Finder")
        self.root.geometry("800x600")
        
        # Variables
        self.current_screenshot = None
        self.selection_start = None
        self.selection_end = None
        self.crop_coords = None
        
        self.setup_gui()
        
    def setup_gui(self):
        """Setup the GUI"""
        # Title
        title_label = tk.Label(self.root, text="🎯 Find Your Game Coordinates", 
                              font=("Arial", 16, "bold"), fg="blue")
        title_label.pack(pady=10)
        
        # Instructions
        instructions = tk.Text(self.root, height=4, wrap=tk.WORD)
        instructions.insert("1.0", 
            "1. Click 'Capture Full Screen' to take a screenshot\n"
            "2. Click and drag to select your game area\n"
            "3. Click 'Test Crop Area' to see what will be captured\n"
            "4. Copy the coordinates to your polite_predict.py file")
        instructions.config(state=tk.DISABLED)
        instructions.pack(pady=5, padx=10, fill=tk.X)
        
        # Buttons frame
        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="📸 Capture Full Screen", 
                 command=self.capture_full_screen, bg="#007bff", fg="white",
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="🎯 Test Crop Area", 
                 command=self.test_crop_area, bg="#28a745", fg="white",
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="📋 Copy Coordinates", 
                 command=self.copy_coordinates, bg="#ffc107", fg="black",
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT, padx=5)
        
        # Coordinates display
        self.coords_var = tk.StringVar()
        self.coords_var.set("Coordinates will appear here...")
        coords_label = tk.Label(self.root, textvariable=self.coords_var, 
                               font=("Courier", 12), bg="lightgray", relief=tk.SUNKEN)
        coords_label.pack(pady=10, padx=10, fill=tk.X)
        
        # Canvas for image display
        self.canvas = tk.Canvas(self.root, bg="white", height=400)
        self.canvas.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
        
        # Bind mouse events for selection
        self.canvas.bind("<Button-1>", self.start_selection)
        self.canvas.bind("<B1-Motion>", self.update_selection)
        self.canvas.bind("<ButtonRelease-1>", self.end_selection)
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Ready - Click 'Capture Full Screen' to start")
        status_bar = tk.Label(self.root, textvariable=self.status_var, 
                             relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
    def capture_full_screen(self):
        """Capture the full screen"""
        try:
            self.status_var.set("Capturing full screen...")
            self.root.update()
            
            # Hide window temporarily
            self.root.withdraw()
            self.root.after(500, self._do_capture)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to capture screen: {e}")
            self.status_var.set("Capture failed")
    
    def _do_capture(self):
        """Actually do the screen capture"""
        try:
            # Capture full screen
            screenshot = ImageGrab.grab()
            self.current_screenshot = screenshot
            
            # Show window again
            self.root.deiconify()
            
            # Resize for display
            display_size = (700, 400)
            display_image = screenshot.copy()
            display_image.thumbnail(display_size, Image.Resampling.LANCZOS)
            
            # Convert to PhotoImage
            self.photo = ImageTk.PhotoImage(display_image)
            
            # Clear canvas and display image
            self.canvas.delete("all")
            self.canvas.create_image(350, 200, image=self.photo)
            
            # Store scale factors for coordinate conversion
            self.scale_x = screenshot.width / display_image.width
            self.scale_y = screenshot.height / display_image.height
            
            self.status_var.set("Screenshot captured! Click and drag to select your game area")
            
        except Exception as e:
            self.root.deiconify()
            messagebox.showerror("Error", f"Failed to process screenshot: {e}")
            self.status_var.set("Capture failed")
    
    def start_selection(self, event):
        """Start selection rectangle"""
        if self.current_screenshot is None:
            return
        
        self.selection_start = (event.x, event.y)
        self.canvas.delete("selection")
    
    def update_selection(self, event):
        """Update selection rectangle"""
        if self.selection_start is None:
            return
        
        self.canvas.delete("selection")
        self.canvas.create_rectangle(
            self.selection_start[0], self.selection_start[1],
            event.x, event.y,
            outline="red", width=2, tags="selection"
        )
    
    def end_selection(self, event):
        """End selection and calculate coordinates"""
        if self.selection_start is None:
            return
        
        self.selection_end = (event.x, event.y)
        
        # Calculate actual coordinates
        x1 = int(min(self.selection_start[0], self.selection_end[0]) * self.scale_x)
        y1 = int(min(self.selection_start[1], self.selection_end[1]) * self.scale_y)
        x2 = int(max(self.selection_start[0], self.selection_end[0]) * self.scale_x)
        y2 = int(max(self.selection_start[1], self.selection_end[1]) * self.scale_y)
        
        self.crop_coords = (x1, y1, x2, y2)
        
        # Update display
        self.coords_var.set(f"CROP_BOX = {self.crop_coords}  # (left, top, right, bottom)")
        self.status_var.set(f"Selected area: {x2-x1}x{y2-y1} pixels at ({x1}, {y1})")
    
    def test_crop_area(self):
        """Test the selected crop area"""
        if self.crop_coords is None:
            messagebox.showwarning("Warning", "Please select an area first!")
            return
        
        try:
            # Capture the selected area
            test_image = ImageGrab.grab(bbox=self.crop_coords)
            
            # Save test image
            test_image.save("test_crop_area.png")
            
            # Show in a new window
            self.show_test_image(test_image)
            
            self.status_var.set("Test crop saved as 'test_crop_area.png'")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to test crop area: {e}")
    
    def show_test_image(self, image):
        """Show the test image in a new window"""
        test_window = tk.Toplevel(self.root)
        test_window.title("Test Crop Area")
        test_window.geometry("600x400")
        
        # Resize for display
        display_image = image.copy()
        display_image.thumbnail((550, 350), Image.Resampling.LANCZOS)
        
        photo = ImageTk.PhotoImage(display_image)
        
        label = tk.Label(test_window, image=photo)
        label.image = photo  # Keep reference
        label.pack(pady=20)
        
        info_label = tk.Label(test_window, 
                             text=f"Crop Area: {self.crop_coords}\nSize: {image.width}x{image.height} pixels",
                             font=("Courier", 10))
        info_label.pack()
        
        # Buttons
        button_frame = tk.Frame(test_window)
        button_frame.pack(pady=10)
        
        tk.Button(button_frame, text="✅ Looks Good", 
                 command=lambda: [test_window.destroy(), self.confirm_coordinates()],
                 bg="#28a745", fg="white").pack(side=tk.LEFT, padx=5)
        
        tk.Button(button_frame, text="❌ Try Again", 
                 command=test_window.destroy,
                 bg="#dc3545", fg="white").pack(side=tk.LEFT, padx=5)
    
    def confirm_coordinates(self):
        """Confirm the coordinates are good"""
        messagebox.showinfo("Success", 
                           "Great! Your coordinates look good.\n"
                           "Click 'Copy Coordinates' to copy them to clipboard.")
    
    def copy_coordinates(self):
        """Copy coordinates to clipboard"""
        if self.crop_coords is None:
            messagebox.showwarning("Warning", "Please select an area first!")
            return
        
        coord_text = f"CROP_BOX = {self.crop_coords}  # (left, top, right, bottom)"
        
        self.root.clipboard_clear()
        self.root.clipboard_append(coord_text)
        
        messagebox.showinfo("Copied", 
                           f"Coordinates copied to clipboard!\n\n"
                           f"{coord_text}\n\n"
                           f"Now paste this into your polite_predict.py file around line 72")
        
        # Also show instructions
        instructions = (
            "To update PolitePredict:\n\n"
            "1. Open polite_predict.py\n"
            "2. Find line 72 with CROP_BOX = ...\n"
            "3. Replace it with the copied coordinates\n"
            "4. Save the file\n"
            "5. Restart PolitePredict\n\n"
            "Your game should now be captured correctly!"
        )
        
        messagebox.showinfo("Instructions", instructions)
    
    def run(self):
        """Run the application"""
        self.root.mainloop()

def main():
    """Main function"""
    print("🎯 Starting Game Coordinate Finder...")
    print("This tool will help you find the exact coordinates for your game.")
    
    app = CoordinateFinder()
    app.run()

if __name__ == "__main__":
    main()
