"""
Quick Capture - Visual Poker Seat Detection
==========================================

Uses your trained YOLO model to detect and visualize winning seat card suits.
"""

import cv2
import numpy as np
import sqlite3
import json
from datetime import datetime
import os
from ultralytics import YOLO

class QuickCapture:
    """
    Quick capture system with visual predictions
    """
    
    def __init__(self, model_path="models/capture.pt"):
        self.model_path = model_path
        self.model = None
        self.db_path = "poker_results.db"
        self.init_database()
        self.load_model()
    
    def load_model(self):
        """Load the YOLO model"""
        if not os.path.exists(self.model_path):
            print(f"❌ Model not found: {self.model_path}")
            return False
        
        try:
            self.model = YOLO(self.model_path)
            print(f"✅ Model loaded: {self.model_path}")
            return True
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False
    
    def init_database(self):
        """Initialize database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS poker_games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                image_path TEXT NOT NULL,
                winning_seat TEXT,
                seat_a_suits TEXT,
                seat_b_suits TEXT, 
                seat_c_suits TEXT,
                all_detections TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        print(f"✅ Database ready: {self.db_path}")
    
    def detect_and_analyze(self, image_path):
        """Run detection and analyze results"""
        if not self.model:
            print("❌ Model not loaded")
            return None
        
        if not os.path.exists(image_path):
            print(f"❌ Image not found: {image_path}")
            return None
        
        print(f"🔍 Analyzing: {os.path.basename(image_path)}")
        
        # Run detection
        results = self.model(image_path, conf=0.3, verbose=False)
        
        # Parse results
        detections = []
        if results and len(results) > 0:
            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        class_id = int(box.cls[0])
                        confidence = float(box.conf[0])
                        bbox = box.xyxy[0].cpu().numpy()
                        
                        class_name = self.model.names[class_id]
                        
                        detections.append({
                            'class': class_name,
                            'confidence': confidence,
                            'bbox': bbox.tolist(),
                            'center_x': float((bbox[0] + bbox[2]) / 2),
                            'center_y': float((bbox[1] + bbox[3]) / 2)
                        })
        
        print(f"📊 Found {len(detections)} detections")
        
        if len(detections) == 0:
            print("❌ No detections found")
            return None
        
        # Show all detections
        print("\n🔍 All Detections:")
        for i, det in enumerate(detections):
            print(f"   {i+1:2d}. {det['class']:15s} (conf: {det['confidence']:.3f})")
        
        # Analyze seats and suits
        analysis = self.analyze_seats_and_suits(detections)
        
        # Create visualization
        self.visualize_predictions(image_path, detections, analysis)
        
        return {
            'detections': detections,
            'analysis': analysis,
            'image_path': image_path
        }
    
    def analyze_seats_and_suits(self, detections):
        """Analyze detections for seats and suits"""
        colored_seats = []  # blue_seat, golden_seat, purple_seat
        winning_seats = []  # winning_seat detections
        suits = []
        cards = []

        for det in detections:
            class_name = det['class'].lower()

            # Check for colored seats
            if 'seat' in class_name:
                if class_name in ['blue_seat', 'golden_seat', 'purple_seat']:
                    colored_seats.append(det)
                elif 'winning' in class_name:
                    winning_seats.append(det)

            # Check for suits
            elif class_name in ['spades', 'hearts', 'diamonds', 'clubs']:
                suits.append(det)

            # Check for cards
            elif class_name in ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'j', 'q', 'k', 'a']:
                cards.append(det)

        print(f"\n🎯 Analysis:")
        print(f"   Colored Seats: {len(colored_seats)} {[s['class'] for s in colored_seats]}")
        print(f"   Winning Seats: {len(winning_seats)}")
        print(f"   Suits: {len(suits)} {[s['class'] for s in suits]}")
        print(f"   Cards: {len(cards)} {[s['class'] for s in cards]}")

        # Determine which colored seat is the winning seat
        winning_seat_info = self.determine_winning_seat(colored_seats, winning_seats)

        # Associate suits with seats
        seat_suits = self.associate_suits_with_seats(colored_seats, suits)

        return {
            'colored_seats': colored_seats,
            'winning_seats': winning_seats,
            'suits': suits,
            'cards': cards,
            'winning_seat_info': winning_seat_info,
            'seat_suits': seat_suits
        }
    
    def determine_winning_seat(self, colored_seats, winning_seats):
        """Determine which colored seat is the winning seat based on proximity"""
        if not winning_seats or not colored_seats:
            return None

        # For each winning_seat detection, find the closest colored seat
        winning_info = []

        for winning_det in winning_seats:
            closest_seat = None
            min_distance = float('inf')

            for colored_seat in colored_seats:
                distance = np.sqrt((winning_det['center_x'] - colored_seat['center_x'])**2 +
                                 (winning_det['center_y'] - colored_seat['center_y'])**2)
                if distance < min_distance:
                    min_distance = distance
                    closest_seat = colored_seat

            if closest_seat:
                seat_letter = self.get_seat_name(closest_seat['class'])
                winning_info.append({
                    'seat_color': closest_seat['class'],
                    'seat_letter': seat_letter,
                    'distance': min_distance,
                    'confidence': winning_det['confidence']
                })
                print(f"🏆 Winning seat detected: {closest_seat['class']} -> Seat {seat_letter}")

        return winning_info[0] if winning_info else None

    def associate_suits_with_seats(self, colored_seats, suits):
        """Associate suits with colored seats based on proximity"""
        seat_suits = {'A': [], 'B': [], 'C': []}

        for suit in suits:
            closest_seat = None
            min_distance = float('inf')

            for seat in colored_seats:
                distance = np.sqrt((suit['center_x'] - seat['center_x'])**2 +
                                 (suit['center_y'] - seat['center_y'])**2)
                if distance < min_distance:
                    min_distance = distance
                    closest_seat = seat

            if closest_seat:
                seat_name = self.get_seat_name(closest_seat['class'])
                if seat_name in seat_suits:
                    seat_suits[seat_name].append(suit['class'])
                    print(f"🎯 {suit['class']} -> {closest_seat['class']} (Seat {seat_name})")

        return seat_suits
    
    def visualize_predictions(self, image_path, detections, analysis):
        """Create and display visualization of predictions"""
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                print("❌ Could not load image for visualization")
                return
            
            # Create a copy for drawing
            vis_image = image.copy()
            
            # Define colors for different types
            colors = {
                'seat': (0, 255, 0),      # Green for seats
                'suit': (255, 0, 0),      # Blue for suits  
                'card': (0, 0, 255),      # Red for cards
                'winning': (0, 255, 255)  # Yellow for winning seat
            }
            
            # Draw all detections
            for i, det in enumerate(detections):
                bbox = det['bbox']
                x1, y1, x2, y2 = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])
                
                # Determine color based on detection type
                class_name = det['class'].lower()
                if 'seat' in class_name or class_name in ['a', 'b', 'c']:
                    if 'winning' in class_name:
                        color = colors['winning']
                    else:
                        color = colors['seat']
                elif class_name in ['spades', 'hearts', 'diamonds', 'clubs']:
                    color = colors['suit']
                else:
                    color = colors['card']
                
                # Draw bounding box
                cv2.rectangle(vis_image, (x1, y1), (x2, y2), color, 2)
                
                # Draw label with confidence
                label = f"{det['class']} {det['confidence']:.2f}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                
                # Draw label background
                cv2.rectangle(vis_image, (x1, y1 - label_size[1] - 10), 
                             (x1 + label_size[0], y1), color, -1)
                
                # Draw label text
                cv2.putText(vis_image, label, (x1, y1 - 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)
                
                # Draw detection number
                cv2.putText(vis_image, str(i+1), (x1 + 5, y1 + 20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
            
            # Add analysis text overlay
            self.add_analysis_overlay(vis_image, analysis)
            
            # Save visualization
            output_path = f"prediction_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
            cv2.imwrite(output_path, vis_image)
            print(f"📸 Visualization saved: {output_path}")
            
            # Display image
            cv2.imshow('Poker Game Predictions', vis_image)
            print("\n👁️ Image displayed! Press any key to close...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
            
        except Exception as e:
            print(f"❌ Error creating visualization: {e}")
    
    def add_analysis_overlay(self, image, analysis):
        """Add analysis text overlay to image"""
        try:
            # Get image dimensions
            height, width = image.shape[:2]
            
            # Create semi-transparent overlay for text
            overlay = image.copy()
            
            # Analysis text
            seat_suits = analysis['seat_suits']
            text_lines = [
                f"Seats: {len(analysis['seats'])} | Suits: {len(analysis['suits'])} | Cards: {len(analysis['cards'])}",
                f"Seat A: {seat_suits['A']}",
                f"Seat B: {seat_suits['B']}",
                f"Seat C: {seat_suits['C']}"
            ]
            
            if analysis['winning_seat_info']:
                winning_seat = analysis['winning_seat_info']['seat_letter']
                seat_color = analysis['winning_seat_info']['seat_color']
                text_lines.insert(1, f"Winner: Seat {winning_seat} ({seat_color})")
            
            # Draw text background
            text_height = 25
            total_height = len(text_lines) * text_height + 20
            cv2.rectangle(overlay, (10, height - total_height - 10), 
                         (width - 10, height - 10), (0, 0, 0), -1)
            
            # Blend overlay
            cv2.addWeighted(overlay, 0.7, image, 0.3, 0, image)
            
            # Draw text
            for i, line in enumerate(text_lines):
                y_pos = height - total_height + (i * text_height) + 20
                cv2.putText(image, line, (20, y_pos), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                
        except Exception as e:
            print(f"❌ Error adding overlay: {e}")
    
    def get_seat_name(self, class_name):
        """Convert colored seat class name to seat letter"""
        class_name = class_name.lower()

        if 'blue' in class_name:
            return 'A'  # Blue seat = Seat A
        elif 'golden' in class_name or 'yellow' in class_name:
            return 'B'  # Golden seat = Seat B
        elif 'purple' in class_name:
            return 'C'  # Purple seat = Seat C
        else:
            return 'Unknown'
    
    def save_to_database(self, result):
        """Save results to database"""
        if not result:
            return None
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        timestamp = datetime.now().isoformat()
        analysis = result['analysis']
        
        # Determine winning seat from proximity analysis
        winning_seat = None
        if analysis['winning_seat_info']:
            winning_seat = analysis['winning_seat_info']['seat_letter']
        
        # Get seat suits
        seat_suits = analysis['seat_suits']
        
        cursor.execute('''
            INSERT INTO poker_games 
            (timestamp, image_path, winning_seat, seat_a_suits, seat_b_suits, seat_c_suits, all_detections)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            timestamp,
            result['image_path'],
            winning_seat,
            json.dumps(seat_suits['A']),
            json.dumps(seat_suits['B']),
            json.dumps(seat_suits['C']),
            json.dumps(result['detections'], default=str)
        ))
        
        game_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        # Print results
        print(f"\n💾 SAVED TO DATABASE (Game ID: {game_id})")
        print("=" * 50)
        print(f"🏆 Winning Seat: {winning_seat or 'Not detected'}")
        print(f"🎯 Seat A Suits: {seat_suits['A']}")
        print(f"🎯 Seat B Suits: {seat_suits['B']}")
        print(f"🎯 Seat C Suits: {seat_suits['C']}")
        
        return game_id
    
    def process_image(self, image_path):
        """Process a single image"""
        result = self.detect_and_analyze(image_path)
        if result:
            game_id = self.save_to_database(result)
            return game_id
        return None

    def process_all_images(self, folder="images"):
        """Process all images in the specified folder"""
        if not os.path.exists(folder):
            print(f"❌ Folder not found: {folder}")
            return

        # Get all image files
        image_files = [f for f in os.listdir(folder) if f.lower().endswith(('.jpg', '.png', '.jpeg'))]

        if not image_files:
            print(f"❌ No images found in {folder}")
            return

        print(f"🚀 Processing ALL {len(image_files)} images in {folder} folder...")
        print("=" * 60)

        successful = 0
        failed = 0

        for i, filename in enumerate(image_files):
            image_path = os.path.join(folder, filename)

            print(f"\n📷 [{i+1}/{len(image_files)}] Processing: {filename}")
            print("-" * 50)

            try:
                game_id = self.process_image(image_path)
                if game_id:
                    successful += 1
                    print(f"✅ Success! Game ID: {game_id}")
                else:
                    failed += 1
                    print("❌ Failed to process")
            except Exception as e:
                failed += 1
                print(f"❌ Error: {e}")

            # Auto-continue without user input (small delay for readability)
            if i < len(image_files) - 1:
                import time
                time.sleep(0.5)  # Small delay to see results

        print(f"\n🎉 BATCH PROCESSING COMPLETE!")
        print("=" * 50)
        print(f"✅ Successfully processed: {successful}")
        print(f"❌ Failed: {failed}")
        print(f"📊 Total: {successful + failed}/{len(image_files)}")

        if successful > 0:
            print(f"\n📋 Check database for {successful} new game records!")

        return successful
    
    def view_database(self):
        """View database contents"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM poker_games")
        count = cursor.fetchone()[0]
        
        if count == 0:
            print("📊 Database is empty")
            conn.close()
            return
        
        print(f"\n📊 DATABASE CONTENTS ({count} games)")
        print("=" * 70)
        
        cursor.execute("""
            SELECT id, timestamp, winning_seat, seat_a_suits, seat_b_suits, seat_c_suits 
            FROM poker_games 
            ORDER BY timestamp DESC 
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        
        for row in results:
            game_id, timestamp, winning_seat, a_suits, b_suits, c_suits = row
            timestamp = timestamp[:19]
            
            print(f"Game {game_id:3d} | {timestamp} | Winner: {winning_seat or 'None':7s} | A:{a_suits} B:{b_suits} C:{c_suits}")
        
        conn.close()


def main():
    """Main function"""
    print("🚀 Quick Capture - Visual Poker Detection")
    print("=" * 45)

    # Initialize with your model path
    model_path = "models/capture.pt"
    capture = QuickCapture(model_path)

    if not capture.model:
        print("❌ Cannot proceed without model")
        return

    # Quick start option
    print("\n🎯 Quick Start Options:")
    print("A. 🚀 Process ALL images in images folder NOW")
    print("B. 📋 Show menu for individual options")

    quick_choice = input("\nChoose (A/B): ").strip().upper()

    if quick_choice == 'A':
        print("\n🚀 Starting batch processing of all images...")
        capture.process_all_images()
        return
    
    while True:
        print("\n📋 Options:")
        print("1. 🔍 Process single image (enter path)")
        print("2. 📁 Choose one image from images folder")
        print("3. 🚀 Process ALL images in images folder")
        print("4. 📊 View database")
        print("5. ❌ Exit")

        choice = input("\nEnter choice (1-5): ").strip()

        if choice == '1':
            print("\n💡 Example paths:")
            print("   images/Screenshot_2025-05-06-22-27-32-36_cropped.jpg")
            print("   D:\\Machine Learning Journey\\...\\images\\Screenshot_2025-05-06-22-27-32-36_cropped.jpg")
            image_path = input("\nEnter image path: ").strip()
            if image_path:
                capture.process_image(image_path)

        elif choice == '2':
            # Show available images in images folder
            images_folder = "images"
            if os.path.exists(images_folder):
                image_files = [f for f in os.listdir(images_folder) if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
                if image_files:
                    print(f"\n📸 Available images in {images_folder} ({len(image_files)} total):")
                    for i, img in enumerate(image_files[:10]):  # Show first 10
                        print(f"   {i+1}. {img}")
                    if len(image_files) > 10:
                        print(f"   ... and {len(image_files) - 10} more")

                    try:
                        img_choice = int(input(f"\nChoose image (1-{min(10, len(image_files))}): ")) - 1
                        if 0 <= img_choice < len(image_files):
                            selected_image = os.path.join(images_folder, image_files[img_choice])
                            capture.process_image(selected_image)
                        else:
                            print("❌ Invalid selection")
                    except ValueError:
                        print("❌ Please enter a number")
                else:
                    print(f"❌ No images found in {images_folder} folder")
            else:
                print(f"❌ {images_folder} folder not found")

        elif choice == '3':
            # Process ALL images in folder
            capture.process_all_images()

        elif choice == '4':
            capture.view_database()

        elif choice == '5':
            print("👋 Goodbye!")
            break

        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    main()
