"""
Quick Capture - Direct Model Path Version
========================================

Uses your specific model path to capture winning seat card suits.
"""

import cv2
import numpy as np
import sqlite3
import json
from datetime import datetime
import os
from ultralytics import YOLO

class QuickCapture:
    """
    Quick capture system with direct model path
    """
    
    def __init__(self, model_path="models/capture.pt"):
        self.model_path = model_path
        self.model = None
        self.db_path = "poker_results.db"
        self.init_database()
        self.load_model()
    
    def load_model(self):
        """Load the YOLO model"""
        if not os.path.exists(self.model_path):
            print(f"❌ Model not found: {self.model_path}")
            return False
        
        try:
            self.model = YOLO(self.model_path)
            print(f"✅ Model loaded: {self.model_path}")
            return True
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False
    
    def init_database(self):
        """Initialize database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS poker_games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                image_path TEXT NOT NULL,
                winning_seat TEXT,
                seat_a_suits TEXT,
                seat_b_suits TEXT, 
                seat_c_suits TEXT,
                all_detections TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        print(f"✅ Database ready: {self.db_path}")
    
    def detect_and_analyze(self, image_path):
        """Run detection and analyze results"""
        if not self.model:
            print("❌ Model not loaded")
            return None
        
        if not os.path.exists(image_path):
            print(f"❌ Image not found: {image_path}")
            return None
        
        print(f"🔍 Analyzing: {os.path.basename(image_path)}")
        
        # Run detection
        results = self.model(image_path, conf=0.3, verbose=False)
        
        # Parse results
        detections = []
        if results and len(results) > 0:
            for result in results:
                if result.boxes is not None:
                    for box in result.boxes:
                        class_id = int(box.cls[0])
                        confidence = float(box.conf[0])
                        bbox = box.xyxy[0].cpu().numpy()
                        
                        class_name = self.model.names[class_id]
                        
                        detections.append({
                            'class': class_name,
                            'confidence': confidence,
                            'bbox': bbox.tolist(),
                            'center_x': float((bbox[0] + bbox[2]) / 2),
                            'center_y': float((bbox[1] + bbox[3]) / 2)
                        })
        
        print(f"📊 Found {len(detections)} detections")
        
        if len(detections) == 0:
            print("❌ No detections found")
            return None
        
        # Show all detections
        print("\n🔍 All Detections:")
        for i, det in enumerate(detections):
            print(f"   {i+1:2d}. {det['class']:15s} (conf: {det['confidence']:.3f})")
        
        # Analyze seats and suits
        analysis = self.analyze_seats_and_suits(detections)
        
        return {
            'detections': detections,
            'analysis': analysis,
            'image_path': image_path
        }
    
    def analyze_seats_and_suits(self, detections):
        """Analyze detections for seats and suits"""
        seats = []
        suits = []
        cards = []
        winning_info = None
        
        for det in detections:
            class_name = det['class'].lower()
            
            # Check for seats
            if class_name in ['a', 'b', 'c']:
                seats.append(det)
            elif 'seat' in class_name:
                if 'winning' in class_name:
                    winning_info = det
                seats.append(det)
            
            # Check for suits
            elif class_name in ['spades', 'hearts', 'diamonds', 'clubs']:
                suits.append(det)
            
            # Check for cards
            elif class_name in ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'j', 'q', 'k', 'a']:
                cards.append(det)
        
        print(f"\n🎯 Analysis:")
        print(f"   Seats: {len(seats)}")
        print(f"   Suits: {len(suits)}")
        print(f"   Cards: {len(cards)}")
        print(f"   Winning info: {'Yes' if winning_info else 'No'}")
        
        # Associate suits with seats
        seat_suits = self.associate_suits_with_seats(seats, suits)
        
        return {
            'seats': seats,
            'suits': suits,
            'cards': cards,
            'winning_info': winning_info,
            'seat_suits': seat_suits
        }
    
    def associate_suits_with_seats(self, seats, suits):
        """Associate suits with seats based on proximity"""
        seat_suits = {'A': [], 'B': [], 'C': []}
        
        for suit in suits:
            closest_seat = None
            min_distance = float('inf')
            
            for seat in seats:
                distance = np.sqrt((suit['center_x'] - seat['center_x'])**2 + 
                                 (suit['center_y'] - seat['center_y'])**2)
                if distance < min_distance:
                    min_distance = distance
                    closest_seat = seat
            
            if closest_seat:
                seat_name = self.get_seat_name(closest_seat['class'])
                if seat_name in seat_suits:
                    seat_suits[seat_name].append(suit['class'])
        
        return seat_suits
    
    def get_seat_name(self, class_name):
        """Convert class name to seat letter"""
        class_name = class_name.lower()
        
        if 'a' in class_name or 'blue' in class_name:
            return 'A'
        elif 'b' in class_name or 'golden' in class_name or 'yellow' in class_name:
            return 'B'
        elif 'c' in class_name or 'purple' in class_name:
            return 'C'
        else:
            return 'Unknown'
    
    def save_to_database(self, result):
        """Save results to database"""
        if not result:
            return None
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        timestamp = datetime.now().isoformat()
        analysis = result['analysis']
        
        # Determine winning seat
        winning_seat = None
        if analysis['winning_info']:
            winning_seat = self.get_seat_name(analysis['winning_info']['class'])
        
        # Get seat suits
        seat_suits = analysis['seat_suits']
        
        cursor.execute('''
            INSERT INTO poker_games 
            (timestamp, image_path, winning_seat, seat_a_suits, seat_b_suits, seat_c_suits, all_detections)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            timestamp,
            result['image_path'],
            winning_seat,
            json.dumps(seat_suits['A']),
            json.dumps(seat_suits['B']),
            json.dumps(seat_suits['C']),
            json.dumps(result['detections'], default=str)
        ))
        
        game_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        # Print results
        print(f"\n💾 SAVED TO DATABASE (Game ID: {game_id})")
        print("=" * 50)
        print(f"🏆 Winning Seat: {winning_seat or 'Not detected'}")
        print(f"🎯 Seat A Suits: {seat_suits['A']}")
        print(f"🎯 Seat B Suits: {seat_suits['B']}")
        print(f"🎯 Seat C Suits: {seat_suits['C']}")
        
        return game_id
    
    def process_image(self, image_path):
        """Process a single image"""
        result = self.detect_and_analyze(image_path)
        if result:
            game_id = self.save_to_database(result)
            return game_id
        return None
    
    def view_database(self):
        """View database contents"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM poker_games")
        count = cursor.fetchone()[0]
        
        if count == 0:
            print("📊 Database is empty")
            conn.close()
            return
        
        print(f"\n📊 DATABASE CONTENTS ({count} games)")
        print("=" * 70)
        
        cursor.execute("""
            SELECT id, timestamp, winning_seat, seat_a_suits, seat_b_suits, seat_c_suits 
            FROM poker_games 
            ORDER BY timestamp DESC 
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        
        for row in results:
            game_id, timestamp, winning_seat, a_suits, b_suits, c_suits = row
            timestamp = timestamp[:19]
            
            print(f"Game {game_id:3d} | {timestamp} | Winner: {winning_seat or 'None':7s} | A:{a_suits} B:{b_suits} C:{c_suits}")
        
        conn.close()


def main():
    """Main function"""
    print("🚀 Quick Capture - Winning Seat Detection")
    print("=" * 45)
    
    # Initialize with your model path
    model_path = "models/capture.pt"
    capture = QuickCapture(model_path)
    
    if not capture.model:
        print("❌ Cannot proceed without model")
        return
    
    while True:
        print("\n📋 Options:")
        print("1. 🔍 Process single image")
        print("2. 📊 View database")
        print("3. ❌ Exit")
        
        choice = input("\nEnter choice (1-3): ").strip()
        
        if choice == '1':
            image_path = input("Enter image path: ").strip()
            capture.process_image(image_path)
        
        elif choice == '2':
            capture.view_database()
        
        elif choice == '3':
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    main()
