# PolitePredict Troubleshooting Guide

## Quick Fixes for Common Issues

### 🚨 **Issue: "tesseract is not installed or it's not in your PATH"**

**What it means:** The OCR engine (Tesseract) is not installed or not accessible.

**Quick Fix:**
```bash
# Run the setup script
python setup_tesseract.py

# Or install manually:
# Windows: Download from https://github.com/UB-Mannheim/tesseract/releases
# Mac: brew install tesseract  
# Linux: sudo apt-get install tesseract-ocr
```

**Verification:**
```bash
tesseract --version
```

### 🚨 **Issue: "Error auto-saving captured data: 'PolitePredict' object has no attribute 'save_to_database'"**

**What it means:** Code method name mismatch (already fixed in latest version).

**Quick Fix:** Update to the latest version of the code or restart the application.

### 🚨 **Issue: "Could not reliably determine winning seat from image"**

**What it means:** The system can't detect which seat won from the screenshot.

**Possible Causes & Fixes:**

1. **Game not clearly visible:**
   - Ensure game window is unobstructed
   - Check that cards and seats are clearly visible
   - Avoid overlapping windows

2. **Wrong crop area:**
   - The `CROP_BOX = (970, 388, 1350, 632)` might not match your screen
   - Adjust coordinates based on your game position

3. **Poor image quality:**
   - Increase screen resolution
   - Improve lighting/contrast
   - Ensure game is not minimized

### 🚨 **Issue: "Warning: Card 3 ROI for seat B is invalid or out of bounds"**

**What it means:** The coordinates for detecting cards are wrong for your game layout.

**Quick Fix:**
1. Take a screenshot of your game
2. Note the exact positions of cards for each seat
3. Adjust the ROI coordinates in the code
4. Or use the "🎮 GAME ON" intelligent mode which auto-adjusts

### 🚨 **Issue: Game detection not working**

**Symptoms:**
- "Game area detected by patterns" not showing
- Auto-detection fails to find game

**Solutions:**

1. **Manual positioning:**
   ```python
   # Adjust CROP_BOX to match your game position
   CROP_BOX = (your_x, your_y, your_x+width, your_y+height)
   ```

2. **Use intelligent mode:**
   - Click "🎮 GAME ON" - it auto-detects game position
   - Let it learn your specific game layout

3. **Check game visibility:**
   - Game should have distinct card-like shapes
   - Avoid games with too much animation
   - Ensure consistent game layout

## Step-by-Step Troubleshooting

### Step 1: Verify Installation
```bash
# Check Python
python --version

# Check required packages
pip list | grep -E "(opencv|numpy|pandas|pillow|pytesseract)"

# Check Tesseract
tesseract --version
```

### Step 2: Test Basic Functionality
```bash
# Run the application
python polite_predict.py

# Check if GUI opens
# Check if database is created (game_data.db)
# Try manual screenshot capture first
```

### Step 3: Test OCR
1. Click "Capture Screenshot" manually
2. Check if any text is detected
3. If OCR fails, install/reinstall Tesseract

### Step 4: Test Game Detection
1. Open your game clearly on screen
2. Try "🎮 GAME ON" for automatic detection
3. Or manually adjust CROP_BOX coordinates

### Step 5: Test Data Saving
1. Try manual data entry first
2. Click "Save Data" to test database
3. Check if data appears in the application

## Configuration for Your Specific Game

### For Your Card Game Layout:

Based on your screenshot, here are the recommended settings:

```python
# Main crop area (adjust based on your game position)
CROP_BOX = (700, 280, 1000, 550)  # Adjust these coordinates

# If using BlueStacks, typical coordinates:
CROP_BOX = (970, 388, 1350, 632)  # Default BlueStacks position
```

### Seat Detection Areas:
```python
# The system now auto-detects these, but you can manually adjust:
highlight_rois = {
    'A': [200:240, 30:130],   # Left seat
    'B': [200:240, 140:240], # Middle seat  
    'C': [200:240, 250:350]  # Right seat
}
```

## Performance Optimization

### For Better OCR Accuracy:
1. **Higher Resolution:** Use 1080p or higher
2. **Good Contrast:** Ensure cards are clearly visible
3. **Stable Position:** Keep game window in same position
4. **Minimal Animation:** Avoid games with too much movement

### For Better Detection:
1. **Consistent Layout:** Use games with fixed card positions
2. **Clear Boundaries:** Cards should have distinct edges
3. **Good Lighting:** Avoid dark or overly bright displays

## Advanced Troubleshooting

### Debug Mode:
Add these lines to see what's happening:
```python
# Add at the top of polite_predict.py
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Manual ROI Testing:
```python
# Test specific coordinates
img = cv2.imread('screenshot.png')
roi = img[y1:y2, x1:x2]  # Your coordinates
cv2.imshow('ROI Test', roi)
cv2.waitKey(0)
```

### Database Issues:
```bash
# Check database
sqlite3 game_data.db
.tables
SELECT * FROM backend_data LIMIT 5;
.quit
```

## Getting Help

### Before Asking for Help:
1. ✅ Run `python setup_tesseract.py`
2. ✅ Try "🎮 GAME ON" intelligent mode
3. ✅ Check that your game is clearly visible
4. ✅ Verify Tesseract installation: `tesseract --version`
5. ✅ Try manual screenshot capture first

### Information to Include:
- Your operating system (Windows/Mac/Linux)
- Screen resolution
- Game type and layout
- Error messages (copy exact text)
- Screenshot of your game
- Python version: `python --version`

### Common Solutions Summary:

| Problem | Quick Fix |
|---------|-----------|
| Tesseract errors | `python setup_tesseract.py` |
| Game not detected | Use "🎮 GAME ON" mode |
| Wrong coordinates | Let intelligent mode auto-adjust |
| OCR fails | Check game visibility and contrast |
| Database errors | Restart application |
| Poor accuracy | Use higher resolution, better lighting |

### Emergency Fallback:
If nothing works, you can still use the application manually:
1. Disable auto-detection
2. Enter data manually
3. Use "Train Now" for predictions
4. The AI will still work with manual data entry

## Success Indicators

You'll know it's working when you see:
- ✅ "Game area detected by patterns at (coordinates)"
- ✅ "Brightness values: {'A': X, 'B': Y, 'C': Z}. Determined winner: [seat]"
- ✅ "[timestamp] Auto-captured: Seat X, Trend: Y"
- ✅ "[timestamp] Auto-saved: Seat X, Cards: [...], Trend: Y"

The system is designed to be robust and self-healing, so most issues resolve themselves once the basic setup is correct.
