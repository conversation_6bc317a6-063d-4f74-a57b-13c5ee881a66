import cv2
import os

# ===== CONFIGURATION =====
# Apne screenshots folder ka path yahan likhein
IMG_DIR = r"C:\Users\<USER>\Desktop\new game\screenshots"   # <-- Aapka sahi folder path
LABEL_DIR = "labels"        # Labels isi folder me save honge
os.makedirs(LABEL_DIR, exist_ok=True)

# Define your classes (order will be class indices)
CLASSES = [
    "card_digit",     # e.g. 6, 10, K, Q, A
    "card_suit",      # e.g. heart, diamond, club, spade
    "pattern",        # e.g. Pair, High Card, Sequence, Color, etc.
    "winning_seat"
]

drawing = False
ix, iy = -1, -1
boxes = []
img = None
clone = None

def draw_rectangle(event, x, y, flags, param):
    global ix, iy, drawing, img, boxes, clone

    if event == cv2.EVENT_LBUTTONDOWN:
        drawing = True
        ix, iy = x, y

    elif event == cv2.EVENT_MOUSEMOVE:
        if drawing:
            temp_img = clone.copy()
            cv2.rectangle(temp_img, (ix, iy), (x, y), (0, 255, 0), 2)
            cv2.imshow("image", temp_img)

    elif event == cv2.EVENT_LBUTTONUP:
        drawing = False
        x1, y1 = min(ix, x), min(iy, y)
        x2, y2 = max(ix, x), max(iy, y)
        # Show class options
        print("\n--- Select class for this box ---")
        for i, name in enumerate(CLASSES):
            print(f"{i}: {name}")
        while True:
            try:
                class_id = int(input("Class id: "))
                if 0 <= class_id < len(CLASSES):
                    break
                else:
                    print("Invalid class id. Try again.")
            except ValueError:
                print("Enter a valid number.")
        boxes.append((class_id, x1, y1, x2, y2))
        cv2.rectangle(img, (x1, y1), (x2, y2), (0,255,0), 2)
        clone = img.copy()  # update clone with current state

def normalize_bbox(x1, y1, x2, y2, w, h):
    # YOLO: class cx cy w h (all normalized)
    cx = ((x1 + x2) / 2) / w
    cy = ((y1 + y2) / 2) / h
    bw = (x2 - x1) / w
    bh = (y2 - y1) / h
    return cx, cy, bw, bh

def annotate_image(img_path, label_path):
    global img, clone, boxes
    img = cv2.imread(img_path)
    if img is None:
        print(f"Error loading image: {img_path}")
        return
    h, w = img.shape[:2]
    clone = img.copy()
    boxes = []
    cv2.namedWindow("image")
    cv2.setMouseCallback("image", draw_rectangle)
    print(f"\n=== Annotating: {os.path.basename(img_path)} ===")
    print("Instructions: Draw box with mouse. After each box, enter class id.")
    print("Keys: [s]=save & next | [r]=reset boxes | [q]=skip image | [esc]=exit\n")
    while True:
        cv2.imshow("image", img)
        key = cv2.waitKey(1) & 0xFF
        if key == ord("r"):  # Reset all boxes
            img = clone.copy()
            boxes = []
            print("Boxes reset.")
        if key == ord("s"):  # Save and next
            with open(label_path, "w") as f:
                for (class_id, x1, y1, x2, y2) in boxes:
                    cx, cy, bw, bh = normalize_bbox(x1, y1, x2, y2, w, h)
                    f.write(f"{class_id} {cx:.6f} {cy:.6f} {bw:.6f} {bh:.6f}\n")
            print(f"Labels saved: {label_path}")
            break
        if key == ord("q"):  # Skip this image
            print("Image skipped.")
            break
        if key == 27:        # ESC to exit
            print("Exiting...")
            exit(0)
    cv2.destroyAllWindows()

def main():
    images = [f for f in os.listdir(IMG_DIR) if f.lower().endswith((".jpg", ".jpeg", ".png"))]
    images.sort()
    total = len(images)
    if total == 0:
        print(f"No images found in {IMG_DIR}!")
        return
    print(f"Found {total} images in '{IMG_DIR}'. Starting annotation...")
    for idx, fname in enumerate(images):
        img_path = os.path.join(IMG_DIR, fname)
        label_path = os.path.join(LABEL_DIR, os.path.splitext(fname)[0] + ".txt")
        print(f"\n[{idx+1}/{total}] {fname}")
        annotate_image(img_path, label_path)
    print("\nAnnotation complete! All labels saved in 'labels' folder.")

if __name__ == "__main__":
    main()