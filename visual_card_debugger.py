"""
Visual Card Recognition Debugger
===============================

This system saves predicted images with visual annotations so you can see
exactly what the AI is detecting and verify if predictions are correct.

Author: Augment Agent
"""

import cv2
import numpy as np
import os
from datetime import datetime
from PIL import ImageGrab, Image, ImageDraw, ImageFont
from advanced_card_recognition import AdvancedCardRecognizer
import time

class VisualCardDebugger:
    """
    Visual debugger for card recognition with image saving
    """
    
    def __init__(self, save_path="debug_images/"):
        self.save_path = save_path
        os.makedirs(save_path, exist_ok=True)
        
        # Initialize the advanced recognizer
        self.recognizer = AdvancedCardRecognizer()
        self.recognizer.load_model()
        
        # Create subdirectories
        os.makedirs(os.path.join(save_path, "live_predictions"), exist_ok=True)
        os.makedirs(os.path.join(save_path, "card_regions"), exist_ok=True)
        os.makedirs(os.path.join(save_path, "annotated"), exist_ok=True)
        
        # Colors for visualization
        self.suit_colors = {
            '♠': (0, 0, 0),      # Black
            '♣': (0, 0, 0),      # Black  
            '♦': (0, 0, 255),    # Red
            '♥': (0, 0, 255),    # Red
            '?': (128, 128, 128) # Gray
        }
        
    def capture_and_debug(self, crop_box=(970, 388, 1350, 632)):
        """
        Capture game image and create detailed debug visualization
        """
        # Capture screenshot
        img = ImageGrab.grab(bbox=crop_box)
        img_np = np.array(img)
        img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save original image
        original_path = os.path.join(self.save_path, f"original_{timestamp}.jpg")
        cv2.imwrite(original_path, img_bgr)
        
        # Make predictions
        predictions = self.recognizer.predict_suits(img_bgr)
        
        if predictions:
            # Create annotated version
            annotated_image = self.create_annotated_image(img_bgr, predictions, timestamp)
            
            # Save individual card regions
            self.save_card_regions(img_bgr, predictions, timestamp)
            
            # Create summary report
            self.create_prediction_report(predictions, timestamp)
            
            print(f"🖼️ Debug images saved with timestamp: {timestamp}")
            print(f"📁 Check folder: {self.save_path}")
            
            return annotated_image, predictions, timestamp
        
        return None, None, timestamp
    
    def create_annotated_image(self, image, predictions, timestamp):
        """
        Create annotated image with predictions and confidence scores
        """
        # Create a copy for annotation
        annotated = image.copy()
        
        # Draw card boundaries and predictions
        for pred in predictions:
            position = self.recognizer.card_positions[pred['position']]
            x, y, w, h = position['x'], position['y'], position['width'], position['height']
            
            # Get color based on confidence
            confidence = pred['confidence']
            if confidence > 90:
                box_color = (0, 255, 0)  # Green - high confidence
            elif confidence > 70:
                box_color = (0, 255, 255)  # Yellow - medium confidence
            else:
                box_color = (0, 0, 255)  # Red - low confidence
            
            # Draw bounding box
            cv2.rectangle(annotated, (x, y), (x + w, y + h), box_color, 2)
            
            # Draw suit symbol and confidence
            suit = pred['suit']
            conf_text = f"{suit} {confidence:.0f}%"
            
            # Text background
            text_size = cv2.getTextSize(conf_text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            cv2.rectangle(annotated, (x, y - 25), (x + text_size[0] + 10, y), box_color, -1)
            
            # Text
            cv2.putText(annotated, conf_text, (x + 5, y - 8), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            # Draw position number
            cv2.putText(annotated, str(pred['position']), (x + 5, y + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Add title and timestamp
        title = f"Card Suit Predictions - {timestamp}"
        cv2.putText(annotated, title, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        # Add average confidence
        avg_conf = np.mean([p['confidence'] for p in predictions])
        conf_text = f"Average Confidence: {avg_conf:.1f}%"
        cv2.putText(annotated, conf_text, (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # Save annotated image
        annotated_path = os.path.join(self.save_path, "annotated", f"annotated_{timestamp}.jpg")
        cv2.imwrite(annotated_path, annotated)
        
        return annotated
    
    def save_card_regions(self, image, predictions, timestamp):
        """
        Save individual card regions for detailed inspection
        """
        card_folder = os.path.join(self.save_path, "card_regions", timestamp)
        os.makedirs(card_folder, exist_ok=True)
        
        for pred in predictions:
            position = self.recognizer.card_positions[pred['position']]
            x, y, w, h = position['x'], position['y'], position['width'], position['height']
            
            # Extract card region
            card_region = image[y:y+h, x:x+w]
            
            if card_region.size > 0:
                # Save original card region
                card_filename = f"card_{pred['position']:02d}_{pred['suit']}_{pred['confidence']:.0f}pct.jpg"
                card_path = os.path.join(card_folder, card_filename)
                cv2.imwrite(card_path, card_region)
                
                # Create enhanced version for better visibility
                enhanced = self.enhance_card_for_viewing(card_region)
                enhanced_filename = f"enhanced_{pred['position']:02d}_{pred['suit']}_{pred['confidence']:.0f}pct.jpg"
                enhanced_path = os.path.join(card_folder, enhanced_filename)
                cv2.imwrite(enhanced_path, enhanced)
    
    def enhance_card_for_viewing(self, card_region):
        """
        Enhance card region for better visual inspection
        """
        # Resize for better viewing
        scale_factor = 3
        height, width = card_region.shape[:2]
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        
        enhanced = cv2.resize(card_region, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        # Apply contrast enhancement
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        if len(enhanced.shape) == 3:
            lab = cv2.cvtColor(enhanced, cv2.COLOR_BGR2LAB)
            lab[:,:,0] = clahe.apply(lab[:,:,0])
            enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
        else:
            enhanced = clahe.apply(enhanced)
        
        return enhanced
    
    def create_prediction_report(self, predictions, timestamp):
        """
        Create a text report of predictions
        """
        report_path = os.path.join(self.save_path, f"report_{timestamp}.txt")

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"Card Suit Prediction Report\n")
            f.write(f"Timestamp: {timestamp}\n")
            f.write(f"=" * 50 + "\n\n")
            
            # Individual predictions
            f.write("Individual Card Predictions:\n")
            f.write("-" * 30 + "\n")
            for pred in predictions:
                f.write(f"Card {pred['position']:2d} (Row {pred['row']}, Col {pred['col']}): ")
                f.write(f"{pred['suit']} (Confidence: {pred['confidence']:.1f}%)\n")
            
            # Grid layout
            f.write(f"\n3x3 Grid Layout:\n")
            f.write("-" * 20 + "\n")
            for row in range(3):
                row_suits = []
                for col in range(3):
                    pos = row * 3 + col
                    pred = next(p for p in predictions if p['position'] == pos)
                    row_suits.append(f"{pred['suit']}({pred['confidence']:.0f}%)")
                f.write(f"   {' '.join(row_suits)}\n")
            
            # Statistics
            confidences = [p['confidence'] for p in predictions]
            f.write(f"\nStatistics:\n")
            f.write("-" * 15 + "\n")
            f.write(f"Average Confidence: {np.mean(confidences):.1f}%\n")
            f.write(f"Min Confidence: {np.min(confidences):.1f}%\n")
            f.write(f"Max Confidence: {np.max(confidences):.1f}%\n")
            f.write(f"Std Deviation: {np.std(confidences):.1f}%\n")
            
            # Suit distribution
            from collections import Counter
            suit_counts = Counter([p['suit'] for p in predictions])
            f.write(f"\nSuit Distribution:\n")
            f.write("-" * 20 + "\n")
            for suit, count in suit_counts.items():
                f.write(f"{suit}: {count} cards\n")
    
    def live_debug_mode(self, crop_box=(970, 388, 1350, 632), interval=15):
        """
        Live debugging mode that saves images continuously
        """
        print("🎮 Starting Live Debug Mode...")
        print(f"📁 Images will be saved to: {self.save_path}")
        print(f"⏱️ Capturing every {interval} seconds")
        print("Press Ctrl+C to stop")
        
        capture_count = 0
        
        try:
            while True:
                capture_count += 1
                print(f"\n📸 Capture #{capture_count}")
                
                # Capture and debug
                annotated, predictions, timestamp = self.capture_and_debug(crop_box)
                
                if predictions:
                    # Display results
                    print(f"🃏 Predictions for {timestamp}:")
                    for row in range(3):
                        row_display = []
                        for col in range(3):
                            pos = row * 3 + col
                            pred = next(p for p in predictions if p['position'] == pos)
                            row_display.append(f"{pred['suit']}({pred['confidence']:.0f}%)")
                        print(f"   {' '.join(row_display)}")
                    
                    avg_confidence = np.mean([p['confidence'] for p in predictions])
                    print(f"   Average Confidence: {avg_confidence:.1f}%")
                    
                    # Show file locations
                    print(f"📁 Files saved:")
                    print(f"   - Original: original_{timestamp}.jpg")
                    print(f"   - Annotated: annotated/annotated_{timestamp}.jpg")
                    print(f"   - Cards: card_regions/{timestamp}/")
                    print(f"   - Report: report_{timestamp}.txt")
                else:
                    print("❌ No predictions generated")
                
                print(f"⏳ Waiting {interval} seconds for next capture...")
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print(f"\n🛑 Live debug mode stopped after {capture_count} captures")
            print(f"📁 All debug files saved in: {self.save_path}")
    
    def analyze_card_positions(self, crop_box=(970, 388, 1350, 632)):
        """
        Analyze and visualize card positions for adjustment
        """
        print("🔍 Analyzing card positions...")
        
        # Capture image
        img = ImageGrab.grab(bbox=crop_box)
        img_np = np.array(img)
        img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
        
        # Draw all card positions
        position_image = img_bgr.copy()
        
        for i, position in enumerate(self.recognizer.card_positions):
            x, y, w, h = position['x'], position['y'], position['width'], position['height']
            
            # Draw rectangle
            cv2.rectangle(position_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            # Draw position number
            cv2.putText(position_image, str(i), (x + 5, y + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            
            # Draw center point
            center_x, center_y = x + w//2, y + h//2
            cv2.circle(position_image, (center_x, center_y), 3, (0, 0, 255), -1)
        
        # Save position analysis
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        position_path = os.path.join(self.save_path, f"card_positions_{timestamp}.jpg")
        cv2.imwrite(position_path, position_image)
        
        print(f"📁 Card position analysis saved: {position_path}")
        print("🔧 Use this image to verify if card positions are correct")
        print("💡 If positions are wrong, adjust coordinates in AdvancedCardRecognizer.get_precise_card_positions()")
        
        return position_path


def main():
    """Main function for visual debugging"""
    print("🔍 Visual Card Recognition Debugger")
    print("=" * 40)
    
    debugger = VisualCardDebugger()
    
    while True:
        print("\n📋 Choose debugging option:")
        print("1. 📸 Single capture with debug images")
        print("2. 🎮 Live debug mode (continuous capture)")
        print("3. 🔧 Analyze card positions")
        print("4. 📁 Open debug folder")
        print("5. ❌ Exit")
        
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == '1':
            print("\n📸 Single capture with debug...")
            annotated, predictions, timestamp = debugger.capture_and_debug()
            
            if predictions:
                print("✅ Debug images created successfully!")
                print(f"📁 Check folder: {debugger.save_path}")
            else:
                print("❌ Failed to generate predictions")
        
        elif choice == '2':
            interval = input("⏱️ Enter capture interval in seconds (default 15): ").strip()
            try:
                interval = int(interval) if interval else 15
            except:
                interval = 15
            
            debugger.live_debug_mode(interval=interval)
        
        elif choice == '3':
            print("\n🔧 Analyzing card positions...")
            position_path = debugger.analyze_card_positions()
            print(f"✅ Position analysis complete: {position_path}")
        
        elif choice == '4':
            import subprocess
            import platform
            
            try:
                if platform.system() == 'Windows':
                    subprocess.run(['explorer', debugger.save_path])
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.run(['open', debugger.save_path])
                else:  # Linux
                    subprocess.run(['xdg-open', debugger.save_path])
                print(f"📁 Opened folder: {debugger.save_path}")
            except:
                print(f"📁 Please manually open: {debugger.save_path}")
        
        elif choice == '5':
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    main()
