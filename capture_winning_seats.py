"""
Capture Winning Seat Card Suits - Simple Version
===============================================

Uses your existing trained model to capture winning seat (A, B, C) card suits
and store them in a database.

Author: Augment Agent
"""

import cv2
import numpy as np
import sqlite3
import json
from datetime import datetime
import os
import glob

class WinningSeatCapture:
    """
    Simple system to capture winning seat card suits using your trained model
    """
    
    def __init__(self):
        self.db_path = "poker_results.db"
        self.init_database()
        
    def init_database(self):
        """Initialize SQLite database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS poker_games (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                image_path TEXT NOT NULL,
                winning_seat TEXT,
                seat_a_suits TEXT,
                seat_b_suits TEXT, 
                seat_c_suits TEXT,
                seat_a_cards TEXT,
                seat_b_cards TEXT,
                seat_c_cards TEXT,
                all_detections TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        print(f"✅ Database ready: {self.db_path}")
    
    def run_yolo_detection(self, image_path):
        """
        Run YOLO detection using your trained model
        """
        print(f"🔍 Running detection on: {os.path.basename(image_path)}")

        # Import YOLO (assuming you have ultralytics installed)
        try:
            from ultralytics import YOLO

            # Look for model files in multiple locations
            model_paths = [
                "models/capture.pt",  # Your specific model
                "models/best.pt",
                "best.pt",
                "capture.pt"
            ]

            # Also search in current directory and models folder
            for folder in ['.', 'models']:
                if os.path.exists(folder):
                    for file in os.listdir(folder):
                        if file.endswith('.pt'):
                            model_paths.append(os.path.join(folder, file))

            # Find the first existing model
            model_path = None
            for path in model_paths:
                if os.path.exists(path):
                    model_path = path
                    break

            if not model_path:
                print("❌ No .pt model file found in current directory or models folder")
                print("🔍 Searched for:")
                for path in model_paths[:4]:  # Show first 4 search paths
                    print(f"   - {path}")
                return None

            print(f"📁 Using model: {model_path}")

            model = YOLO(model_path)
            
            # Run detection
            results = model(image_path, conf=0.3, verbose=False)
            
            # Parse results
            detections = []
            if results and len(results) > 0:
                for result in results:
                    if result.boxes is not None:
                        for box in result.boxes:
                            class_id = int(box.cls[0])
                            confidence = float(box.conf[0])
                            bbox = box.xyxy[0].cpu().numpy()
                            
                            class_name = model.names[class_id]
                            
                            detections.append({
                                'class': class_name,
                                'confidence': confidence,
                                'bbox': bbox.tolist(),
                                'center_x': float((bbox[0] + bbox[2]) / 2),
                                'center_y': float((bbox[1] + bbox[3]) / 2)
                            })
            
            print(f"📊 Found {len(detections)} detections")
            return detections
            
        except ImportError:
            print("❌ ultralytics not installed. Install with: pip install ultralytics")
            return None
        except Exception as e:
            print(f"❌ Error running detection: {e}")
            return None
    
    def analyze_detections(self, detections):
        """
        Analyze detections to extract seat information
        """
        if not detections:
            return None
        
        # Separate different types of detections
        seats = []
        suits = []
        cards = []
        winning_seat = None
        
        for det in detections:
            class_name = det['class'].lower()
            
            # Identify seats
            if class_name in ['a', 'b', 'c']:
                seats.append(det)
            elif 'seat' in class_name:
                if 'winning' in class_name:
                    winning_seat = det
                seats.append(det)
            
            # Identify suits
            elif class_name in ['spades', 'hearts', 'diamonds', 'clubs']:
                suits.append(det)
            
            # Identify cards
            elif class_name in ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'j', 'q', 'k', 'a']:
                cards.append(det)
        
        print(f"🎯 Analysis: {len(seats)} seats, {len(suits)} suits, {len(cards)} cards")
        
        # Associate suits and cards with seats based on proximity
        seat_data = self.associate_with_seats(seats, suits, cards)
        
        return {
            'seats': seats,
            'suits': suits,
            'cards': cards,
            'winning_seat': winning_seat,
            'seat_data': seat_data
        }
    
    def associate_with_seats(self, seats, suits, cards):
        """
        Associate suits and cards with seats based on proximity
        """
        seat_data = {'A': {'suits': [], 'cards': []}, 'B': {'suits': [], 'cards': []}, 'C': {'suits': [], 'cards': []}}
        
        # Associate suits with seats
        for suit in suits:
            closest_seat = self.find_closest_seat(suit, seats)
            if closest_seat:
                seat_name = self.get_seat_name(closest_seat['class'])
                if seat_name in seat_data:
                    seat_data[seat_name]['suits'].append({
                        'suit': suit['class'],
                        'confidence': suit['confidence']
                    })
        
        # Associate cards with seats
        for card in cards:
            closest_seat = self.find_closest_seat(card, seats)
            if closest_seat:
                seat_name = self.get_seat_name(closest_seat['class'])
                if seat_name in seat_data:
                    seat_data[seat_name]['cards'].append({
                        'card': card['class'],
                        'confidence': card['confidence']
                    })
        
        return seat_data
    
    def find_closest_seat(self, item, seats):
        """Find the closest seat to an item"""
        if not seats:
            return None
        
        min_distance = float('inf')
        closest_seat = None
        
        for seat in seats:
            distance = np.sqrt((item['center_x'] - seat['center_x'])**2 + 
                             (item['center_y'] - seat['center_y'])**2)
            if distance < min_distance:
                min_distance = distance
                closest_seat = seat
        
        return closest_seat
    
    def get_seat_name(self, class_name):
        """Convert class name to seat name"""
        class_name = class_name.lower()
        
        if class_name in ['a', 'b', 'c']:
            return class_name.upper()
        elif 'blue' in class_name:
            return 'A'
        elif 'golden' in class_name or 'yellow' in class_name:
            return 'B'
        elif 'purple' in class_name:
            return 'C'
        else:
            return 'Unknown'
    
    def save_to_database(self, image_path, analysis):
        """Save analysis results to database"""
        if not analysis:
            return None
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        timestamp = datetime.now().isoformat()
        
        # Extract data
        winning_seat = None
        if analysis['winning_seat']:
            winning_seat = self.get_seat_name(analysis['winning_seat']['class'])
        
        seat_data = analysis['seat_data']
        
        # Prepare data for database
        seat_a_suits = [s['suit'] for s in seat_data['A']['suits']]
        seat_b_suits = [s['suit'] for s in seat_data['B']['suits']]
        seat_c_suits = [s['suit'] for s in seat_data['C']['suits']]
        
        seat_a_cards = [c['card'] for c in seat_data['A']['cards']]
        seat_b_cards = [c['card'] for c in seat_data['B']['cards']]
        seat_c_cards = [c['card'] for c in seat_data['C']['cards']]
        
        # Insert into database
        cursor.execute('''
            INSERT INTO poker_games 
            (timestamp, image_path, winning_seat, seat_a_suits, seat_b_suits, seat_c_suits,
             seat_a_cards, seat_b_cards, seat_c_cards, all_detections)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            timestamp,
            image_path,
            winning_seat,
            json.dumps(seat_a_suits),
            json.dumps(seat_b_suits),
            json.dumps(seat_c_suits),
            json.dumps(seat_a_cards),
            json.dumps(seat_b_cards),
            json.dumps(seat_c_cards),
            json.dumps(analysis, default=str)
        ))
        
        game_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        # Print results
        print(f"\n💾 SAVED TO DATABASE (ID: {game_id})")
        print("=" * 40)
        print(f"🏆 Winning Seat: {winning_seat or 'Not detected'}")
        print(f"🎯 Seat A: Suits={seat_a_suits}, Cards={seat_a_cards}")
        print(f"🎯 Seat B: Suits={seat_b_suits}, Cards={seat_b_cards}")
        print(f"🎯 Seat C: Suits={seat_c_suits}, Cards={seat_c_cards}")
        
        return game_id
    
    def process_image(self, image_path):
        """Process a single image"""
        if not os.path.exists(image_path):
            print(f"❌ Image not found: {image_path}")
            return None
        
        # Run detection
        detections = self.run_yolo_detection(image_path)
        if not detections:
            print("❌ No detections found")
            return None
        
        # Analyze detections
        analysis = self.analyze_detections(detections)
        if not analysis:
            print("❌ Analysis failed")
            return None
        
        # Save to database
        game_id = self.save_to_database(image_path, analysis)
        return game_id
    
    def process_folder(self, folder_path="train"):
        """Process all images in a folder"""
        image_files = glob.glob(f"{folder_path}/*.jpg") + glob.glob(f"{folder_path}/*.png")
        
        if not image_files:
            print(f"❌ No images found in {folder_path}")
            return
        
        print(f"📸 Processing {len(image_files)} images from {folder_path}")
        
        successful = 0
        for i, image_path in enumerate(image_files):
            print(f"\n📷 Image {i+1}/{len(image_files)}: {os.path.basename(image_path)}")
            
            game_id = self.process_image(image_path)
            if game_id:
                successful += 1
        
        print(f"\n✅ Successfully processed {successful}/{len(image_files)} images")
    
    def view_database(self):
        """View database contents"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM poker_games")
        count = cursor.fetchone()[0]
        
        if count == 0:
            print("📊 Database is empty")
            conn.close()
            return
        
        print(f"\n📊 DATABASE CONTENTS ({count} games)")
        print("=" * 60)
        
        cursor.execute("""
            SELECT id, timestamp, winning_seat, seat_a_suits, seat_b_suits, seat_c_suits 
            FROM poker_games 
            ORDER BY timestamp DESC 
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        
        for row in results:
            game_id, timestamp, winning_seat, a_suits, b_suits, c_suits = row
            timestamp = timestamp[:19]  # Remove microseconds
            
            print(f"Game {game_id:3d} | {timestamp} | Winner: {winning_seat or 'None':7s} | A:{a_suits} B:{b_suits} C:{c_suits}")
        
        conn.close()


def main():
    """Main function"""
    print("🎯 Winning Seat Card Suit Capture System")
    print("=" * 45)
    print("📋 Uses your trained YOLO model to detect and store seat card suits")
    print()
    
    capture = WinningSeatCapture()
    
    while True:
        print("\n📋 Options:")
        print("1. 🔍 Process single image")
        print("2. 📁 Process all images in folder")
        print("3. 📊 View database")
        print("4. ❌ Exit")
        
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == '1':
            image_path = input("Enter image path: ").strip()
            capture.process_image(image_path)
        
        elif choice == '2':
            folder = input("Enter folder path (default: train): ").strip() or "train"
            capture.process_folder(folder)
        
        elif choice == '3':
            capture.view_database()
        
        elif choice == '4':
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    main()
