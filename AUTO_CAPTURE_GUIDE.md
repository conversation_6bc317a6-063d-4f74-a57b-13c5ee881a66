# Auto-Capture Screenshot Feature Guide

## Overview
The Auto-Capture feature provides automated screenshot capture at regular intervals, enabling continuous data collection without manual intervention. This feature works independently or in combination with the auto-detection system for maximum automation.

## Key Features

### 📷 **Automated Screenshot Capture**
- **Timed Intervals**: Captures screenshots every 2 seconds (configurable)
- **Smart Capture Mode**: Only processes when significant changes are detected
- **Background Processing**: Runs in separate thread without blocking GUI
- **Error Recovery**: Handles capture failures gracefully

### 🔄 **Intelligent Processing**
- **OCR Integration**: Automatically extracts data from captured screenshots
- **Change Detection**: Compares images to avoid processing duplicate states
- **Auto-Save Logic**: Saves high-confidence data automatically
- **Manual Fallback**: Prompts for verification when OCR confidence is low

### 🎯 **Flexible Operation Modes**
- **Standalone Mode**: Works without game detection for any screen area
- **Combined Mode**: Works with auto-detection for enhanced accuracy
- **Manual Trigger**: Immediate capture on demand
- **Smart Filtering**: Skips processing when no significant changes occur

## Setup and Usage

### Basic Auto-Capture Setup
1. **Configure Capture Area**: Ensure your game is visible in the default crop area
2. **Start Auto-Capture**: Click "📷 Start Auto-Capture"
3. **Monitor Status**: Check "Capture: ON" indicator
4. **Play Normally**: System captures screenshots automatically every 2 seconds

### Advanced Setup with Game Detection
1. **Calibrate Game**: Click "🎯 Calibrate Game" first
2. **Start Auto-Detection**: Click "▶️ Start Auto-Monitor"
3. **Start Auto-Capture**: Click "📷 Start Auto-Capture"
4. **Full Automation**: System detects game location AND captures automatically

### Manual Controls
- **📸 Capture Now**: Trigger immediate screenshot capture
- **⏹️ Stop Auto-Capture**: Stop automatic capture
- **📷 Start Auto-Capture**: Resume automatic capture

## Configuration Options

### Timing Settings
```python
AUTO_CAPTURE_INTERVAL = 2000    # Capture every 2 seconds
CAPTURE_DELAY_AFTER_CHANGE = 1000  # Wait 1 second after change detection
SMART_CAPTURE_MODE = True       # Only process when changes detected
```

### Processing Settings
```python
MIN_CHANGE_THRESHOLD = 0.1      # 10% change required to process
AUTO_CAPTURE_ENABLED = True     # Enable auto-capture by default
```

## Operation Modes

### 🟢 **Fully Automatic Mode**
**Setup**: Auto-Detection + Auto-Capture both enabled
**Behavior**: 
- Detects game window automatically
- Captures screenshots at regular intervals
- Processes only when game state changes
- Auto-saves high-confidence data

**Best For**: Hands-free operation during long gaming sessions

### 🟡 **Timed Capture Mode**
**Setup**: Auto-Capture enabled, Auto-Detection disabled
**Behavior**:
- Captures screenshots from fixed screen area
- Processes all captures regardless of changes
- Requires manual verification for most data

**Best For**: Consistent capture timing with manual oversight

### 🔴 **Manual Capture Mode**
**Setup**: Both auto-features disabled
**Behavior**:
- Manual screenshot capture only
- Full manual data entry required
- Complete user control over timing

**Best For**: Precise timing control or testing

## Status Indicators

### GUI Indicators
- **Auto: ON/OFF** - Auto-detection status
- **Capture: ON/OFF** - Auto-capture status
- **Status Bar** - Real-time operation messages

### Console Output
```
[14:23:15] Auto-captured: Seat A, Trend: High Card
[14:23:17] Auto-saved: Seat A, Cards: [(K, black), (7, red), (3, black)], Trend: High Card
[14:23:19] Auto-capture: [14:23:19] Screenshot captured
```

## Performance Optimization

### For High-Performance Systems
```python
AUTO_CAPTURE_INTERVAL = 1000    # Capture every 1 second
SMART_CAPTURE_MODE = True       # Enable smart filtering
```

### For Low-Performance Systems
```python
AUTO_CAPTURE_INTERVAL = 5000    # Capture every 5 seconds
SMART_CAPTURE_MODE = True       # Reduce processing load
```

### Memory Management
- Screenshots are automatically cleaned up
- Only last 5 screenshots stored in memory
- Processed images are released immediately

## Troubleshooting

### Common Issues

#### 📷 **Capture Fails**
**Symptoms**: "Too many capture errors" message
**Solutions**:
1. Check if game window is visible
2. Verify screen permissions (some systems require authorization)
3. Close other screen capture applications
4. Restart the application

#### 🔄 **Too Many Captures**
**Symptoms**: System processes same state repeatedly
**Solutions**:
1. Enable `SMART_CAPTURE_MODE = True`
2. Increase `MIN_CHANGE_THRESHOLD`
3. Ensure game display is stable
4. Check for screen animations or overlays

#### 💾 **High Memory Usage**
**Symptoms**: System memory increases over time
**Solutions**:
1. Increase `AUTO_CAPTURE_INTERVAL`
2. Reduce `MAX_SCREENSHOTS_STORED`
3. Enable smart capture mode
4. Restart application periodically

#### ⚡ **High CPU Usage**
**Symptoms**: System becomes slow during auto-capture
**Solutions**:
1. Increase capture interval to 3-5 seconds
2. Enable smart capture mode
3. Close unnecessary applications
4. Use lower resolution display settings

### Error Messages

#### "Auto-capture: Processing error"
- **Cause**: OCR or image processing failure
- **Solution**: Check image quality, ensure game is clearly visible

#### "Auto-capture: Auto-save failed"
- **Cause**: Database or validation error
- **Solution**: Check database permissions, verify data format

#### "Too many auto-capture errors, stopping"
- **Cause**: Consecutive capture failures
- **Solution**: Check system permissions, restart application

## Best Practices

### 🎮 **Game Setup**
1. **Stable Display**: Ensure game window doesn't move during capture
2. **Good Contrast**: High contrast between cards and background
3. **Minimal Overlays**: Remove unnecessary UI elements
4. **Consistent Timing**: Use games with predictable round timing

### 🖥️ **System Setup**
1. **Adequate Resources**: Ensure sufficient CPU and memory
2. **Screen Permissions**: Grant screen capture permissions
3. **Stable Environment**: Minimize other screen capture applications
4. **Regular Monitoring**: Check capture status periodically

### 📊 **Data Quality**
1. **Verification**: Periodically verify auto-saved data accuracy
2. **Manual Override**: Use manual capture for critical moments
3. **Backup Strategy**: Keep manual records for important games
4. **Quality Monitoring**: Track OCR success rates

## Integration with Other Features

### With Auto-Detection
- **Enhanced Accuracy**: Game detection improves capture targeting
- **Dynamic Positioning**: Follows game window movement
- **Intelligent Timing**: Captures only when game state changes

### With Pattern Analysis
- **Continuous Learning**: Regular captures improve pattern recognition
- **Real-Time Updates**: Immediate pattern analysis on new data
- **Trend Detection**: Identifies emerging patterns quickly

### With Prediction System
- **Automatic Training**: Triggers model retraining with sufficient data
- **Real-Time Predictions**: Updates predictions as new data arrives
- **Accuracy Tracking**: Monitors prediction performance continuously

## Advanced Features

### Scheduled Capture
```python
# Custom capture scheduling
def schedule_capture_session(start_time, duration, interval):
    # Implementation for scheduled capture sessions
    pass
```

### Batch Processing
- Process multiple screenshots simultaneously
- Bulk OCR processing for efficiency
- Batch database operations

### Quality Metrics
- Track OCR success rates
- Monitor capture timing accuracy
- Analyze processing performance

## Security and Privacy

### Data Protection
- All captures processed locally
- No network transmission of screenshots
- Automatic cleanup of temporary files
- Secure database storage

### System Impact
- Minimal system resource usage
- Non-intrusive capture methods
- Respects system privacy settings
- Compatible with security software

## Future Enhancements

### Planned Features
- **Adaptive Timing**: Automatically adjust capture intervals based on game pace
- **Quality Assessment**: Real-time image quality scoring
- **Multi-Game Profiles**: Different capture settings for different games
- **Cloud Backup**: Optional cloud storage for captures
- **Advanced Filtering**: Machine learning-based change detection

## Support and Troubleshooting

For technical support with auto-capture:
1. Check system requirements and permissions
2. Verify game display quality and stability
3. Monitor console output for error messages
4. Test with manual capture first
5. Report persistent issues with system specifications
