#!/usr/bin/env python3
"""
Complete Poker Game Processing and Database Viewer
This script will:
1. Complete processing all remaining images
2. Launch the database viewer GUI
"""

import os
import sys
import subprocess
import time
from advanced_ocr_capture import AdvancedOCRCapture
import tkinter as tk
from tkinter import messagebox
import threading

class ProcessingController:
    def __init__(self):
        self.processor = AdvancedOCRCapture()
        self.processing_complete = False
        
    def process_remaining_images(self, progress_callback=None):
        """Process all remaining images with progress updates"""
        try:
            folder = "images"
            if not os.path.exists(folder):
                print(f"❌ Folder not found: {folder}")
                return 0

            # Get all image files
            image_files = [f for f in os.listdir(folder) if f.lower().endswith(('.jpg', '.png', '.jpeg'))]
            
            if not image_files:
                print(f"❌ No images found in {folder}")
                return 0

            print(f"🚀 COMPLETING POKER GAME PROCESSING")
            print(f"📸 Processing {len(image_files)} images...")
            print("=" * 70)
            
            processed_count = 0
            
            for i, image_file in enumerate(image_files):
                image_path = os.path.join(folder, image_file)
                
                if progress_callback:
                    progress_callback(i + 1, len(image_files), image_file)
                
                print(f"📷 [{i+1}/{len(image_files)}] Processing: {image_file}")
                print("-" * 70)
                
                try:
                    game_id = self.processor.process_image_advanced(image_path)
                    if game_id:
                        processed_count += 1
                        print(f"✅ Success! Game ID: {game_id}")
                    else:
                        print(f"❌ Failed to process {image_file}")
                        
                except Exception as e:
                    print(f"❌ Error processing {image_file}: {e}")
                
                print()  # Empty line for readability
            
            print("=" * 70)
            print(f"🎉 PROCESSING COMPLETE!")
            print(f"✅ Successfully processed: {processed_count}/{len(image_files)} images")
            print(f"💾 Database: poker_games_advanced.db")
            print("=" * 70)
            
            self.processing_complete = True
            return processed_count
            
        except Exception as e:
            print(f"❌ Critical error during processing: {e}")
            return 0

def launch_database_viewer():
    """Launch the database viewer GUI"""
    try:
        print("🚀 Launching Database Viewer...")
        from database_viewer import main as viewer_main
        viewer_main()
    except Exception as e:
        print(f"❌ Error launching database viewer: {e}")
        messagebox.showerror("Error", f"Failed to launch database viewer: {e}")

class ProcessingGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🎮 Poker Game Processing")
        self.root.geometry("600x400")
        
        self.controller = ProcessingController()
        
        # Main frame
        main_frame = tk.Frame(root, padx=20, pady=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = tk.Label(main_frame, text="🎮 Poker Game Processing & Database Viewer", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Status
        self.status_var = tk.StringVar(value="Ready to process images...")
        status_label = tk.Label(main_frame, textvariable=self.status_var, 
                               font=("Arial", 12))
        status_label.pack(pady=(0, 10))
        
        # Progress bar
        self.progress_var = tk.StringVar(value="")
        progress_label = tk.Label(main_frame, textvariable=self.progress_var, 
                                 font=("Arial", 10))
        progress_label.pack(pady=(0, 20))
        
        # Buttons frame
        buttons_frame = tk.Frame(main_frame)
        buttons_frame.pack(pady=20)
        
        # Process button
        self.process_btn = tk.Button(buttons_frame, text="🚀 Process All Images", 
                                    command=self.start_processing,
                                    font=("Arial", 12), bg="#4CAF50", fg="white",
                                    padx=20, pady=10)
        self.process_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # View database button
        self.view_btn = tk.Button(buttons_frame, text="📊 View Database", 
                                 command=self.launch_viewer,
                                 font=("Arial", 12), bg="#2196F3", fg="white",
                                 padx=20, pady=10)
        self.view_btn.pack(side=tk.LEFT, padx=(10, 0))
        
        # Results text area
        results_frame = tk.Frame(main_frame)
        results_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, font=("Courier", 10))
        scrollbar = tk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Initial message
        self.results_text.insert(tk.END, "🎮 Poker Game Processing System\n")
        self.results_text.insert(tk.END, "=" * 50 + "\n\n")
        self.results_text.insert(tk.END, "Features:\n")
        self.results_text.insert(tk.END, "✅ Card area detection (Left=A, Center=B, Right=C)\n")
        self.results_text.insert(tk.END, "✅ Brightness-based winner detection\n")
        self.results_text.insert(tk.END, "✅ OCR text extraction\n")
        self.results_text.insert(tk.END, "✅ Comprehensive database storage\n\n")
        self.results_text.insert(tk.END, "Click 'Process All Images' to start processing...\n")
    
    def progress_callback(self, current, total, filename):
        """Update progress display"""
        percentage = (current / total) * 100
        self.progress_var.set(f"Processing {current}/{total} ({percentage:.1f}%) - {filename}")
        self.root.update()
    
    def start_processing(self):
        """Start processing in a separate thread"""
        self.process_btn.config(state=tk.DISABLED)
        self.status_var.set("Processing images... Please wait...")
        
        def process_thread():
            try:
                # Redirect stdout to capture print statements
                import io
                import contextlib
                
                output_buffer = io.StringIO()
                
                with contextlib.redirect_stdout(output_buffer):
                    processed_count = self.controller.process_remaining_images(self.progress_callback)
                
                # Get the output
                output = output_buffer.getvalue()
                
                # Update GUI in main thread
                self.root.after(0, self.processing_complete, processed_count, output)
                
            except Exception as e:
                self.root.after(0, self.processing_error, str(e))
        
        # Start processing thread
        thread = threading.Thread(target=process_thread)
        thread.daemon = True
        thread.start()
    
    def processing_complete(self, processed_count, output):
        """Called when processing is complete"""
        self.process_btn.config(state=tk.NORMAL)
        self.status_var.set(f"✅ Processing complete! {processed_count} images processed.")
        self.progress_var.set("")
        
        # Show results
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, output)
        self.results_text.insert(tk.END, f"\n🎉 Ready to view database results!\n")
        self.results_text.see(tk.END)
        
        # Enable view button
        self.view_btn.config(bg="#4CAF50")
    
    def processing_error(self, error_msg):
        """Called when processing encounters an error"""
        self.process_btn.config(state=tk.NORMAL)
        self.status_var.set("❌ Processing failed!")
        self.progress_var.set("")
        
        self.results_text.insert(tk.END, f"\n❌ Error: {error_msg}\n")
        self.results_text.see(tk.END)
    
    def launch_viewer(self):
        """Launch the database viewer"""
        try:
            # Launch in separate process to avoid GUI conflicts
            subprocess.Popen([sys.executable, "database_viewer.py"])
            self.results_text.insert(tk.END, "\n📊 Database viewer launched in separate window!\n")
            self.results_text.see(tk.END)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch database viewer: {e}")

def main():
    """Main function"""
    print("🎮 Poker Game Processing & Database Viewer")
    print("=" * 50)
    
    # Check if GUI mode or command line mode
    if len(sys.argv) > 1 and sys.argv[1] == "--cli":
        # Command line mode
        controller = ProcessingController()
        processed_count = controller.process_remaining_images()
        
        if processed_count > 0:
            print("\n🚀 Launching Database Viewer...")
            launch_database_viewer()
    else:
        # GUI mode
        root = tk.Tk()
        app = ProcessingGUI(root)
        root.mainloop()

if __name__ == "__main__":
    main()
