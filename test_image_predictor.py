"""
Test Image Predictor with Visual Output
======================================

This script tests card suit prediction on sample images from your train folder
and saves the prediction results as visual images so you can verify accuracy.

Author: Augment Agent
"""

import cv2
import numpy as np
import os
import glob
from datetime import datetime
from advanced_card_recognition import AdvancedCardRecognizer

class TestImagePredictor:
    """
    Test predictor that saves visual results
    """
    
    def __init__(self, save_path="test_predictions/"):
        self.save_path = save_path
        os.makedirs(save_path, exist_ok=True)
        
        # Initialize the recognizer
        self.recognizer = AdvancedCardRecognizer()
        self.recognizer.load_model()
        
        # Colors for visualization
        self.confidence_colors = {
            'high': (0, 255, 0),    # Green for >90%
            'medium': (0, 255, 255), # Yellow for 70-90%
            'low': (0, 0, 255)      # Red for <70%
        }
        
    def get_confidence_color(self, confidence):
        """Get color based on confidence level"""
        if confidence > 90:
            return self.confidence_colors['high']
        elif confidence > 70:
            return self.confidence_colors['medium']
        else:
            return self.confidence_colors['low']
    
    def create_prediction_visualization(self, image, predictions, image_name):
        """
        Create a visual representation of predictions
        """
        # Create a copy for annotation
        vis_image = image.copy()
        
        # Make image larger for better visibility
        height, width = vis_image.shape[:2]
        scale_factor = 2
        new_width = int(width * scale_factor)
        new_height = int(height * scale_factor)
        vis_image = cv2.resize(vis_image, (new_width, new_height))
        
        # Scale card positions accordingly
        scaled_positions = []
        for pos in self.recognizer.card_positions:
            scaled_pos = {
                'x': int(pos['x'] * scale_factor),
                'y': int(pos['y'] * scale_factor),
                'width': int(pos['width'] * scale_factor),
                'height': int(pos['height'] * scale_factor),
                'index': pos['index']
            }
            scaled_positions.append(scaled_pos)
        
        # Draw predictions
        for pred in predictions:
            pos = scaled_positions[pred['position']]
            x, y, w, h = pos['x'], pos['y'], pos['width'], pos['height']
            
            # Get color based on confidence
            color = self.get_confidence_color(pred['confidence'])
            
            # Draw bounding box
            cv2.rectangle(vis_image, (x, y), (x + w, y + h), color, 3)
            
            # Prepare text
            suit = pred['suit']
            confidence = pred['confidence']
            text = f"{suit} {confidence:.0f}%"
            
            # Text background
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.8
            thickness = 2
            text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
            
            # Background rectangle for text
            bg_x1, bg_y1 = x, y - 35
            bg_x2, bg_y2 = x + text_size[0] + 10, y
            cv2.rectangle(vis_image, (bg_x1, bg_y1), (bg_x2, bg_y2), color, -1)
            
            # Text
            cv2.putText(vis_image, text, (x + 5, y - 10), font, font_scale, (255, 255, 255), thickness)
            
            # Position number
            cv2.putText(vis_image, str(pred['position']), (x + 5, y + 25),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # Add title and statistics
        title = f"Card Suit Predictions - {image_name}"
        cv2.putText(vis_image, title, (20, 40), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)
        
        # Average confidence
        avg_conf = np.mean([p['confidence'] for p in predictions])
        conf_text = f"Average Confidence: {avg_conf:.1f}%"
        cv2.putText(vis_image, conf_text, (20, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
        
        # Legend
        legend_y = new_height - 100
        cv2.putText(vis_image, "Legend:", (20, legend_y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(vis_image, "Green: >90%", (20, legend_y + 25), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        cv2.putText(vis_image, "Yellow: 70-90%", (150, legend_y + 25), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 2)
        cv2.putText(vis_image, "Red: <70%", (300, legend_y + 25), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)
        
        return vis_image
    
    def create_grid_visualization(self, predictions, image_name):
        """
        Create a clean 3x3 grid visualization
        """
        # Create a white background
        grid_size = 600
        grid_image = np.ones((grid_size, grid_size, 3), dtype=np.uint8) * 255
        
        # Grid parameters
        cell_size = 180
        start_x = (grid_size - 3 * cell_size) // 2
        start_y = 100
        
        # Draw grid and predictions
        for row in range(3):
            for col in range(3):
                pos = row * 3 + col
                pred = next(p for p in predictions if p['position'] == pos)
                
                # Cell coordinates
                x = start_x + col * cell_size
                y = start_y + row * cell_size
                
                # Draw cell border
                color = self.get_confidence_color(pred['confidence'])
                cv2.rectangle(grid_image, (x, y), (x + cell_size, y + cell_size), color, 3)
                
                # Draw suit symbol (large)
                suit = pred['suit']
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 4.0
                thickness = 8
                
                # Get text size for centering
                text_size = cv2.getTextSize(suit, font, font_scale, thickness)[0]
                text_x = x + (cell_size - text_size[0]) // 2
                text_y = y + (cell_size + text_size[1]) // 2
                
                # Draw suit symbol
                cv2.putText(grid_image, suit, (text_x, text_y), font, font_scale, (0, 0, 0), thickness)
                
                # Draw confidence below
                conf_text = f"{pred['confidence']:.0f}%"
                conf_font_scale = 1.0
                conf_thickness = 2
                conf_size = cv2.getTextSize(conf_text, font, conf_font_scale, conf_thickness)[0]
                conf_x = x + (cell_size - conf_size[0]) // 2
                conf_y = y + cell_size - 20
                
                cv2.putText(grid_image, conf_text, (conf_x, conf_y), font, conf_font_scale, color, conf_thickness)
                
                # Position number (small, top-left)
                cv2.putText(grid_image, str(pos), (x + 10, y + 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (128, 128, 128), 2)
        
        # Title
        title = f"Card Grid - {image_name}"
        cv2.putText(grid_image, title, (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 0, 0), 3)
        
        # Average confidence
        avg_conf = np.mean([p['confidence'] for p in predictions])
        conf_text = f"Average Confidence: {avg_conf:.1f}%"
        cv2.putText(grid_image, conf_text, (50, grid_size - 30), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
        
        return grid_image
    
    def save_individual_cards(self, image, predictions, image_name):
        """
        Save individual card regions with predictions
        """
        cards_folder = os.path.join(self.save_path, f"cards_{image_name}")
        os.makedirs(cards_folder, exist_ok=True)
        
        for pred in predictions:
            pos = self.recognizer.card_positions[pred['position']]
            x, y, w, h = pos['x'], pos['y'], pos['width'], pos['height']
            
            # Extract card region
            card_region = image[y:y+h, x:x+w]
            
            if card_region.size > 0:
                # Enlarge for better viewing
                scale = 4
                enlarged = cv2.resize(card_region, (w*scale, h*scale), interpolation=cv2.INTER_CUBIC)
                
                # Add prediction text
                text = f"{pred['suit']} - {pred['confidence']:.1f}%"
                cv2.putText(enlarged, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (255, 255, 255), 2)
                cv2.putText(enlarged, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 1)
                
                # Save
                filename = f"card_{pred['position']:02d}_{pred['suit']}_{pred['confidence']:.0f}pct.jpg"
                filepath = os.path.join(cards_folder, filename)
                cv2.imwrite(filepath, enlarged)
    
    def test_on_sample_images(self, num_images=5):
        """
        Test on sample images from train folder
        """
        print("🧪 Testing on sample images...")
        
        # Load sample images
        image_files = glob.glob("train/*.jpg")
        
        if len(image_files) == 0:
            print("❌ No images found in train folder")
            return
        
        # Test on first few images
        test_images = image_files[:num_images]
        
        for i, img_path in enumerate(test_images):
            print(f"\n📸 Testing image {i+1}/{len(test_images)}: {os.path.basename(img_path)}")
            
            # Load image
            image = cv2.imread(img_path)
            if image is None:
                print(f"❌ Could not load {img_path}")
                continue
            
            # Make predictions
            predictions = self.recognizer.predict_suits(image)
            
            if predictions:
                image_name = os.path.splitext(os.path.basename(img_path))[0]
                
                # Create visualizations
                vis_image = self.create_prediction_visualization(image, predictions, image_name)
                grid_image = self.create_grid_visualization(predictions, image_name)
                
                # Save images
                vis_path = os.path.join(self.save_path, f"prediction_{image_name}.jpg")
                grid_path = os.path.join(self.save_path, f"grid_{image_name}.jpg")
                original_path = os.path.join(self.save_path, f"original_{image_name}.jpg")
                
                cv2.imwrite(vis_path, vis_image)
                cv2.imwrite(grid_path, grid_image)
                cv2.imwrite(original_path, image)
                
                # Save individual cards
                self.save_individual_cards(image, predictions, image_name)
                
                # Print results
                print("🎯 Predictions:")
                for row in range(3):
                    row_display = []
                    for col in range(3):
                        pos = row * 3 + col
                        pred = next(p for p in predictions if p['position'] == pos)
                        row_display.append(f"{pred['suit']}({pred['confidence']:.0f}%)")
                    print(f"   {' '.join(row_display)}")
                
                avg_conf = np.mean([p['confidence'] for p in predictions])
                print(f"   Average Confidence: {avg_conf:.1f}%")
                
                print(f"💾 Saved:")
                print(f"   - Prediction: prediction_{image_name}.jpg")
                print(f"   - Grid: grid_{image_name}.jpg")
                print(f"   - Cards: cards_{image_name}/")
            else:
                print("❌ No predictions generated")
        
        print(f"\n✅ Testing complete! Check folder: {self.save_path}")


def main():
    """Main function"""
    print("🧪 Test Image Predictor")
    print("=" * 30)
    
    predictor = TestImagePredictor()
    
    # Check if model is trained
    if not predictor.recognizer.is_trained:
        print("❌ No trained model found!")
        print("💡 Please run advanced_card_recognition.py first and train the model (option 1)")
        return
    
    print("✅ Model loaded successfully")
    
    while True:
        print("\n📋 Choose option:")
        print("1. 🧪 Test on 5 sample images")
        print("2. 🧪 Test on 10 sample images")
        print("3. 🧪 Test on all images")
        print("4. 📁 Open results folder")
        print("5. ❌ Exit")
        
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == '1':
            predictor.test_on_sample_images(num_images=5)
        
        elif choice == '2':
            predictor.test_on_sample_images(num_images=10)
        
        elif choice == '3':
            predictor.test_on_sample_images(num_images=999)
        
        elif choice == '4':
            import subprocess
            import platform
            
            try:
                if platform.system() == 'Windows':
                    subprocess.run(['explorer', predictor.save_path])
                elif platform.system() == 'Darwin':
                    subprocess.run(['open', predictor.save_path])
                else:
                    subprocess.run(['xdg-open', predictor.save_path])
                print(f"📁 Opened: {predictor.save_path}")
            except:
                print(f"📁 Please open: {predictor.save_path}")
        
        elif choice == '5':
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    main()
