"""
Reinforcement Learning with Unsupervised Visual Learning
========================================================

This module implements a sophisticated AI system that:
1. Uses unsupervised learning to understand visual elements
2. Applies reinforcement learning for decision making
3. Learns visual patterns without manual labeling
4. Adapts to new game environments automatically

Author: Augment Agent
"""

import cv2
import numpy as np
import sqlite3
import os
import time
import pickle
from datetime import datetime
from PIL import ImageGrab
from sklearn.cluster import KMeans, DBSCAN
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from collections import deque, namedtuple
import random
import matplotlib.pyplot as plt
from skimage.segmentation import slic, felzenszwalb
from skimage.feature import local_binary_pattern, hog
from skimage.measure import regionprops
import warnings
warnings.filterwarnings('ignore')

# Experience tuple for RL
Experience = namedtuple('Experience', ['state', 'action', 'reward', 'next_state', 'done'])

class UnsupervisedVisualLearner:
    """
    Learns visual patterns and shapes without supervision
    """
    
    def __init__(self, save_path="visual_learning/"):
        self.save_path = save_path
        os.makedirs(save_path, exist_ok=True)
        
        # Visual vocabulary storage
        self.visual_vocabulary = {}
        self.shape_clusters = {}
        self.color_clusters = {}
        self.texture_clusters = {}
        
        # Feature extractors
        self.scaler = StandardScaler()
        self.pca = PCA(n_components=50)
        self.shape_kmeans = KMeans(n_clusters=20, random_state=42)
        self.color_kmeans = KMeans(n_clusters=15, random_state=42)
        self.texture_kmeans = KMeans(n_clusters=25, random_state=42)
        
        # Learning parameters
        self.min_samples_for_learning = 100
        self.learning_history = []
        
    def extract_visual_features(self, image):
        """
        Extract comprehensive visual features from image
        """
        features = {}
        
        # 1. Shape Features using contours
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        shape_features = []
        for contour in contours:
            if cv2.contourArea(contour) > 100:  # Filter small contours
                # Geometric features
                area = cv2.contourArea(contour)
                perimeter = cv2.arcLength(contour, True)
                if perimeter > 0:
                    circularity = 4 * np.pi * area / (perimeter * perimeter)
                else:
                    circularity = 0
                
                # Bounding box features
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = w / h if h > 0 else 0
                extent = area / (w * h) if w * h > 0 else 0
                
                shape_features.extend([area, perimeter, circularity, aspect_ratio, extent])
        
        features['shapes'] = np.array(shape_features[:50])  # Limit to 50 features
        
        # 2. Color Features using histograms
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        hist_h = cv2.calcHist([hsv], [0], None, [50], [0, 180])
        hist_s = cv2.calcHist([hsv], [1], None, [50], [0, 256])
        hist_v = cv2.calcHist([hsv], [2], None, [50], [0, 256])
        
        color_features = np.concatenate([hist_h.flatten(), hist_s.flatten(), hist_v.flatten()])
        features['colors'] = color_features
        
        # 3. Texture Features using LBP
        lbp = local_binary_pattern(gray, 24, 8, method='uniform')
        lbp_hist, _ = np.histogram(lbp.ravel(), bins=26, range=(0, 26))
        features['textures'] = lbp_hist
        
        # 4. HOG Features for object detection
        hog_features = hog(gray, orientations=9, pixels_per_cell=(8, 8),
                          cells_per_block=(2, 2), feature_vector=True)
        features['hog'] = hog_features[:200]  # Limit HOG features
        
        return features
    
    def segment_image(self, image):
        """
        Segment image into meaningful regions
        """
        # SLIC superpixel segmentation
        segments_slic = slic(image, n_segments=50, compactness=10, sigma=1)
        
        # Felzenszwalb segmentation
        segments_fz = felzenszwalb(image, scale=100, sigma=0.5, min_size=50)
        
        # Extract regions
        regions = []
        for segment_id in np.unique(segments_slic):
            mask = segments_slic == segment_id
            if np.sum(mask) > 100:  # Filter small regions
                region_props = regionprops(mask.astype(int))
                if region_props:
                    regions.append({
                        'mask': mask,
                        'bbox': region_props[0].bbox,
                        'area': region_props[0].area,
                        'centroid': region_props[0].centroid
                    })
        
        return regions, segments_slic
    
    def learn_visual_patterns(self, images):
        """
        Learn visual patterns from a collection of images
        """
        print("🧠 Learning visual patterns...")
        
        all_shape_features = []
        all_color_features = []
        all_texture_features = []
        
        for i, image in enumerate(images):
            if i % 10 == 0:
                print(f"Processing image {i+1}/{len(images)}")
            
            features = self.extract_visual_features(image)
            
            # Pad features to consistent length
            shape_feat = features['shapes']
            if len(shape_feat) < 50:
                shape_feat = np.pad(shape_feat, (0, 50 - len(shape_feat)), 'constant')
            
            all_shape_features.append(shape_feat)
            all_color_features.append(features['colors'])
            all_texture_features.append(features['textures'])
        
        # Convert to arrays with consistent data types
        all_shape_features = np.array(all_shape_features, dtype=np.float64)
        all_color_features = np.array(all_color_features, dtype=np.float64)
        all_texture_features = np.array(all_texture_features, dtype=np.float64)
        
        # Fit clustering models
        print("🔍 Clustering visual features...")
        
        # Shape clustering
        if len(all_shape_features) > 0:
            shape_scaled = self.scaler.fit_transform(all_shape_features)
            self.shape_clusters = self.shape_kmeans.fit(shape_scaled)
        
        # Color clustering
        if len(all_color_features) > 0:
            # Ensure data type consistency
            all_color_features = np.array(all_color_features, dtype=np.float64)
            self.color_clusters = self.color_kmeans.fit(all_color_features)

        # Texture clustering
        if len(all_texture_features) > 0:
            # Ensure data type consistency
            all_texture_features = np.array(all_texture_features, dtype=np.float64)
            self.texture_clusters = self.texture_kmeans.fit(all_texture_features)
        
        # Build visual vocabulary
        self.visual_vocabulary = {
            'shape_centers': self.shape_kmeans.cluster_centers_,
            'color_centers': self.color_kmeans.cluster_centers_,
            'texture_centers': self.texture_kmeans.cluster_centers_,
            'n_samples': len(images)
        }
        
        print(f"✅ Learned visual vocabulary from {len(images)} images")
        self.save_visual_learning()
        
    def encode_image(self, image):
        """
        Encode image using learned visual vocabulary
        """
        features = self.extract_visual_features(image)
        
        # Encode shapes
        shape_feat = features['shapes']
        if len(shape_feat) < 50:
            shape_feat = np.pad(shape_feat, (0, 50 - len(shape_feat)), 'constant')
        
        shape_scaled = self.scaler.transform([shape_feat])
        shape_code = self.shape_kmeans.predict(shape_scaled)[0]
        
        # Encode colors
        color_features = np.array([features['colors']], dtype=np.float64)
        color_code = self.color_kmeans.predict(color_features)[0]

        # Encode textures
        texture_features = np.array([features['textures']], dtype=np.float64)
        texture_code = self.texture_kmeans.predict(texture_features)[0]
        
        # Create compact visual representation
        visual_encoding = np.array([shape_code, color_code, texture_code])
        
        return visual_encoding, features
    
    def save_visual_learning(self):
        """Save learned visual patterns"""
        save_data = {
            'visual_vocabulary': self.visual_vocabulary,
            'scaler': self.scaler,
            'shape_kmeans': self.shape_kmeans,
            'color_kmeans': self.color_kmeans,
            'texture_kmeans': self.texture_kmeans,
            'learning_history': self.learning_history
        }
        
        with open(os.path.join(self.save_path, 'visual_learning.pkl'), 'wb') as f:
            pickle.dump(save_data, f)
        
        print("💾 Visual learning saved")
    
    def load_visual_learning(self):
        """Load previously learned visual patterns"""
        try:
            with open(os.path.join(self.save_path, 'visual_learning.pkl'), 'rb') as f:
                save_data = pickle.load(f)
            
            self.visual_vocabulary = save_data['visual_vocabulary']
            self.scaler = save_data['scaler']
            self.shape_kmeans = save_data['shape_kmeans']
            self.color_kmeans = save_data['color_kmeans']
            self.texture_kmeans = save_data['texture_kmeans']
            self.learning_history = save_data.get('learning_history', [])
            
            print("📚 Visual learning loaded")
            return True
        except FileNotFoundError:
            print("🆕 No previous visual learning found")
            return False


class DQNNetwork(nn.Module):
    """
    Deep Q-Network for reinforcement learning
    """

    def __init__(self, state_size, action_size, hidden_size=256):
        super(DQNNetwork, self).__init__()
        self.fc1 = nn.Linear(state_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, hidden_size // 2)
        self.fc4 = nn.Linear(hidden_size // 2, action_size)
        self.dropout = nn.Dropout(0.2)

    def forward(self, x):
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.dropout(x)
        x = F.relu(self.fc3(x))
        x = self.fc4(x)
        return x


class ReinforcementLearningAgent:
    """
    RL Agent that learns to make predictions using visual features
    """

    def __init__(self, state_size=100, action_size=3, lr=0.001, save_path="rl_models/"):
        self.state_size = state_size
        self.action_size = action_size  # 3 actions: predict A, B, or C
        self.save_path = save_path
        os.makedirs(save_path, exist_ok=True)

        # RL parameters
        self.epsilon = 1.0  # Exploration rate
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.learning_rate = lr
        self.gamma = 0.95  # Discount factor
        self.batch_size = 32
        self.memory_size = 10000

        # Experience replay memory
        self.memory = deque(maxlen=self.memory_size)

        # Neural networks
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.q_network = DQNNetwork(state_size, action_size).to(self.device)
        self.target_network = DQNNetwork(state_size, action_size).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=lr)

        # Performance tracking
        self.training_history = []
        self.reward_history = []
        self.accuracy_history = []

        # Update target network
        self.update_target_network()

    def update_target_network(self):
        """Copy weights from main network to target network"""
        self.target_network.load_state_dict(self.q_network.state_dict())

    def remember(self, state, action, reward, next_state, done):
        """Store experience in replay memory"""
        self.memory.append(Experience(state, action, reward, next_state, done))

    def act(self, state, training=True):
        """Choose action using epsilon-greedy policy"""
        if training and np.random.random() <= self.epsilon:
            return random.randrange(self.action_size)

        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        q_values = self.q_network(state_tensor)
        return np.argmax(q_values.cpu().data.numpy())

    def replay(self):
        """Train the network on a batch of experiences"""
        if len(self.memory) < self.batch_size:
            return

        batch = random.sample(self.memory, self.batch_size)
        states = torch.FloatTensor([e.state for e in batch]).to(self.device)
        actions = torch.LongTensor([e.action for e in batch]).to(self.device)
        rewards = torch.FloatTensor([e.reward for e in batch]).to(self.device)
        next_states = torch.FloatTensor([e.next_state for e in batch]).to(self.device)
        dones = torch.BoolTensor([e.done for e in batch]).to(self.device)

        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        next_q_values = self.target_network(next_states).max(1)[0].detach()
        target_q_values = rewards + (self.gamma * next_q_values * ~dones)

        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)

        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()

        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay

        return loss.item()

    def save_model(self):
        """Save the trained model"""
        torch.save({
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'training_history': self.training_history,
            'reward_history': self.reward_history,
            'accuracy_history': self.accuracy_history
        }, os.path.join(self.save_path, 'rl_model.pth'))

        print("🤖 RL model saved")

    def load_model(self):
        """Load a previously trained model"""
        try:
            checkpoint = torch.load(os.path.join(self.save_path, 'rl_model.pth'))
            self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
            self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.epsilon = checkpoint['epsilon']
            self.training_history = checkpoint.get('training_history', [])
            self.reward_history = checkpoint.get('reward_history', [])
            self.accuracy_history = checkpoint.get('accuracy_history', [])

            print("🤖 RL model loaded")
            return True
        except FileNotFoundError:
            print("🆕 No previous RL model found")
            return False


class VisualReinforcementLearningSystem:
    """
    Main system that combines unsupervised visual learning with reinforcement learning
    """

    def __init__(self, crop_box=(970, 388, 1350, 632), db_path="game_data_final.db"):
        self.crop_box = crop_box
        self.db_path = db_path

        # Initialize components
        self.visual_learner = UnsupervisedVisualLearner()
        self.rl_agent = ReinforcementLearningAgent()

        # System state
        self.is_learning_phase = True
        self.is_playing_phase = False
        self.learning_samples = []
        self.performance_metrics = {
            'accuracy': 0,
            'total_predictions': 0,
            'correct_predictions': 0,
            'learning_progress': 0
        }

        # Load previous learning if available
        self.visual_learner.load_visual_learning()
        self.rl_agent.load_model()

    def capture_game_image(self):
        """Capture current game state"""
        img = ImageGrab.grab(bbox=self.crop_box)
        img_np = np.array(img)
        img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
        return img_bgr

    def get_winning_seat_from_db(self):
        """Get the actual winning seat from database for reward calculation"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT winning_seat FROM backend_data ORDER BY id DESC LIMIT 1")
            result = cursor.fetchone()
            conn.close()

            if result:
                seat_map = {'A': 0, 'B': 1, 'C': 2}
                return seat_map.get(result[0], -1)
            return -1
        except:
            return -1

    def calculate_reward(self, predicted_action, actual_action):
        """Calculate reward based on prediction accuracy"""
        if predicted_action == actual_action:
            return 1.0  # Correct prediction
        else:
            return -0.1  # Incorrect prediction (small penalty)

    def create_state_representation(self, image):
        """Create state representation from image using visual learning"""
        # Extract visual features
        visual_encoding, raw_features = self.visual_learner.encode_image(image)

        # Combine different feature types into state vector
        state_vector = []

        # Add visual encoding (3 values)
        state_vector.extend(visual_encoding)

        # Add normalized shape features (first 20)
        shape_features = raw_features['shapes'][:20]
        if len(shape_features) < 20:
            shape_features = np.pad(shape_features, (0, 20 - len(shape_features)), 'constant')
        state_vector.extend(shape_features)

        # Add color histogram features (first 30)
        color_features = raw_features['colors'][:30]
        state_vector.extend(color_features)

        # Add texture features (first 26)
        texture_features = raw_features['textures'][:26]
        state_vector.extend(texture_features)

        # Add HOG features (first 21)
        hog_features = raw_features['hog'][:21]
        state_vector.extend(hog_features)

        # Ensure consistent state size (100 features)
        state_vector = np.array(state_vector[:100])
        if len(state_vector) < 100:
            state_vector = np.pad(state_vector, (0, 100 - len(state_vector)), 'constant')

        # Normalize state vector
        state_vector = (state_vector - np.mean(state_vector)) / (np.std(state_vector) + 1e-8)

        return state_vector

    def learning_phase(self, num_samples=200):
        """
        Phase 1: Learn visual patterns without supervision
        """
        print("🎓 Starting Learning Phase...")
        print(f"📚 Collecting {num_samples} samples for visual learning")

        images = []
        for i in range(num_samples):
            if i % 20 == 0:
                print(f"📸 Collecting sample {i+1}/{num_samples}")

            # Capture image
            image = self.capture_game_image()
            images.append(image)

            # Small delay between captures
            time.sleep(0.5)

        # Learn visual patterns
        self.visual_learner.learn_visual_patterns(images)

        # Store samples for RL training
        self.learning_samples = images

        print("✅ Learning phase completed!")
        self.is_learning_phase = False

    def training_phase(self, num_episodes=1000):
        """
        Phase 2: Train RL agent using learned visual features (FAST VERSION)
        """
        print("🤖 Starting RL Training Phase...")

        if not self.learning_samples:
            print("❌ No learning samples available. Run learning phase first.")
            return

        # Pre-compute all state representations to speed up training
        print("🔄 Pre-computing state representations...")
        states = []
        sample_size = min(50, len(self.learning_samples))  # Use only 50 images for training

        for i in range(sample_size):
            if i % 10 == 0:
                print(f"   Processing {i+1}/{sample_size}")
            state = self.create_state_representation(self.learning_samples[i])
            states.append(state)

        print(f"✅ Pre-computed {len(states)} states")

        episode_rewards = []

        for episode in range(num_episodes):
            if episode % 100 == 0:
                print(f"🎯 Training episode {episode+1}/{num_episodes}")

            # Simulate training using pre-computed states
            total_reward = 0

            # Use smaller batches for faster training
            batch_size = min(10, len(states) - 1)

            for i in range(batch_size):
                # Current state
                current_state = states[i]

                # Next state
                next_state = states[(i + 1) % len(states)]

                # Choose action
                action = self.rl_agent.act(current_state, training=True)

                # Simulate reward (in real scenario, this would come from actual game results)
                actual_action = random.randint(0, 2)  # Placeholder
                reward = self.calculate_reward(action, actual_action)
                total_reward += reward

                # Store experience
                done = (i == batch_size - 1)
                self.rl_agent.remember(current_state, action, reward, next_state, done)

                # Train the network more frequently
                if len(self.rl_agent.memory) > self.rl_agent.batch_size:
                    loss = self.rl_agent.replay()

            episode_rewards.append(total_reward)

            # Update target network more frequently
            if episode % 50 == 0:
                self.rl_agent.update_target_network()

        # Save trained model
        self.rl_agent.save_model()

        print("✅ RL training completed!")
        self.is_playing_phase = True

    def playing_phase(self):
        """
        Phase 3: Use trained RL agent to make predictions
        """
        print("🎮 Starting Playing Phase...")
        print("🤖 AI is now making autonomous predictions!")

        while self.is_playing_phase:
            try:
                # Capture current game state
                current_image = self.capture_game_image()

                # Create state representation
                state = self.create_state_representation(current_image)

                # Make prediction using RL agent
                action = self.rl_agent.act(state, training=False)

                # Convert action to seat prediction
                seat_map = {0: 'A', 1: 'B', 2: 'C'}
                predicted_seat = seat_map[action]

                print(f"🎯 RL Prediction: {predicted_seat}")

                # Wait for actual result and calculate reward for continuous learning
                time.sleep(60)  # Wait for game round to complete

                actual_action = self.get_winning_seat_from_db()
                if actual_action != -1:
                    reward = self.calculate_reward(action, actual_action)

                    # Update performance metrics
                    self.performance_metrics['total_predictions'] += 1
                    if reward > 0:
                        self.performance_metrics['correct_predictions'] += 1

                    accuracy = (self.performance_metrics['correct_predictions'] /
                              self.performance_metrics['total_predictions']) * 100
                    self.performance_metrics['accuracy'] = accuracy

                    print(f"📊 Accuracy: {accuracy:.1f}% ({self.performance_metrics['correct_predictions']}/{self.performance_metrics['total_predictions']})")

                    # Continue learning (online learning)
                    next_image = self.capture_game_image()
                    next_state = self.create_state_representation(next_image)
                    self.rl_agent.remember(state, action, reward, next_state, True)

                    # Periodic retraining
                    if self.performance_metrics['total_predictions'] % 10 == 0:
                        self.rl_agent.replay()

            except KeyboardInterrupt:
                print("🛑 Playing phase stopped by user")
                break
            except Exception as e:
                print(f"❌ Error in playing phase: {e}")
                time.sleep(5)

    def run_complete_system(self):
        """
        Run the complete system: Learning -> Training -> Playing
        """
        print("🚀 Starting Complete Visual RL System!")
        print("=" * 50)

        # Phase 1: Visual Learning
        if self.is_learning_phase:
            self.learning_phase(num_samples=200)

        # Phase 2: RL Training
        self.training_phase(num_episodes=500)

        # Phase 3: Playing
        self.playing_phase()

    def get_performance_report(self):
        """Get detailed performance report"""
        report = f"""
🤖 Visual Reinforcement Learning System Report
============================================

📊 Performance Metrics:
- Accuracy: {self.performance_metrics['accuracy']:.2f}%
- Total Predictions: {self.performance_metrics['total_predictions']}
- Correct Predictions: {self.performance_metrics['correct_predictions']}

🧠 Learning Status:
- Visual Learning: {'✅ Completed' if not self.is_learning_phase else '🔄 In Progress'}
- RL Training: {'✅ Completed' if self.is_playing_phase else '🔄 In Progress'}
- Playing Phase: {'🎮 Active' if self.is_playing_phase else '⏸️ Inactive'}

🎯 RL Agent Status:
- Exploration Rate (Epsilon): {self.rl_agent.epsilon:.3f}
- Memory Size: {len(self.rl_agent.memory)}
- Training Episodes: {len(self.rl_agent.training_history)}

📚 Visual Learning Status:
- Visual Vocabulary Size: {len(self.visual_learner.visual_vocabulary) if self.visual_learner.visual_vocabulary else 0}
- Learning Samples: {len(self.learning_samples)}
        """
        return report


# Example usage and testing functions
def test_visual_learning():
    """Test the visual learning component"""
    print("🧪 Testing Visual Learning Component...")

    learner = UnsupervisedVisualLearner()

    # Create some test images
    test_images = []
    for i in range(10):
        # Create synthetic test image
        img = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        test_images.append(img)

    # Test feature extraction
    features = learner.extract_visual_features(test_images[0])
    print(f"✅ Extracted features: {list(features.keys())}")

    # Test learning
    learner.learn_visual_patterns(test_images)

    # Test encoding
    encoding, raw_features = learner.encode_image(test_images[0])
    print(f"✅ Visual encoding: {encoding}")

    print("✅ Visual learning test completed!")


def test_rl_agent():
    """Test the RL agent component"""
    print("🧪 Testing RL Agent Component...")

    agent = ReinforcementLearningAgent(state_size=100, action_size=3)

    # Test action selection
    test_state = np.random.random(100)
    action = agent.act(test_state)
    print(f"✅ Selected action: {action}")

    # Test memory
    next_state = np.random.random(100)
    agent.remember(test_state, action, 1.0, next_state, False)
    print(f"✅ Memory size: {len(agent.memory)}")

    print("✅ RL agent test completed!")


if __name__ == "__main__":
    print("🎮 Visual Reinforcement Learning System")
    print("=" * 40)

    # Run tests
    test_visual_learning()
    test_rl_agent()

    # Initialize and run the complete system
    print("\n🚀 Initializing Complete System...")
    system = VisualReinforcementLearningSystem()

    # Print initial report
    print(system.get_performance_report())

    # Uncomment to run the complete system
    # system.run_complete_system()
