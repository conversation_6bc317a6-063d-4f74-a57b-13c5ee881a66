"""
Winning Seat Card Suit Detector & Database Storage
=================================================

Detects winning seat (A, B, C) and their card suits, then stores in database.

Author: Augment Agent
"""

import cv2
import numpy as np
import sqlite3
import json
from datetime import datetime
import os
import glob
from ultralytics import YOLO
import pandas as pd

class WinningSeatDetector:
    """
    Detects winning seats and their card suits, stores in database
    """
    
    def __init__(self, model_path="best.pt"):
        self.model_path = model_path
        self.model = None
        self.db_path = "poker_game_results.db"
        self.init_database()
        self.load_model()
        
        # Seat mappings
        self.seat_classes = ['A', 'B', 'C']
        self.suit_classes = ['spades', 'hearts', 'diamonds', 'clubs']
        
    def load_model(self):
        """Load the trained YOLO model"""
        try:
            if os.path.exists(self.model_path):
                self.model = YOLO(self.model_path)
                print(f"✅ Model loaded: {self.model_path}")
                return True
            else:
                print(f"❌ Model not found: {self.model_path}")
                return False
        except Exception as e:
            print(f"❌ Error loading model: {e}")
            return False
    
    def init_database(self):
        """Initialize SQLite database for storing results"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create table for game results
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS game_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                image_path TEXT NOT NULL,
                winning_seat TEXT,
                seat_a_suits TEXT,
                seat_b_suits TEXT,
                seat_c_suits TEXT,
                seat_a_cards TEXT,
                seat_b_cards TEXT,
                seat_c_cards TEXT,
                confidence_scores TEXT,
                detection_details TEXT
            )
        ''')
        
        # Create table for individual detections
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS detections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id INTEGER,
                detection_type TEXT,
                class_name TEXT,
                confidence REAL,
                bbox_x REAL,
                bbox_y REAL,
                bbox_width REAL,
                bbox_height REAL,
                FOREIGN KEY (game_id) REFERENCES game_results (id)
            )
        ''')
        
        conn.commit()
        conn.close()
        print(f"✅ Database initialized: {self.db_path}")
    
    def detect_seats_and_suits(self, image_path):
        """
        Detect seats (A, B, C) and their associated card suits
        """
        if not self.model:
            print("❌ Model not loaded")
            return None
        
        print(f"🔍 Analyzing: {os.path.basename(image_path)}")
        
        # Run detection
        results = self.model(image_path, conf=0.3, verbose=False)
        
        if not results or len(results) == 0:
            print("❌ No detections found")
            return None
        
        # Parse detections
        detections = []
        for result in results:
            if result.boxes is not None:
                for box in result.boxes:
                    class_id = int(box.cls[0])
                    confidence = float(box.conf[0])
                    bbox = box.xyxy[0].cpu().numpy()
                    
                    class_name = self.model.names[class_id]
                    
                    detections.append({
                        'class_name': class_name,
                        'confidence': confidence,
                        'bbox': bbox,
                        'center_x': (bbox[0] + bbox[2]) / 2,
                        'center_y': (bbox[1] + bbox[3]) / 2
                    })
        
        print(f"📊 Found {len(detections)} detections")
        
        # Analyze detections
        analysis = self.analyze_seat_suits(detections)
        
        return {
            'detections': detections,
            'analysis': analysis,
            'image_path': image_path
        }
    
    def analyze_seat_suits(self, detections):
        """
        Analyze which suits belong to which seats
        """
        # Separate seat detections and suit detections
        seats = []
        suits = []
        cards = []
        winning_seat = None
        
        for det in detections:
            class_name = det['class_name']
            
            # Check for seats
            if class_name in ['A', 'B', 'C']:
                seats.append(det)
            elif 'seat' in class_name.lower():
                if 'winning' in class_name.lower():
                    winning_seat = det
                seats.append(det)
            
            # Check for suits
            elif class_name in self.suit_classes:
                suits.append(det)
            
            # Check for cards (numbers, face cards)
            elif class_name in ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']:
                cards.append(det)
        
        print(f"🎯 Found: {len(seats)} seats, {len(suits)} suits, {len(cards)} cards")
        
        # Associate suits with seats based on proximity
        seat_suit_mapping = self.associate_suits_with_seats(seats, suits, cards)
        
        return {
            'seats': seats,
            'suits': suits,
            'cards': cards,
            'winning_seat': winning_seat,
            'seat_suit_mapping': seat_suit_mapping
        }
    
    def associate_suits_with_seats(self, seats, suits, cards):
        """
        Associate detected suits with their corresponding seats based on proximity
        """
        seat_mapping = {'A': [], 'B': [], 'C': []}
        
        # For each suit, find the closest seat
        for suit in suits:
            suit_x, suit_y = suit['center_x'], suit['center_y']
            
            closest_seat = None
            min_distance = float('inf')
            
            for seat in seats:
                seat_x, seat_y = seat['center_x'], seat['center_y']
                distance = np.sqrt((suit_x - seat_x)**2 + (suit_y - seat_y)**2)
                
                if distance < min_distance:
                    min_distance = distance
                    closest_seat = seat
            
            if closest_seat:
                seat_name = self.get_seat_name(closest_seat['class_name'])
                if seat_name in seat_mapping:
                    seat_mapping[seat_name].append({
                        'suit': suit['class_name'],
                        'confidence': suit['confidence'],
                        'distance': min_distance
                    })
        
        # Also associate cards with seats
        for card in cards:
            card_x, card_y = card['center_x'], card['center_y']
            
            closest_seat = None
            min_distance = float('inf')
            
            for seat in seats:
                seat_x, seat_y = seat['center_x'], seat['center_y']
                distance = np.sqrt((card_x - seat_x)**2 + (card_y - seat_y)**2)
                
                if distance < min_distance:
                    min_distance = distance
                    closest_seat = seat
            
            if closest_seat:
                seat_name = self.get_seat_name(closest_seat['class_name'])
                if seat_name in seat_mapping:
                    if 'cards' not in seat_mapping[seat_name]:
                        seat_mapping[seat_name] = {'suits': seat_mapping[seat_name], 'cards': []}
                    else:
                        seat_mapping[seat_name]['cards'].append({
                            'card': card['class_name'],
                            'confidence': card['confidence'],
                            'distance': min_distance
                        })
        
        return seat_mapping
    
    def get_seat_name(self, class_name):
        """Extract seat name from class name"""
        if class_name in ['A', 'B', 'C']:
            return class_name
        elif 'blue' in class_name.lower():
            return 'A'  # Assuming blue = A
        elif 'golden' in class_name.lower():
            return 'B'  # Assuming golden = B
        elif 'purple' in class_name.lower():
            return 'C'  # Assuming purple = C
        else:
            return 'Unknown'
    
    def save_to_database(self, detection_result):
        """Save detection results to database"""
        if not detection_result:
            return None
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        timestamp = datetime.now().isoformat()
        analysis = detection_result['analysis']
        
        # Determine winning seat
        winning_seat = None
        if analysis['winning_seat']:
            winning_seat = self.get_seat_name(analysis['winning_seat']['class_name'])
        
        # Extract suits for each seat
        seat_mapping = analysis['seat_suit_mapping']
        
        seat_a_suits = [s['suit'] for s in seat_mapping.get('A', [])]
        seat_b_suits = [s['suit'] for s in seat_mapping.get('B', [])]
        seat_c_suits = [s['suit'] for s in seat_mapping.get('C', [])]
        
        # Extract cards for each seat
        seat_a_cards = []
        seat_b_cards = []
        seat_c_cards = []
        
        if isinstance(seat_mapping.get('A'), dict) and 'cards' in seat_mapping['A']:
            seat_a_cards = [c['card'] for c in seat_mapping['A']['cards']]
        if isinstance(seat_mapping.get('B'), dict) and 'cards' in seat_mapping['B']:
            seat_b_cards = [c['card'] for c in seat_mapping['B']['cards']]
        if isinstance(seat_mapping.get('C'), dict) and 'cards' in seat_mapping['C']:
            seat_c_cards = [c['card'] for c in seat_mapping['C']['cards']]
        
        # Prepare confidence scores
        confidence_scores = {
            'A': [s['confidence'] for s in seat_mapping.get('A', [])],
            'B': [s['confidence'] for s in seat_mapping.get('B', [])],
            'C': [s['confidence'] for s in seat_mapping.get('C', [])]
        }
        
        # Insert main record
        cursor.execute('''
            INSERT INTO game_results 
            (timestamp, image_path, winning_seat, seat_a_suits, seat_b_suits, seat_c_suits,
             seat_a_cards, seat_b_cards, seat_c_cards, confidence_scores, detection_details)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            timestamp,
            detection_result['image_path'],
            winning_seat,
            json.dumps(seat_a_suits),
            json.dumps(seat_b_suits),
            json.dumps(seat_c_suits),
            json.dumps(seat_a_cards),
            json.dumps(seat_b_cards),
            json.dumps(seat_c_cards),
            json.dumps(confidence_scores),
            json.dumps(detection_result['detections'], default=str)
        ))
        
        game_id = cursor.lastrowid
        
        # Insert individual detections
        for det in detection_result['detections']:
            cursor.execute('''
                INSERT INTO detections 
                (game_id, detection_type, class_name, confidence, bbox_x, bbox_y, bbox_width, bbox_height)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                game_id,
                'seat' if det['class_name'] in ['A', 'B', 'C'] or 'seat' in det['class_name'] else 'suit_or_card',
                det['class_name'],
                det['confidence'],
                det['bbox'][0],
                det['bbox'][1],
                det['bbox'][2] - det['bbox'][0],
                det['bbox'][3] - det['bbox'][1]
            ))
        
        conn.commit()
        conn.close()
        
        print(f"💾 Saved to database: Game ID {game_id}")
        
        # Print summary
        print(f"\n📊 GAME SUMMARY:")
        print(f"🏆 Winning Seat: {winning_seat or 'Not detected'}")
        print(f"🎯 Seat A Suits: {seat_a_suits}")
        print(f"🎯 Seat B Suits: {seat_b_suits}")
        print(f"🎯 Seat C Suits: {seat_c_suits}")
        print(f"🃏 Seat A Cards: {seat_a_cards}")
        print(f"🃏 Seat B Cards: {seat_b_cards}")
        print(f"🃏 Seat C Cards: {seat_c_cards}")
        
        return game_id
    
    def process_image(self, image_path):
        """Process a single image and save results"""
        detection_result = self.detect_seats_and_suits(image_path)
        if detection_result:
            game_id = self.save_to_database(detection_result)
            return game_id
        return None
    
    def process_folder(self, folder_path="train"):
        """Process all images in a folder"""
        image_files = glob.glob(f"{folder_path}/*.jpg") + glob.glob(f"{folder_path}/*.png")
        
        if not image_files:
            print(f"❌ No images found in {folder_path}")
            return
        
        print(f"📸 Processing {len(image_files)} images...")
        
        processed_count = 0
        for image_path in image_files:
            game_id = self.process_image(image_path)
            if game_id:
                processed_count += 1
        
        print(f"✅ Processed {processed_count}/{len(image_files)} images")
    
    def view_database_summary(self):
        """View summary of stored results"""
        conn = sqlite3.connect(self.db_path)
        
        # Get summary statistics
        df = pd.read_sql_query("SELECT * FROM game_results", conn)
        
        if len(df) == 0:
            print("📊 No data in database yet")
            conn.close()
            return
        
        print(f"\n📊 DATABASE SUMMARY ({len(df)} games)")
        print("=" * 40)
        
        # Winning seat distribution
        winning_seats = df['winning_seat'].value_counts()
        print(f"🏆 Winning Seat Distribution:")
        for seat, count in winning_seats.items():
            print(f"   Seat {seat}: {count} wins")
        
        # Recent games
        print(f"\n📅 Recent Games:")
        recent = df.tail(5)[['timestamp', 'winning_seat', 'seat_a_suits', 'seat_b_suits', 'seat_c_suits']]
        for _, row in recent.iterrows():
            timestamp = row['timestamp'][:19]  # Remove microseconds
            print(f"   {timestamp} | Winner: {row['winning_seat']} | A:{row['seat_a_suits']} B:{row['seat_b_suits']} C:{row['seat_c_suits']}")
        
        conn.close()


def main():
    """Main function"""
    print("🎯 Winning Seat Card Suit Detector")
    print("=" * 40)
    
    detector = WinningSeatDetector()
    
    if not detector.model:
        print("❌ Cannot proceed without model")
        return
    
    print("\n📋 Options:")
    print("1. 🔍 Process single image")
    print("2. 📁 Process all images in folder")
    print("3. 📊 View database summary")
    print("4. ❌ Exit")
    
    choice = input("\nEnter choice (1-4): ").strip()
    
    if choice == '1':
        image_path = input("Enter image path: ").strip()
        if os.path.exists(image_path):
            detector.process_image(image_path)
        else:
            print("❌ Image not found")
    
    elif choice == '2':
        folder = input("Enter folder path (default: train): ").strip() or "train"
        detector.process_folder(folder)
    
    elif choice == '3':
        detector.view_database_summary()
    
    elif choice == '4':
        print("👋 Goodbye!")
    
    else:
        print("❌ Invalid choice")


if __name__ == "__main__":
    main()
