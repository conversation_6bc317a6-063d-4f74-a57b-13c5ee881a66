"""
Integration module for Visual Reinforcement Learning with existing PolitePredict system
====================================================================================

This module integrates the new RL system with your existing prediction engine,
allowing you to compare and combine traditional ML with reinforcement learning.

Author: Augment Agent
"""

import sys
import os
import time
import threading
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox

# Import existing modules
try:
    from model import PredictionEngine
    from rl_visual_learning import VisualReinforcementLearningSystem
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure all required modules are available")

class HybridPredictionSystem:
    """
    Hybrid system that combines traditional ML with reinforcement learning
    """
    
    def __init__(self):
        # Initialize both systems
        self.traditional_engine = PredictionEngine()
        self.rl_system = VisualReinforcementLearningSystem()
        
        # System configuration
        self.use_rl = True
        self.use_traditional = True
        self.hybrid_mode = True
        
        # Performance tracking
        self.performance_comparison = {
            'traditional_ml': {'correct': 0, 'total': 0, 'accuracy': 0},
            'reinforcement_learning': {'correct': 0, 'total': 0, 'accuracy': 0},
            'hybrid': {'correct': 0, 'total': 0, 'accuracy': 0}
        }
        
        # Prediction history
        self.prediction_history = []
        
    def make_hybrid_prediction(self):
        """
        Make prediction using both systems and combine results
        """
        predictions = {}
        
        # Get traditional ML prediction
        if self.use_traditional:
            try:
                ml_prediction, ml_details, ml_confidence = self.traditional_engine.predict_next()
                predictions['traditional_ml'] = {
                    'prediction': ml_prediction,
                    'confidence': ml_confidence,
                    'details': ml_details
                }
            except Exception as e:
                print(f"Traditional ML prediction error: {e}")
                predictions['traditional_ml'] = None
        
        # Get RL prediction
        if self.use_rl:
            try:
                # Capture current game state
                current_image = self.rl_system.capture_game_image()
                state = self.rl_system.create_state_representation(current_image)
                
                # Make RL prediction
                rl_action = self.rl_system.rl_agent.act(state, training=False)
                seat_map = {0: 'A', 1: 'B', 2: 'C'}
                rl_prediction = seat_map[rl_action]
                
                # Calculate RL confidence (based on Q-values)
                import torch
                state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.rl_system.rl_agent.device)
                q_values = self.rl_system.rl_agent.q_network(state_tensor)
                q_values_np = q_values.cpu().data.numpy()[0]
                rl_confidence = int(max(q_values_np) * 100)
                
                predictions['reinforcement_learning'] = {
                    'prediction': rl_prediction,
                    'confidence': rl_confidence,
                    'q_values': q_values_np.tolist()
                }
            except Exception as e:
                print(f"RL prediction error: {e}")
                predictions['reinforcement_learning'] = None
        
        # Combine predictions if both are available
        if self.hybrid_mode and predictions.get('traditional_ml') and predictions.get('reinforcement_learning'):
            ml_pred = predictions['traditional_ml']
            rl_pred = predictions['reinforcement_learning']
            
            # Weight predictions by confidence
            ml_weight = ml_pred['confidence'] / 100.0
            rl_weight = rl_pred['confidence'] / 100.0
            
            # If both predict the same, high confidence
            if ml_pred['prediction'] == rl_pred['prediction']:
                hybrid_prediction = ml_pred['prediction']
                hybrid_confidence = min(95, int((ml_weight + rl_weight) * 50))
            else:
                # Choose prediction with higher confidence
                if ml_pred['confidence'] > rl_pred['confidence']:
                    hybrid_prediction = ml_pred['prediction']
                    hybrid_confidence = ml_pred['confidence']
                else:
                    hybrid_prediction = rl_pred['prediction']
                    hybrid_confidence = rl_pred['confidence']
            
            predictions['hybrid'] = {
                'prediction': hybrid_prediction,
                'confidence': hybrid_confidence,
                'ml_prediction': ml_pred['prediction'],
                'rl_prediction': rl_pred['prediction']
            }
        
        return predictions
    
    def update_performance(self, predictions, actual_result):
        """
        Update performance metrics for all prediction methods
        """
        for method, pred_data in predictions.items():
            if pred_data and 'prediction' in pred_data:
                self.performance_comparison[method]['total'] += 1
                
                if pred_data['prediction'] == actual_result:
                    self.performance_comparison[method]['correct'] += 1
                
                # Calculate accuracy
                total = self.performance_comparison[method]['total']
                correct = self.performance_comparison[method]['correct']
                self.performance_comparison[method]['accuracy'] = (correct / total) * 100 if total > 0 else 0
    
    def get_performance_report(self):
        """
        Get comprehensive performance comparison report
        """
        report = """
🤖 Hybrid Prediction System Performance Report
=============================================

"""
        
        for method, stats in self.performance_comparison.items():
            method_name = method.replace('_', ' ').title()
            report += f"""
📊 {method_name}:
   - Accuracy: {stats['accuracy']:.2f}%
   - Correct: {stats['correct']}/{stats['total']}
   - Success Rate: {(stats['correct']/stats['total']*100) if stats['total'] > 0 else 0:.1f}%
"""
        
        return report
    
    def run_comparison_mode(self, duration_minutes=60):
        """
        Run system in comparison mode to evaluate different approaches
        """
        print("🔬 Starting Comparison Mode...")
        print(f"⏱️ Running for {duration_minutes} minutes")
        
        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)
        
        while time.time() < end_time:
            try:
                # Make predictions
                predictions = self.make_hybrid_prediction()
                
                # Display predictions
                print("\n" + "="*50)
                print(f"🕐 {datetime.now().strftime('%H:%M:%S')}")
                
                for method, pred_data in predictions.items():
                    if pred_data:
                        method_name = method.replace('_', ' ').title()
                        print(f"🎯 {method_name}: {pred_data['prediction']} (Confidence: {pred_data['confidence']}%)")
                
                # Wait for game round to complete
                print("⏳ Waiting for round result...")
                time.sleep(60)  # Adjust based on your game timing
                
                # Get actual result (you'll need to implement this based on your game)
                actual_result = self.get_actual_result()
                
                if actual_result:
                    print(f"✅ Actual Result: {actual_result}")
                    self.update_performance(predictions, actual_result)
                    
                    # Show current performance
                    print(self.get_performance_report())
                
            except KeyboardInterrupt:
                print("🛑 Comparison mode stopped by user")
                break
            except Exception as e:
                print(f"❌ Error in comparison mode: {e}")
                time.sleep(5)
        
        print("📊 Final Performance Report:")
        print(self.get_performance_report())
    
    def get_actual_result(self):
        """
        Get actual game result - implement based on your game detection
        """
        try:
            # This should integrate with your existing game result detection
            # For now, return None - you can implement this based on your needs
            return None
        except:
            return None


class RLIntegrationGUI:
    """
    GUI for managing the RL integration
    """
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 RL Integration - PolitePredict")
        self.root.geometry("800x600")
        
        self.hybrid_system = HybridPredictionSystem()
        self.rl_running = False
        
        self.setup_gui()
    
    def setup_gui(self):
        """Setup the GUI interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="🤖 Visual Reinforcement Learning Integration", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Control buttons
        control_frame = ttk.LabelFrame(main_frame, text="🎮 System Control", padding="10")
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(control_frame, text="🎓 Start Learning Phase", 
                  command=self.start_learning_phase).grid(row=0, column=0, padx=5)
        ttk.Button(control_frame, text="🤖 Start RL Training", 
                  command=self.start_rl_training).grid(row=0, column=1, padx=5)
        ttk.Button(control_frame, text="🎮 Start Playing", 
                  command=self.start_playing).grid(row=0, column=2, padx=5)
        ttk.Button(control_frame, text="🔬 Comparison Mode", 
                  command=self.start_comparison).grid(row=1, column=0, padx=5)
        ttk.Button(control_frame, text="🛑 Stop All", 
                  command=self.stop_all).grid(row=1, column=1, padx=5)
        
        # Status display
        status_frame = ttk.LabelFrame(main_frame, text="📊 System Status", padding="10")
        status_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_text = tk.Text(status_frame, height=15, width=80)
        scrollbar = ttk.Scrollbar(status_frame, orient="vertical", command=self.status_text.yview)
        self.status_text.configure(yscrollcommand=scrollbar.set)
        
        self.status_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Performance display
        perf_frame = ttk.LabelFrame(main_frame, text="📈 Performance Metrics", padding="10")
        perf_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E))
        
        self.perf_text = tk.Text(perf_frame, height=8, width=80)
        self.perf_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # Update status periodically
        self.update_status()
    
    def log_message(self, message):
        """Add message to status display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.status_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.status_text.see(tk.END)
        self.root.update()
    
    def start_learning_phase(self):
        """Start the visual learning phase"""
        self.log_message("🎓 Starting visual learning phase...")
        threading.Thread(target=self._run_learning_phase, daemon=True).start()
    
    def _run_learning_phase(self):
        """Run learning phase in background thread"""
        try:
            self.hybrid_system.rl_system.learning_phase(num_samples=100)
            self.log_message("✅ Learning phase completed!")
        except Exception as e:
            self.log_message(f"❌ Learning phase error: {e}")
    
    def start_rl_training(self):
        """Start RL training"""
        self.log_message("🤖 Starting RL training...")
        threading.Thread(target=self._run_rl_training, daemon=True).start()
    
    def _run_rl_training(self):
        """Run RL training in background thread"""
        try:
            self.hybrid_system.rl_system.training_phase(num_episodes=200)
            self.log_message("✅ RL training completed!")
        except Exception as e:
            self.log_message(f"❌ RL training error: {e}")
    
    def start_playing(self):
        """Start the playing phase"""
        self.log_message("🎮 Starting playing phase...")
        self.rl_running = True
        threading.Thread(target=self._run_playing, daemon=True).start()
    
    def _run_playing(self):
        """Run playing phase in background thread"""
        try:
            while self.rl_running:
                predictions = self.hybrid_system.make_hybrid_prediction()
                
                for method, pred_data in predictions.items():
                    if pred_data:
                        method_name = method.replace('_', ' ').title()
                        self.log_message(f"🎯 {method_name}: {pred_data['prediction']} ({pred_data['confidence']}%)")
                
                time.sleep(60)  # Wait for next round
                
        except Exception as e:
            self.log_message(f"❌ Playing phase error: {e}")
    
    def start_comparison(self):
        """Start comparison mode"""
        self.log_message("🔬 Starting comparison mode...")
        threading.Thread(target=self._run_comparison, daemon=True).start()
    
    def _run_comparison(self):
        """Run comparison mode in background thread"""
        try:
            self.hybrid_system.run_comparison_mode(duration_minutes=30)
        except Exception as e:
            self.log_message(f"❌ Comparison mode error: {e}")
    
    def stop_all(self):
        """Stop all running processes"""
        self.rl_running = False
        self.hybrid_system.rl_system.is_playing_phase = False
        self.log_message("🛑 All processes stopped")
    
    def update_status(self):
        """Update status display periodically"""
        try:
            # Update performance metrics
            report = self.hybrid_system.get_performance_report()
            self.perf_text.delete(1.0, tk.END)
            self.perf_text.insert(1.0, report)
            
            # Update RL system status
            rl_report = self.hybrid_system.rl_system.get_performance_report()
            
        except Exception as e:
            pass
        
        # Schedule next update
        self.root.after(5000, self.update_status)  # Update every 5 seconds
    
    def run(self):
        """Run the GUI"""
        self.root.mainloop()


if __name__ == "__main__":
    print("🚀 Starting RL Integration System...")
    
    # Create and run GUI
    gui = RLIntegrationGUI()
    gui.run()
