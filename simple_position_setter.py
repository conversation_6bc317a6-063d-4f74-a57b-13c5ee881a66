"""
Simple Card Position Setter (No GUI Required)
============================================

This script helps you set card positions by analyzing your game image
and providing a simple coordinate input method.

Author: Augment Agent
"""

import cv2
import numpy as np
import os
import glob
import json
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont

class SimplePositionSetter:
    """
    Simple position setter without GUI dependencies
    """
    
    def __init__(self):
        self.card_positions = []
        
    def analyze_image_and_suggest_positions(self, image_path):
        """
        Analyze the image and suggest likely card positions
        """
        print("🔍 Analyzing your game image...")
        
        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ Could not load image: {image_path}")
            return None
        
        height, width = image.shape[:2]
        print(f"📐 Image size: {width} x {height}")
        
        # Based on your game screenshot, the cards appear to be in the center area
        # Let me suggest positions based on typical poker game layouts
        
        # Estimated card area (adjust these based on your game)
        center_x = width // 2
        center_y = height // 2
        
        # Card dimensions (estimate)
        card_width = 50
        card_height = 70
        
        # Grid spacing
        spacing_x = 60
        spacing_y = 80
        
        # Calculate starting position for 3x3 grid
        start_x = center_x - spacing_x
        start_y = center_y - spacing_y
        
        suggested_positions = []
        
        for row in range(3):
            for col in range(3):
                x = start_x + col * spacing_x
                y = start_y + row * spacing_y
                
                position = {
                    'x': x,
                    'y': y,
                    'width': card_width,
                    'height': card_height,
                    'index': row * 3 + col,
                    'row': row,
                    'col': col
                }
                suggested_positions.append(position)
        
        # Create visualization
        vis_image = image.copy()
        colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), 
                 (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0), (0, 128, 255)]
        
        for i, pos in enumerate(suggested_positions):
            color = colors[i]
            cv2.rectangle(vis_image, (pos['x'], pos['y']), 
                         (pos['x'] + pos['width'], pos['y'] + pos['height']), color, 2)
            cv2.putText(vis_image, str(i), (pos['x'] + 5, pos['y'] + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        # Save visualization
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        vis_path = f"suggested_positions_{timestamp}.jpg"
        cv2.imwrite(vis_path, vis_image)
        
        print(f"📸 Suggested positions saved: {vis_path}")
        print("🔍 Please check this image to see if the rectangles align with your cards")
        
        return suggested_positions, vis_path
    
    def manual_position_input(self):
        """
        Allow manual input of card positions
        """
        print("✏️ Manual Position Input")
        print("=" * 30)
        print("Enter the coordinates for each card position.")
        print("Format: x,y,width,height")
        print("Example: 100,150,50,70")
        print("Press Enter without input to skip a position")
        print()
        
        positions = []
        
        for i in range(9):
            row = i // 3
            col = i % 3
            
            while True:
                coords = input(f"Card {i} (Row {row}, Col {col}) - x,y,width,height: ").strip()
                
                if not coords:
                    print(f"⏭️ Skipping card {i}")
                    break
                
                try:
                    x, y, w, h = map(int, coords.split(','))
                    
                    position = {
                        'x': x, 'y': y, 'width': w, 'height': h,
                        'index': i, 'row': row, 'col': col
                    }
                    positions.append(position)
                    print(f"✅ Card {i}: ({x}, {y}, {w}, {h})")
                    break
                    
                except ValueError:
                    print("❌ Invalid format. Use: x,y,width,height (e.g., 100,150,50,70)")
        
        return positions
    
    def preset_positions_for_your_game(self):
        """
        Preset positions based on your game layout analysis
        """
        print("🎯 Using corrected positions for your poker game layout...")

        # Based on your actual screenshot, the cards are in the TOP area
        # I can see 4 cards in the top row, need to find where the other 5 are

        positions = [
            # Top row - where the actual cards are visible
            {'x': 120, 'y': 50, 'width': 90, 'height': 120, 'index': 0, 'row': 0, 'col': 0},  # 5♠ 5♠ 5♠
            {'x': 220, 'y': 50, 'width': 90, 'height': 120, 'index': 1, 'row': 0, 'col': 1},  # High Card
            {'x': 320, 'y': 50, 'width': 90, 'height': 120, 'index': 2, 'row': 0, 'col': 2},  # High Card

            # Second row - might be hidden or in different area
            {'x': 420, 'y': 50, 'width': 90, 'height': 120, 'index': 3, 'row': 1, 'col': 0},  # A♠
            {'x': 520, 'y': 50, 'width': 90, 'height': 120, 'index': 4, 'row': 1, 'col': 1},  # Possible 5th card
            {'x': 620, 'y': 50, 'width': 90, 'height': 120, 'index': 5, 'row': 1, 'col': 2},  # Possible 6th card

            # Third row - might be in a different area or hidden
            {'x': 220, 'y': 180, 'width': 90, 'height': 120, 'index': 6, 'row': 2, 'col': 0},  # Card 7
            {'x': 320, 'y': 180, 'width': 90, 'height': 120, 'index': 7, 'row': 2, 'col': 1},  # Card 8
            {'x': 420, 'y': 180, 'width': 90, 'height': 120, 'index': 8, 'row': 2, 'col': 2},  # Card 9
        ]

        return positions
    
    def create_visualization(self, image_path, positions):
        """
        Create visualization of the positions
        """
        image = cv2.imread(image_path)
        if image is None:
            return None
        
        vis_image = image.copy()
        colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), 
                 (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0), (0, 128, 255)]
        
        for pos in positions:
            i = pos['index']
            color = colors[i]
            
            cv2.rectangle(vis_image, (pos['x'], pos['y']), 
                         (pos['x'] + pos['width'], pos['y'] + pos['height']), color, 2)
            cv2.putText(vis_image, str(i), (pos['x'] + 5, pos['y'] + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        vis_path = f"card_positions_visualization_{timestamp}.jpg"
        cv2.imwrite(vis_path, vis_image)
        
        return vis_path
    
    def save_positions(self, positions):
        """Save positions to files"""
        if not positions:
            print("❌ No positions to save")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save as JSON
        json_data = {
            'timestamp': timestamp,
            'positions': positions,
            'description': 'Card positions for poker game'
        }
        
        json_path = f"card_positions_{timestamp}.json"
        with open(json_path, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        # Save as Python code for easy copying
        py_path = f"positions_code_{timestamp}.py"
        with open(py_path, 'w') as f:
            f.write("# Card positions for your poker game\n")
            f.write(f"# Generated: {timestamp}\n\n")
            f.write("def get_precise_card_positions(self):\n")
            f.write('    """Get precise card positions based on your game layout"""\n')
            f.write("    positions = [\n")
            
            for pos in positions:
                f.write(f"        {{\n")
                f.write(f"            'x': {pos['x']}, 'y': {pos['y']},\n")
                f.write(f"            'width': {pos['width']}, 'height': {pos['height']},\n")
                f.write(f"            'row': {pos['row']}, 'col': {pos['col']},\n")
                f.write(f"            'index': {pos['index']}\n")
                f.write(f"        }},\n")
            
            f.write("    ]\n")
            f.write("    return positions\n")
        
        print(f"💾 Positions saved:")
        print(f"   - JSON: {json_path}")
        print(f"   - Python code: {py_path}")
        
        # Print the code to copy
        print(f"\n📋 COPY THIS CODE to advanced_card_recognition.py:")
        print("=" * 60)
        print("Replace the get_precise_card_positions method with:")
        print()
        print("def get_precise_card_positions(self):")
        print('    """Get precise card positions based on your game layout"""')
        print("    positions = [")
        for pos in positions:
            print(f"        {{'x': {pos['x']}, 'y': {pos['y']}, 'width': {pos['width']}, 'height': {pos['height']}, 'row': {pos['row']}, 'col': {pos['col']}, 'index': {pos['index']}}},")
        print("    ]")
        print("    return positions")
        print("=" * 60)
        
        return json_path, py_path


def main():
    """Main function"""
    print("🔧 Simple Card Position Setter")
    print("=" * 35)
    
    setter = SimplePositionSetter()
    
    # Get sample image
    image_files = glob.glob("train/*.jpg")
    if not image_files:
        print("❌ No images found in train folder")
        return
    
    sample_image = image_files[0]
    print(f"📸 Using: {os.path.basename(sample_image)}")
    
    while True:
        print("\n📋 Choose method:")
        print("1. 🎯 Use corrected preset positions (for top row cards)")
        print("2. 🔍 Analyze image and suggest positions")
        print("3. ✏️ Manual coordinate input")
        print("4. 📸 Choose different image")
        print("5. ❓ Help me understand your game layout")
        print("6. ❌ Exit")
        
        choice = input("\nEnter choice (1-6): ").strip()
        
        if choice == '1':
            print("\n🎯 Using preset positions for your poker game...")
            positions = setter.preset_positions_for_your_game()
            
            # Create visualization
            vis_path = setter.create_visualization(sample_image, positions)
            if vis_path:
                print(f"📸 Visualization saved: {vis_path}")
                print("🔍 Check this image to see if positions look correct")
            
            # Ask if user wants to save
            save = input("\n💾 Save these positions? (y/n): ").strip().lower()
            if save == 'y':
                setter.save_positions(positions)
                print("✅ Positions saved! Copy the code to advanced_card_recognition.py")
        
        elif choice == '2':
            positions, vis_path = setter.analyze_image_and_suggest_positions(sample_image)
            if positions:
                print(f"📸 Check the visualization: {vis_path}")
                save = input("💾 Save suggested positions? (y/n): ").strip().lower()
                if save == 'y':
                    setter.save_positions(positions)
        
        elif choice == '3':
            positions = setter.manual_position_input()
            if positions:
                vis_path = setter.create_visualization(sample_image, positions)
                if vis_path:
                    print(f"📸 Visualization: {vis_path}")
                setter.save_positions(positions)
        
        elif choice == '4':
            print("\n📸 Available images:")
            for i, img in enumerate(image_files[:10]):
                print(f"   {i}: {os.path.basename(img)}")
            
            try:
                idx = int(input("Enter number: "))
                if 0 <= idx < len(image_files):
                    sample_image = image_files[idx]
                    print(f"✅ Selected: {os.path.basename(sample_image)}")
            except:
                print("❌ Invalid selection")
        
        elif choice == '5':
            print("\n❓ Understanding Your Game Layout")
            print("=" * 40)
            print("From your screenshot, I can see:")
            print("📍 4 cards visible in the TOP row:")
            print("   - Card 1: 5♠ 5♠ 5♠ (Trail)")
            print("   - Card 2: High Card")
            print("   - Card 3: High Card")
            print("   - Card 4: A♠ (High Card)")
            print()
            print("❓ Questions:")
            print("1. Are there 5 more cards somewhere else in the game?")
            print("2. Do cards appear in different positions during gameplay?")
            print("3. Are some cards hidden/face-down?")
            print("4. Is this a 4-card game or 9-card game?")
            print()
            print("💡 Please tell me:")
            print("- How many total cards should be detected?")
            print("- Where are the other cards located?")
            print("- Do card positions change during the game?")

        elif choice == '6':
            print("👋 Goodbye!")
            break
        
        else:
            print("❌ Invalid choice")


if __name__ == "__main__":
    main()
