"""
Fast Card Detection Algorithm
============================

A lightweight, fast algorithm that quickly learns card positions
without hanging the terminal.

Author: Augment Agent
"""

import cv2
import numpy as np
import os
import glob
import json
from datetime import datetime

class FastCardDetector:
    """
    Fast, efficient card detection that won't hang
    """
    
    def __init__(self):
        self.debug = True
        
    def quick_card_detection(self, image_path):
        """
        Quick and efficient card detection method
        """
        print(f"🔍 Analyzing: {os.path.basename(image_path)}")
        
        image = cv2.imread(image_path)
        if image is None:
            return []
        
        height, width = image.shape[:2]
        
        # Method 1: Simple white region detection (fastest)
        positions = self.detect_white_cards(image)
        
        if len(positions) < 4:  # If not enough cards found
            # Method 2: Edge-based detection
            edge_positions = self.detect_edge_cards(image)
            positions.extend(edge_positions)
        
        # Remove duplicates and sort by area
        positions = self.remove_duplicates(positions)
        positions = sorted(positions, key=lambda x: x['area'], reverse=True)
        
        # Take top 9 positions
        return positions[:9]
    
    def detect_white_cards(self, image):
        """
        Fast detection of white/light card regions
        """
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        
        # White/light color range (cards are usually white)
        lower = np.array([0, 0, 200])
        upper = np.array([180, 50, 255])
        
        mask = cv2.inRange(hsv, lower, upper)
        
        # Simple morphology to clean up
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        cards = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 1500:  # Minimum card size
                continue
                
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h
            
            # Check if it looks like a card
            if 0.5 < aspect_ratio < 2.0 and w > 40 and h > 50:
                cards.append({
                    'x': x, 'y': y, 'width': w, 'height': h,
                    'area': area, 'method': 'white_detection'
                })
        
        return cards
    
    def detect_edge_cards(self, image):
        """
        Fast edge-based card detection
        """
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Simple edge detection
        edges = cv2.Canny(gray, 100, 200)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        cards = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 1000:
                continue
                
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h
            
            if 0.6 < aspect_ratio < 1.8 and w > 35 and h > 45:
                cards.append({
                    'x': x, 'y': y, 'width': w, 'height': h,
                    'area': area, 'method': 'edge_detection'
                })
        
        return cards
    
    def remove_duplicates(self, positions):
        """
        Remove overlapping detections
        """
        if len(positions) <= 1:
            return positions
        
        unique_positions = []
        
        for pos in positions:
            is_duplicate = False
            
            for existing in unique_positions:
                # Check if positions overlap significantly
                overlap_x = max(0, min(pos['x'] + pos['width'], existing['x'] + existing['width']) - 
                              max(pos['x'], existing['x']))
                overlap_y = max(0, min(pos['y'] + pos['height'], existing['y'] + existing['height']) - 
                              max(pos['y'], existing['y']))
                
                overlap_area = overlap_x * overlap_y
                min_area = min(pos['area'], existing['area'])
                
                if overlap_area > 0.5 * min_area:  # 50% overlap threshold
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_positions.append(pos)
        
        return unique_positions
    
    def fast_train_on_images(self, folder="train"):
        """
        Fast training on all images
        """
        print("⚡ Fast Card Detection Training")
        print("=" * 35)
        
        image_files = glob.glob(f"{folder}/*.jpg") + glob.glob(f"{folder}/*.png")
        
        if not image_files:
            print(f"❌ No images found in {folder}")
            return []
        
        print(f"📸 Processing {len(image_files)} images...")
        
        all_positions = []
        
        for i, image_path in enumerate(image_files[:5]):  # Limit to 5 images for speed
            print(f"📷 Image {i+1}: {os.path.basename(image_path)}")
            
            positions = self.quick_card_detection(image_path)
            
            # Add image info
            for pos in positions:
                pos['image'] = image_path
                pos['image_index'] = i
            
            all_positions.extend(positions)
            
            # Create quick visualization
            self.create_quick_visualization(image_path, positions, i)
        
        print(f"🎯 Found {len(all_positions)} total detections")
        
        # Find most common positions
        final_positions = self.find_common_positions(all_positions)
        
        # Save results
        self.save_fast_results(final_positions)
        
        return final_positions
    
    def find_common_positions(self, all_positions):
        """
        Find positions that appear in multiple images
        """
        if len(all_positions) < 2:
            return all_positions
        
        # Simple clustering by proximity
        clusters = []
        
        for pos in all_positions:
            center_x = pos['x'] + pos['width'] // 2
            center_y = pos['y'] + pos['height'] // 2
            
            # Find if this position is close to existing cluster
            added_to_cluster = False
            
            for cluster in clusters:
                cluster_center_x = np.mean([p['x'] + p['width']//2 for p in cluster])
                cluster_center_y = np.mean([p['y'] + p['height']//2 for p in cluster])
                
                distance = np.sqrt((center_x - cluster_center_x)**2 + (center_y - cluster_center_y)**2)
                
                if distance < 80:  # Close enough to be same position
                    cluster.append(pos)
                    added_to_cluster = True
                    break
            
            if not added_to_cluster:
                clusters.append([pos])
        
        # Create final positions from clusters
        final_positions = []
        
        for i, cluster in enumerate(clusters):
            if len(cluster) >= 1:  # At least 1 detection
                avg_x = int(np.mean([p['x'] for p in cluster]))
                avg_y = int(np.mean([p['y'] for p in cluster]))
                avg_w = int(np.mean([p['width'] for p in cluster]))
                avg_h = int(np.mean([p['height'] for p in cluster]))
                
                final_positions.append({
                    'x': avg_x, 'y': avg_y, 'width': avg_w, 'height': avg_h,
                    'index': i, 'detection_count': len(cluster),
                    'confidence': len(cluster) / len(all_positions)
                })
        
        # Sort by confidence and take top 9
        final_positions.sort(key=lambda x: x['confidence'], reverse=True)
        return final_positions[:9]
    
    def create_quick_visualization(self, image_path, positions, index):
        """
        Create quick visualization
        """
        image = cv2.imread(image_path)
        if image is None:
            return
        
        colors = [(0, 255, 0), (255, 0, 0), (0, 0, 255), (255, 255, 0), 
                 (255, 0, 255), (0, 255, 255), (128, 0, 128), (255, 165, 0), (0, 128, 255)]
        
        for i, pos in enumerate(positions):
            color = colors[i % len(colors)]
            cv2.rectangle(image, (pos['x'], pos['y']), 
                         (pos['x'] + pos['width'], pos['y'] + pos['height']), color, 2)
            cv2.putText(image, str(i), (pos['x'] + 5, pos['y'] + 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
        
        os.makedirs("fast_results", exist_ok=True)
        cv2.imwrite(f"fast_results/detection_{index:03d}.jpg", image)
    
    def save_fast_results(self, positions):
        """
        Save results quickly
        """
        os.makedirs("fast_results", exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save as JSON
        results = {
            'timestamp': timestamp,
            'positions': positions,
            'method': 'fast_detection'
        }
        
        with open(f"fast_results/positions_{timestamp}.json", 'w') as f:
            json.dump(results, f, indent=2)
        
        # Generate Python code
        code_lines = [
            "# Fast-learned card positions",
            f"# Generated: {timestamp}",
            "",
            "def get_fast_learned_positions():",
            '    """Positions learned by fast algorithm"""',
            "    positions = ["
        ]
        
        for pos in positions:
            code_lines.append(f"        {{'x': {pos['x']}, 'y': {pos['y']}, 'width': {pos['width']}, 'height': {pos['height']}, 'index': {pos['index']}}},")
        
        code_lines.extend([
            "    ]",
            "    return positions"
        ])
        
        with open(f"fast_results/positions_code_{timestamp}.py", 'w') as f:
            f.write('\n'.join(code_lines))
        
        print(f"\n💾 Results saved in fast_results/")
        print(f"📋 Found {len(positions)} card positions")
        
        print("\n📋 COPY THIS CODE:")
        print("=" * 50)
        for line in code_lines:
            print(line)
        print("=" * 50)


def main():
    """Main function - fast execution"""
    print("⚡ Fast Card Detection")
    print("=" * 25)
    print("🚀 This will run quickly without hanging!")
    print()
    
    detector = FastCardDetector()
    positions = detector.fast_train_on_images()
    
    if positions:
        print(f"\n✅ Success! Found {len(positions)} card positions")
        print("📁 Check fast_results/ folder for visualizations")
        print("🔄 Copy the generated code to your recognition system")
    else:
        print("❌ No positions found. Check your images.")


if __name__ == "__main__":
    main()
