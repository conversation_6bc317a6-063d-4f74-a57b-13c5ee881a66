import os
import sys
import cv2
import numpy as np
import sqlite3
import pandas as pd
import time
import datetime
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from PIL import Image, ImageTk, ImageGrab
import threading
import queue
import joblib
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# Advanced AI imports
try:
    import pytesseract
    import platform
    # Configure Tesseract path for Windows
    if platform.system() == "Windows":
        # Common Tesseract installation paths
        tesseract_paths = [
            r"C:\Program Files\Tesseract-OCR\tesseract.exe",
            r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
            r"C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe"
        ]

        for path in tesseract_paths:
            if os.path.exists(path):
                pytesseract.pytesseract.tesseract_cmd = path
                print(f"✅ Tesseract found at: {path}")
                break
        else:
            # If not found in common paths, try the one you specified
            pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
            print(f"🔧 Using specified Tesseract path: C:\\Program Files\\Tesseract-OCR\\tesseract.exe")

    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    print("Warning: pytesseract not available. OCR functionality will be limited.")

try:
    from Levenshtein import distance as levenshtein_distance
    LEVENSHTEIN_AVAILABLE = True
except ImportError:
    LEVENSHTEIN_AVAILABLE = False
    print("Warning: Levenshtein not available. Fuzzy matching will be limited.")

# Additional AI libraries for advanced functionality
try:
    from river import drift
    from sklearn.ensemble import IsolationForest
    from sklearn.cluster import DBSCAN
    import scipy.stats as stats
    ADVANCED_AI_AVAILABLE = True
except ImportError:
    ADVANCED_AI_AVAILABLE = False
    print("Warning: Some advanced AI libraries not available.")

# Global constants
DB_PATH = "game_data.db"
PATTERN_PATH = "pattern_memory.joblib"
CROP_BOX = (680, 260, 1000, 580)  # Screenshot crop area - BlueStacks Teen Patti game area
UPDATE_INTERVAL = 60000  # Update frequency in milliseconds (1 minute)
DELAY_BETWEEN_CAPTURES = 56  # Seconds between captures

# OCR and AI constants
SIMILARITY_THRESHOLD = 80  # Percentage for similarity_rule_80
CURRENT_DAY_CUTOFF_HOUR = 5  # 5:00 AM cutoff for current day data
BLOCK_THRESHOLD = 7  # Number of rounds to consider a seat blocked
REPEAT_HIGH_CONFIDENCE = 90  # Percentage for high confidence repeat
MAX_SCREENSHOTS_STORED = 5  # Maximum number of screenshots to keep

# Auto-detection constants
AUTO_DETECTION_INTERVAL = 3000  # Check every 3 seconds for game changes
GAME_DETECTION_THRESHOLD = 0.8  # Template matching threshold
MIN_CHANGE_THRESHOLD = 0.1  # Minimum change to consider new game state
CONTINUOUS_MONITORING = True  # Enable continuous game monitoring

# Auto-capture constants
AUTO_CAPTURE_ENABLED = True  # Enable automatic screenshot capture
AUTO_CAPTURE_INTERVAL = 2000  # Auto-capture every 2 seconds when enabled
CAPTURE_DELAY_AFTER_CHANGE = 1000  # Wait 1 second after detecting change before capture
SMART_CAPTURE_MODE = True  # Only capture when significant changes detected

# Intelligent Game Mode constants
LEARNING_PHASE_DURATION = 300  # 5 minutes to learn game patterns (300 seconds)
MIN_LEARNING_SAMPLES = 20  # Minimum samples needed before starting predictions
ADAPTATION_INTERVAL = 60  # Re-evaluate and adapt every 60 seconds
CONFIDENCE_THRESHOLD_LEARNING = 70  # Lower threshold during learning phase
CONFIDENCE_THRESHOLD_PLAYING = 85  # Higher threshold during playing phase

# Card suits mapping
CARD_SUITS_MAP = {
    'S': 'Spade', 'C': 'Club', 'H': 'Heart', 'D': 'Diamond',
    '♠': 'Spade', '♣': 'Club', '♥': 'Heart', '♦': 'Diamond'
}

CARD_COLORS_MAP = {
    'Spade': 'Black', 'Club': 'Black', 'Heart': 'Red', 'Diamond': 'Red'
}

# Known trends for validation and fuzzy matching
KNOWN_TRENDS = [
    'High Card', 'Pair', 'Flush', 'Straight', 'Three of a Kind',
    'Straight Flush', 'Royal Flush', 'Two Pair', 'Full House',
    'Trail', 'Pure Sequence', 'Sequence', 'Color', 'Colour', 'Normal', 'Tilt'
]

class PolitePredict:
    def __init__(self):
        self.setup_database()
        self.pattern_memory = self.load_pattern_memory()
        self.data_queue = queue.Queue()

        # Initialize advanced AI components
        self.drift_detector = None
        self.anomaly_detector = None
        self.uncertainty_tracker = {}
        self.similarity_cache = {}
        self.screenshot_history = []

        # Initialize prediction tracking
        self.prediction_history = []
        self.session_accuracy = {'correct': 0, 'total': 0}
        self.total_accuracy = {'correct': 0, 'total': 0}

        # Initialize auto-detection components
        self.auto_detection_enabled = False
        self.last_game_state = None
        self.game_window_coords = None
        self.monitoring_thread = None
        self.game_templates = {}
        self.detection_running = False

        # Initialize auto-capture components
        self.auto_capture_enabled = False
        self.auto_capture_thread = None
        self.capture_running = False
        self.last_capture_time = 0
        self.pending_capture = False
        self.capture_queue = queue.Queue()

        # Initialize intelligent game mode
        self.game_mode_active = False
        self.game_mode_thread = None
        self.learning_phase = True
        self.learning_start_time = 0
        self.learning_samples = 0
        self.playing_phase = False
        self.adaptation_timer = 0
        self.game_intelligence = {
            'learned_patterns': {},
            'game_timing': {},
            'ocr_accuracy': {},
            'prediction_performance': {},
            'adaptation_history': []
        }

        # Initialize advanced AI if available
        if ADVANCED_AI_AVAILABLE:
            self.drift_detector = drift.ADWIN()
            self.anomaly_detector = IsolationForest(contamination=0.1, random_state=42)

        self.setup_gui()

    def setup_database(self):
        """Create database and tables if they don't exist"""
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Create winfail table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS winfail (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            A TEXT,
            B TEXT,
            C TEXT,
            timestamp TEXT
        )
        ''')

        # Create seat_changing table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS seat_changing (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            A TEXT,
            B TEXT,
            C TEXT,
            timestamp TEXT
        )
        ''')

        # Create backend_data table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS backend_data (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            winning_seat TEXT,
            card1 TEXT,
            card2 TEXT,
            card3 TEXT,
            color1 TEXT,
            color2 TEXT,
            color3 TEXT,
            trend TEXT,
            timestamp TEXT,
            session_id INTEGER
        )
        ''')

        conn.commit()
        conn.close()
        print("Database setup complete")

    def load_pattern_memory(self):
        """Load saved pattern memory if exists"""
        if os.path.exists(PATTERN_PATH):
            try:
                return joblib.load(PATTERN_PATH)
            except Exception as e:
                print(f"Error loading pattern memory: {str(e)}")

        # Initialize new pattern memory
        return {
            'repeat_patterns': {},
            'block_patterns': {},
            'singles_patterns': {},
            'card_influence': {},
            'visual_patterns': {},
            'seat_change_patterns': {},
            'prediction_accuracy': {
                'overall': [],
                'by_pattern': {}
            },
            'prediction_log': []
        }

    def save_pattern_memory(self):
        """Save pattern memory to disk"""
        try:
            joblib.dump(self.pattern_memory, PATTERN_PATH)
            print(f"Pattern memory saved with {len(self.pattern_memory)} patterns")
        except Exception as e:
            print(f"Error saving pattern memory: {str(e)}")

    def load_data(self, limit=None):
        """Load data from database with optional limit"""
        try:
            conn = sqlite3.connect(DB_PATH)

            # Load backend_data
            query = "SELECT * FROM backend_data ORDER BY id DESC"
            if limit:
                query += f" LIMIT {limit}"
            backend_df = pd.read_sql_query(query, conn)

            # Load winfail data
            winfail_query = "SELECT * FROM winfail ORDER BY id DESC"
            if limit:
                winfail_query += f" LIMIT {limit}"
            winfail_df = pd.read_sql_query(winfail_query, conn)

            # Load seat_changing data
            seat_query = "SELECT * FROM seat_changing ORDER BY id DESC"
            if limit:
                seat_query += f" LIMIT {limit}"
            seat_df = pd.read_sql_query(seat_query, conn)

            conn.close()

            return {
                'backend_data': backend_df,
                'winfail': winfail_df,
                'seat_changing': seat_df
            }
        except Exception as e:
            print(f"Error loading data: {str(e)}")
            return None

    def capture_screenshot(self):
        """Capture screenshot of the game area"""
        try:
            img = ImageGrab.grab(bbox=CROP_BOX)
            img_np = np.array(img)
            img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)

            # Save debug screenshot to help with positioning
            timestamp = datetime.datetime.now().strftime("%H%M%S")
            debug_filename = f"debug_screenshot_{timestamp}.png"
            cv2.imwrite(debug_filename, img_bgr)

            print(f"📸 Screenshot captured and saved as: {debug_filename}")
            print(f"📏 Current crop area: {CROP_BOX} (left, top, right, bottom)")
            print(f"📐 Captured image size: {img_bgr.shape[1]}x{img_bgr.shape[0]} pixels")

            # Check if image is too small
            if img_bgr.shape[1] < 200 or img_bgr.shape[0] < 200:
                print("⚠️  WARNING: Captured area seems too small!")
                print("   Your game might be outside the current crop area.")
                print("   Check the debug_screenshot file to see what's being captured.")

            return img_bgr
        except Exception as e:
            print(f"Error capturing screenshot: {str(e)}")
            return None

    def preprocess_image_for_ocr(self, image_segment, scale_factor=2):
        """Preprocesses an image segment for better OCR results."""
        if image_segment is None or image_segment.size == 0:
            return None

        # Convert to grayscale
        gray = cv2.cvtColor(image_segment, cv2.COLOR_BGR2GRAY)

        # Rescale for clarity (often helps OCR)
        width = int(gray.shape[1] * scale_factor)
        height = int(gray.shape[0] * scale_factor)
        dim = (width, height)
        resized = cv2.resize(gray, dim, interpolation=cv2.INTER_LINEAR)

        # Apply adaptive thresholding to binarize the image
        thresh = cv2.adaptiveThreshold(resized, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY_INV, 19, 9)

        return thresh

    def ocr_text_from_image_segment(self, image_segment, lang='eng', psm=7, whitelist=None):
        """Performs OCR on a preprocessed image segment."""
        if not TESSERACT_AVAILABLE or image_segment is None:
            return ""

        try:
            custom_config = f'--oem 3 --psm {psm}'
            if whitelist:
                custom_config += f' -c tessedit_char_whitelist={whitelist}'
            text = pytesseract.image_to_string(image_segment, lang=lang, config=custom_config)
            return text.strip()
        except Exception as e:
            error_msg = str(e).lower()
            if "tesseract" in error_msg or "not installed" in error_msg or "not found" in error_msg:
                print("⚠️  Tesseract OCR error. Please check:")
                print(f"   1. Tesseract path: C:\\Program Files\\Tesseract-OCR\\tesseract.exe")
                print(f"   2. PATH environment variable includes Tesseract")
                print(f"   3. Error details: {e}")
            else:
                print(f"OCR Error for segment: {e}")
            return ""

    def determine_winning_seat_from_image(self, full_cropped_image):
        """
        Determines the winning seat (A, B, or C) based on visual cues (e.g., highlight).
        This is highly game-specific and needs careful tuning.
        """
        if full_cropped_image is None or full_cropped_image.size == 0:
            return None

        # Define ROIs for areas where highlights appear for A, B, C
        # Updated coordinates based on your game layout
        highlight_rois = {
            'A': full_cropped_image[200:240, 30:130],   # Left seat highlight area
            'B': full_cropped_image[200:240, 140:240],  # Middle seat highlight area
            'C': full_cropped_image[200:240, 250:350]   # Right seat highlight area
        }

        brightness = {}
        for seat, roi_img in highlight_rois.items():
            if roi_img.size == 0:
                brightness[seat] = 0
                continue
            # Convert to grayscale and calculate mean brightness
            gray_roi = cv2.cvtColor(roi_img, cv2.COLOR_BGR2GRAY)
            brightness[seat] = np.mean(gray_roi)

        # Determine winner by max brightness (add a threshold if needed)
        if not brightness:
            return None

        # Check if all brightness values are very similar (no clear highlight)
        std_dev = np.std(list(brightness.values()))
        if std_dev < 5:  # Adjust this threshold
            print(f"Low standard deviation in brightness ({std_dev:.2f}), cannot determine winner.")
            return None

        winning_seat = max(brightness, key=brightness.get)

        # Ensure the max brightness is significantly higher than others
        sorted_brightness = sorted(brightness.items(), key=lambda item: item[1], reverse=True)
        if len(sorted_brightness) > 1:
            if sorted_brightness[0][1] < sorted_brightness[1][1] + 10:  # Adjust threshold
                print("No clear winner by brightness threshold.")
                return None

        print(f"Brightness values: {brightness}. Determined winner: {winning_seat}")
        return winning_seat

    def extract_data_from_image(self, img_bgr):
        """Extract game data from image using computer vision and OCR"""
        if img_bgr is None:
            return {"error": "No image provided."}

        # 1. Determine Winning Seat
        winning_seat_char = self.determine_winning_seat_from_image(img_bgr)
        if not winning_seat_char:
            return {"error": "Could not reliably determine winning seat from image."}

        # 2. Define ROIs for cards and trend for the winning seat
        # These coordinates need to be precisely calibrated for the game
        card_value_y_offset = (5, 35)  # top part of card image for number/letter
        card_suit_y_offset = (35, 70)  # middle/bottom part for suit symbol

        # ROI configuration for each seat (y_start, y_end, x_start, x_end)
        # Updated coordinates based on your game layout
        seat_card_rois_config = {
            'A': {
                'card1_full': (100, 180, 30, 85),    # Left seat cards
                'card2_full': (100, 180, 95, 150),
                'card3_full': (100, 180, 160, 215),
                'trend': (190, 220, 30, 215),        # Trend area below cards
            },
            'B': {
                'card1_full': (100, 180, 140, 195),  # Middle seat cards
                'card2_full': (100, 180, 205, 260),
                'card3_full': (100, 180, 270, 325),
                'trend': (190, 220, 140, 325),       # Trend area below cards
            },
            'C': {
                'card1_full': (100, 180, 250, 305),  # Right seat cards
                'card2_full': (100, 180, 315, 370),
                'card3_full': (100, 180, 380, 435),
                'trend': (190, 220, 250, 435),       # Trend area below cards
            }
        }

        rois = seat_card_rois_config[winning_seat_char]
        extracted_cards = []

        for i in range(1, 4):
            key = f'card{i}_full'
            if key not in rois:
                continue

            y_s, y_e, x_s, x_e = rois[key]

            # Ensure ROI is within image bounds
            y_e = min(y_e, img_bgr.shape[0])
            x_e = min(x_e, img_bgr.shape[1])
            if y_s >= y_e or x_s >= x_e:
                print(f"Warning: Card {i} ROI for seat {winning_seat_char} is invalid or out of bounds.")
                continue

            card_img_full = img_bgr[y_s:y_e, x_s:x_e]
            if card_img_full.size == 0:
                print(f"Warning: Card {i} image for seat {winning_seat_char} is empty.")
                continue

            # Card Value ROI (top half of card_img_full)
            card_val_img_segment = card_img_full[card_value_y_offset[0]:card_value_y_offset[1], :]
            # Card Suit ROI (bottom half of card_img_full)
            card_suit_img_segment = card_img_full[card_suit_y_offset[0]:card_suit_y_offset[1], :]

            if card_val_img_segment.size == 0 or card_suit_img_segment.size == 0:
                print(f"Warning: Card {i} value or suit segment empty for seat {winning_seat_char}.")
                continue

            processed_val_img = self.preprocess_image_for_ocr(card_val_img_segment)
            val_text = self.ocr_text_from_image_segment(processed_val_img, psm=7, whitelist='2345678910JQKA')

            processed_suit_img = self.preprocess_image_for_ocr(card_suit_img_segment)
            suit_text = self.ocr_text_from_image_segment(processed_suit_img, psm=10, whitelist='SCJHD♠♣♥♦')

            suit_name = CARD_SUITS_MAP.get(suit_text.upper(), "UnknownSuit")
            if suit_name == "UnknownSuit" and suit_text:
                # Try fuzzy match
                for k, v in CARD_SUITS_MAP.items():
                    if k.startswith(suit_text.upper()):
                        suit_name = v
                        break

            color = CARD_COLORS_MAP.get(suit_name, "UnknownColor")

            extracted_cards.append({
                'value': val_text if val_text else "N/A",
                'suit_symbol_ocr': suit_text if suit_text else "N/A",
                'suit_name': suit_name,
                'color': color
            })

        # 3. OCR Trend
        y_s, y_e, x_s, x_e = rois['trend']
        y_e = min(y_e, img_bgr.shape[0])
        x_e = min(x_e, img_bgr.shape[1])
        trend_img_segment = img_bgr[y_s:y_e, x_s:x_e]
        trend_text = "N/A"

        if trend_img_segment.size > 0:
            processed_trend_img = self.preprocess_image_for_ocr(trend_img_segment, scale_factor=1.5)
            trend_text = self.ocr_text_from_image_segment(processed_trend_img, psm=7)

            if trend_text not in KNOWN_TRENDS:
                # Basic fuzzy matching
                best_match = trend_text
                min_dist = float('inf')
                if LEVENSHTEIN_AVAILABLE:
                    for known in KNOWN_TRENDS:
                        dist = levenshtein_distance(trend_text.lower(), known.lower())
                        if dist < min_dist and dist <= 3:  # Allow up to 3 edits
                            min_dist = dist
                            best_match = known
                    trend_text = best_match

        return {
            'winning_seat': winning_seat_char,
            'cards': extracted_cards,
            'trend': trend_text,
            'error': None
        }

    def find_game_window(self):
        """Find the game window anywhere on the screen using template matching"""
        try:
            # Capture full screen
            full_screen = ImageGrab.grab()
            full_screen_np = np.array(full_screen)
            full_screen_bgr = cv2.cvtColor(full_screen_np, cv2.COLOR_RGB2BGR)

            # If we have game templates, try to find them
            if self.game_templates:
                best_match = None
                best_confidence = 0

                for template_name, template in self.game_templates.items():
                    result = cv2.matchTemplate(full_screen_bgr, template, cv2.TM_CCOEFF_NORMED)
                    _, max_val, _, max_loc = cv2.minMaxLoc(result)

                    if max_val > best_confidence and max_val > GAME_DETECTION_THRESHOLD:
                        best_confidence = max_val
                        best_match = {
                            'name': template_name,
                            'location': max_loc,
                            'confidence': max_val,
                            'template_shape': template.shape
                        }

                if best_match:
                    # Calculate game window coordinates
                    x, y = best_match['location']
                    h, w = best_match['template_shape'][:2]

                    # Expand the area to capture the full game
                    margin = 50
                    self.game_window_coords = (
                        max(0, x - margin),
                        max(0, y - margin),
                        min(full_screen.width, x + w + margin),
                        min(full_screen.height, y + h + margin)
                    )

                    print(f"Game found: {best_match['name']} at {self.game_window_coords} (confidence: {best_confidence:.2f})")
                    return True

            # If no templates or no match, try to detect game area by looking for card-like patterns
            return self.detect_game_area_by_patterns(full_screen_bgr)

        except Exception as e:
            print(f"Error finding game window: {e}")
            return False

    def detect_game_area_by_patterns(self, full_screen_bgr):
        """Detect game area by looking for card-like patterns"""
        try:
            # Convert to grayscale for pattern detection
            gray = cv2.cvtColor(full_screen_bgr, cv2.COLOR_BGR2GRAY)

            # Look for rectangular patterns (cards)
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Filter contours that look like cards
            card_like_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 1000 < area < 10000:  # Card-like size
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    if 0.6 < aspect_ratio < 1.4:  # Card-like aspect ratio
                        card_like_contours.append((x, y, w, h))

            # If we found multiple card-like objects, try to find the game area
            if len(card_like_contours) >= 3:
                # Find bounding box of all card-like objects
                min_x = min(x for x, y, w, h in card_like_contours)
                min_y = min(y for x, y, w, h in card_like_contours)
                max_x = max(x + w for x, y, w, h in card_like_contours)
                max_y = max(y + h for x, y, w, h in card_like_contours)

                # Expand the area
                margin = 100
                self.game_window_coords = (
                    max(0, min_x - margin),
                    max(0, min_y - margin),
                    min(full_screen_bgr.shape[1], max_x + margin),
                    min(full_screen_bgr.shape[0], max_y + margin)
                )

                print(f"Game area detected by patterns at {self.game_window_coords}")
                return True

            return False

        except Exception as e:
            print(f"Error detecting game area by patterns: {e}")
            return False

    def capture_game_area(self):
        """Capture the detected game area"""
        try:
            if self.game_window_coords:
                # Capture the specific game area
                img = ImageGrab.grab(bbox=self.game_window_coords)
                img_np = np.array(img)
                img_bgr = cv2.cvtColor(img_np, cv2.COLOR_RGB2BGR)
                return img_bgr
            else:
                # Fallback to default crop box
                return self.capture_screenshot()

        except Exception as e:
            print(f"Error capturing game area: {e}")
            return None

    def detect_game_state_change(self, current_image, previous_image):
        """Detect if the game state has changed significantly"""
        if previous_image is None:
            return True

        try:
            # Resize images to same size for comparison
            h, w = min(current_image.shape[0], previous_image.shape[0]), min(current_image.shape[1], previous_image.shape[1])
            current_resized = cv2.resize(current_image, (w, h))
            previous_resized = cv2.resize(previous_image, (w, h))

            # Convert to grayscale
            current_gray = cv2.cvtColor(current_resized, cv2.COLOR_BGR2GRAY)
            previous_gray = cv2.cvtColor(previous_resized, cv2.COLOR_BGR2GRAY)

            # Calculate structural similarity
            diff = cv2.absdiff(current_gray, previous_gray)
            change_percentage = np.mean(diff) / 255.0

            return change_percentage > MIN_CHANGE_THRESHOLD

        except Exception as e:
            print(f"Error detecting game state change: {e}")
            return True

    def create_game_template(self, image, template_name="game_template"):
        """Create a template from the current game state for future detection"""
        try:
            if image is not None:
                # Resize template to manageable size
                template_size = (200, 150)
                template = cv2.resize(image, template_size)
                self.game_templates[template_name] = template
                print(f"Game template '{template_name}' created")
                return True
        except Exception as e:
            print(f"Error creating game template: {e}")
        return False

    def start_continuous_monitoring(self):
        """Start continuous monitoring of the game"""
        if self.detection_running:
            print("Monitoring already running")
            return

        self.detection_running = True
        self.auto_detection_enabled = True

        # Start monitoring thread
        self.monitoring_thread = threading.Thread(target=self.monitoring_loop, daemon=True)
        self.monitoring_thread.start()

        print("Continuous monitoring started")
        self.status_var.set("Auto-detection: ACTIVE - Monitoring game...")

        # Update button states and indicator
        if hasattr(self, 'start_monitoring_btn'):
            self.start_monitoring_btn.configure(state=tk.DISABLED)
        if hasattr(self, 'stop_monitoring_btn'):
            self.stop_monitoring_btn.configure(state=tk.NORMAL)
        if hasattr(self, 'auto_status_indicator'):
            self.auto_status_indicator.configure(text="ON", fg="#28a745")

    def stop_continuous_monitoring(self):
        """Stop continuous monitoring"""
        self.detection_running = False
        self.auto_detection_enabled = False

        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=1)

        print("Continuous monitoring stopped")
        self.status_var.set("Auto-detection: STOPPED")

        # Update button states and indicator
        if hasattr(self, 'start_monitoring_btn'):
            self.start_monitoring_btn.configure(state=tk.NORMAL)
        if hasattr(self, 'stop_monitoring_btn'):
            self.stop_monitoring_btn.configure(state=tk.DISABLED)
        if hasattr(self, 'auto_status_indicator'):
            self.auto_status_indicator.configure(text="OFF", fg="#dc3545")

    def monitoring_loop(self):
        """Main monitoring loop that runs in background"""
        consecutive_errors = 0
        max_errors = 5

        while self.detection_running:
            try:
                # Find game window if not already found
                if not self.game_window_coords:
                    if not self.find_game_window():
                        time.sleep(AUTO_DETECTION_INTERVAL / 1000)
                        continue

                # Capture current game state
                current_image = self.capture_game_area()
                if current_image is None:
                    consecutive_errors += 1
                    if consecutive_errors >= max_errors:
                        print("Too many capture errors, stopping monitoring")
                        break
                    time.sleep(AUTO_DETECTION_INTERVAL / 1000)
                    continue

                # Reset error counter on successful capture
                consecutive_errors = 0

                # Check if game state has changed
                if self.detect_game_state_change(current_image, self.last_game_state):
                    print("Game state change detected, processing...")

                    # Update status
                    self.root.after(0, lambda: self.status_var.set("Auto-detection: Processing new game state..."))

                    # Process the new game state
                    self.process_detected_game_state(current_image)

                    # Store current state for next comparison
                    self.last_game_state = current_image.copy()

                # Wait before next check
                time.sleep(AUTO_DETECTION_INTERVAL / 1000)

            except Exception as e:
                print(f"Error in monitoring loop: {e}")
                consecutive_errors += 1
                if consecutive_errors >= max_errors:
                    print("Too many monitoring errors, stopping")
                    break
                time.sleep(AUTO_DETECTION_INTERVAL / 1000)

        # Clean up
        self.detection_running = False
        self.root.after(0, lambda: self.status_var.set("Auto-detection: STOPPED (Error or manual stop)"))

        # Update button states
        self.root.after(0, lambda: self.start_monitoring_btn.configure(state=tk.NORMAL) if hasattr(self, 'start_monitoring_btn') else None)
        self.root.after(0, lambda: self.stop_monitoring_btn.configure(state=tk.DISABLED) if hasattr(self, 'stop_monitoring_btn') else None)
        self.root.after(0, lambda: self.auto_status_indicator.configure(text="OFF", fg="#dc3545") if hasattr(self, 'auto_status_indicator') else None)

    def process_detected_game_state(self, image):
        """Process a newly detected game state"""
        try:
            # Extract data using OCR
            extracted_data = self.extract_data_from_image(image)

            if extracted_data and not extracted_data.get("error"):
                # Update GUI with extracted data (thread-safe)
                self.root.after(0, lambda: self.update_gui_with_detected_data(extracted_data))

                # Auto-save if confidence is high enough
                if self.should_auto_save(extracted_data):
                    self.root.after(0, lambda: self.auto_save_detected_data(extracted_data))
                else:
                    # Just update GUI for manual verification
                    self.root.after(0, lambda: self.status_var.set("Auto-detection: Data detected - Please verify and save"))
            else:
                error_msg = extracted_data.get("error", "Unknown OCR error") if extracted_data else "OCR failed"
                print(f"OCR processing failed: {error_msg}")
                self.root.after(0, lambda: self.status_var.set(f"Auto-detection: OCR failed - {error_msg}"))

        except Exception as e:
            print(f"Error processing detected game state: {e}")
            self.root.after(0, lambda: self.status_var.set(f"Auto-detection: Processing error - {str(e)}"))

    def update_gui_with_detected_data(self, extracted_data):
        """Update GUI with automatically detected data"""
        try:
            # Update winning seat
            self.winning_seat.set(extracted_data['winning_seat'])

            # Update cards
            if len(extracted_data['cards']) == 3:
                for i in range(3):
                    self.card_values[i].set(extracted_data['cards'][i]['value'])

                    # Map suit name to GUI string
                    suit_symbol_to_gui_str = {
                        'Spade': '♠ (Hukam)', 'Club': '♣ (Chirya)',
                        'Heart': '♥ (Paan)', 'Diamond': '♦ (Eant)',
                        'UnknownSuit': ''
                    }
                    gui_suit_str = suit_symbol_to_gui_str.get(extracted_data['cards'][i]['suit_name'], "")
                    self.card_suits[i].set(gui_suit_str)

            # Update trend
            self.selected_trend.set(extracted_data['trend'])

            # Flash the GUI to indicate new data
            self.flash_gui_for_new_data()

        except Exception as e:
            print(f"Error updating GUI with detected data: {e}")

    def should_auto_save(self, extracted_data):
        """Determine if the detected data should be auto-saved"""
        # Auto-save criteria:
        # 1. All required data is present
        # 2. Winning seat is valid
        # 3. All 3 cards have valid values
        # 4. Trend is recognized

        if not extracted_data or extracted_data.get("error"):
            return False

        # Check winning seat
        if extracted_data['winning_seat'] not in ['A', 'B', 'C']:
            return False

        # Check cards
        if len(extracted_data['cards']) != 3:
            return False

        for card in extracted_data['cards']:
            if card['value'] in ['N/A', ''] or card['suit_name'] == 'UnknownSuit':
                return False

        # Check trend
        if extracted_data['trend'] in ['N/A', ''] or extracted_data['trend'] not in KNOWN_TRENDS:
            return False

        return True

    def auto_save_detected_data(self, extracted_data):
        """Automatically save detected data"""
        try:
            # Validate prediction if we have one
            actual_seat = extracted_data['winning_seat']
            self.validate_prediction(actual_seat)

            # Prepare card data
            cards = []
            for card in extracted_data['cards']:
                color = card['color']
                if color == 'UnknownColor':
                    color = 'Red' if card['suit_name'] in ['Heart', 'Diamond'] else 'Black'
                cards.append((card['value'], color))

            # Save to database
            self.save_to_database(actual_seat, cards, extracted_data['trend'])

            # Update session count
            current_count = self.current_session_count.get() + 1
            self.current_session_count.set(current_count)

            # Update last win info
            self.update_last_win_info(actual_seat, cards, extracted_data['trend'])

            # Auto-trigger prediction if we have enough entries
            if current_count >= 60:
                threading.Thread(target=self.train_and_predict, daemon=True).start()

            # Clear input fields
            self.clear_input()

            # Update status
            self.status_var.set(f"Auto-detection: Data auto-saved! Session count: {current_count}")

            # Update visual display
            self.update_visual_display()

            print(f"Auto-saved: Seat {actual_seat}, Cards: {cards}, Trend: {extracted_data['trend']}")

        except Exception as e:
            print(f"Error auto-saving detected data: {e}")
            self.status_var.set(f"Auto-detection: Auto-save failed - {str(e)}")

    def flash_gui_for_new_data(self):
        """Flash the GUI to indicate new data has been detected"""
        try:
            # Change background color briefly to indicate new data
            original_bg = self.root.cget('bg')
            self.root.configure(bg='#90EE90')  # Light green
            self.root.after(500, lambda: self.root.configure(bg=original_bg))
        except Exception as e:
            print(f"Error flashing GUI: {e}")

    def calibrate_game_detection(self):
        """Calibrate the system for the current game window"""
        try:
            # Capture current screenshot
            img = self.capture_screenshot()
            if img is None:
                messagebox.showerror("Error", "Failed to capture screenshot for calibration")
                return

            # Create template from current game state
            template_name = f"game_template_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
            if self.create_game_template(img, template_name):
                # Set current crop box as game window
                self.game_window_coords = CROP_BOX

                messagebox.showinfo("Calibration",
                    f"Game detection calibrated successfully!\n"
                    f"Template '{template_name}' created.\n"
                    f"Game window set to: {self.game_window_coords}")

                self.status_var.set("Game detection calibrated - Ready for auto-monitoring")
                return True
            else:
                messagebox.showerror("Error", "Failed to create game template")
                return False

        except Exception as e:
            messagebox.showerror("Error", f"Calibration failed: {str(e)}")
            return False

    def start_auto_capture(self):
        """Start automatic screenshot capture"""
        if self.capture_running:
            print("Auto-capture already running")
            return

        self.capture_running = True
        self.auto_capture_enabled = True

        # Start capture thread
        self.auto_capture_thread = threading.Thread(target=self.auto_capture_loop, daemon=True)
        self.auto_capture_thread.start()

        print("Auto-capture started")
        self.status_var.set("Auto-capture: ACTIVE - Taking screenshots automatically...")

        # Update button states and indicator
        if hasattr(self, 'start_capture_btn'):
            self.start_capture_btn.configure(state=tk.DISABLED)
        if hasattr(self, 'stop_capture_btn'):
            self.stop_capture_btn.configure(state=tk.NORMAL)
        if hasattr(self, 'capture_status_indicator'):
            self.capture_status_indicator.configure(text="ON", fg="#28a745")

    def stop_auto_capture(self):
        """Stop automatic screenshot capture"""
        self.capture_running = False
        self.auto_capture_enabled = False

        if self.auto_capture_thread and self.auto_capture_thread.is_alive():
            self.auto_capture_thread.join(timeout=1)

        print("Auto-capture stopped")
        self.status_var.set("Auto-capture: STOPPED")

        # Update button states and indicator
        if hasattr(self, 'start_capture_btn'):
            self.start_capture_btn.configure(state=tk.NORMAL)
        if hasattr(self, 'stop_capture_btn'):
            self.stop_capture_btn.configure(state=tk.DISABLED)
        if hasattr(self, 'capture_status_indicator'):
            self.capture_status_indicator.configure(text="OFF", fg="#dc3545")

    def auto_capture_loop(self):
        """Main auto-capture loop that runs in background"""
        consecutive_errors = 0
        max_errors = 5

        while self.capture_running:
            try:
                current_time = time.time() * 1000  # Convert to milliseconds

                # Check if it's time for next capture
                if current_time - self.last_capture_time >= AUTO_CAPTURE_INTERVAL:

                    # Capture screenshot
                    if self.game_window_coords:
                        img = self.capture_game_area()
                    else:
                        img = self.capture_screenshot()

                    if img is not None:
                        # Reset error counter on successful capture
                        consecutive_errors = 0

                        # Process the capture
                        self.process_auto_captured_image(img)

                        # Update last capture time
                        self.last_capture_time = current_time

                        # Update status
                        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
                        self.root.after(0, lambda: self.status_var.set(f"Auto-capture: [{timestamp}] Screenshot captured"))

                    else:
                        consecutive_errors += 1
                        if consecutive_errors >= max_errors:
                            print("Too many capture errors, stopping auto-capture")
                            break

                # Wait before next iteration
                time.sleep(0.5)  # Check every 500ms

            except Exception as e:
                print(f"Error in auto-capture loop: {e}")
                consecutive_errors += 1
                if consecutive_errors >= max_errors:
                    print("Too many auto-capture errors, stopping")
                    break
                time.sleep(1)

        # Clean up
        self.capture_running = False
        self.root.after(0, lambda: self.status_var.set("Auto-capture: STOPPED (Error or manual stop)"))

        # Update button states and indicator
        self.root.after(0, lambda: self.start_capture_btn.configure(state=tk.NORMAL) if hasattr(self, 'start_capture_btn') else None)
        self.root.after(0, lambda: self.stop_capture_btn.configure(state=tk.DISABLED) if hasattr(self, 'stop_capture_btn') else None)
        self.root.after(0, lambda: self.capture_status_indicator.configure(text="OFF", fg="#dc3545") if hasattr(self, 'capture_status_indicator') else None)

    def process_auto_captured_image(self, img):
        """Process an automatically captured image"""
        try:
            # Store screenshot for display
            self.store_screenshot(img)

            # If smart capture mode is enabled, check for significant changes
            if SMART_CAPTURE_MODE:
                if not self.detect_game_state_change(img, self.last_game_state):
                    # No significant change, skip processing
                    return

            # Extract data using OCR
            extracted_data = self.extract_data_from_image(img)

            if extracted_data and not extracted_data.get("error"):
                # Update GUI with extracted data (thread-safe)
                self.root.after(0, lambda: self.update_gui_with_captured_data(extracted_data, img))

                # Store current state for next comparison
                self.last_game_state = img.copy()

                # Auto-save logic - use intelligent mode if active
                if self.game_mode_active:
                    if self.should_auto_save_intelligent(extracted_data):
                        self.root.after(0, lambda: self.auto_save_captured_data(extracted_data))
                    else:
                        # In learning phase, be more lenient
                        if self.learning_phase:
                            self.root.after(0, lambda: self.auto_save_captured_data(extracted_data))
                        else:
                            self.root.after(0, lambda: self.status_var.set("Auto-capture: Data captured - Please verify and save"))
                else:
                    # Standard auto-save logic
                    if self.should_auto_save(extracted_data):
                        self.root.after(0, lambda: self.auto_save_captured_data(extracted_data))
                    else:
                        self.root.after(0, lambda: self.status_var.set("Auto-capture: Data captured - Please verify and save"))
            else:
                error_msg = extracted_data.get("error", "Unknown OCR error") if extracted_data else "OCR failed"
                print(f"Auto-capture OCR failed: {error_msg}")
                # Still store the screenshot even if OCR fails
                timestamp = datetime.datetime.now().strftime("%H:%M:%S")
                self.root.after(0, lambda: self.status_var.set(f"Auto-capture: [{timestamp}] Screenshot saved, manual entry needed"))

        except Exception as e:
            print(f"Error processing auto-captured image: {e}")
            self.root.after(0, lambda: self.status_var.set(f"Auto-capture: Processing error - {str(e)}"))

    def update_gui_with_captured_data(self, extracted_data, img):
        """Update GUI with automatically captured data"""
        try:
            # Update winning seat
            self.winning_seat.set(extracted_data['winning_seat'])

            # Update cards
            if len(extracted_data['cards']) == 3:
                for i in range(3):
                    self.card_values[i].set(extracted_data['cards'][i]['value'])

                    # Map suit name to GUI string
                    suit_symbol_to_gui_str = {
                        'Spade': '♠ (Hukam)', 'Club': '♣ (Chirya)',
                        'Heart': '♥ (Paan)', 'Diamond': '♦ (Eant)',
                        'UnknownSuit': ''
                    }
                    gui_suit_str = suit_symbol_to_gui_str.get(extracted_data['cards'][i]['suit_name'], "")
                    self.card_suits[i].set(gui_suit_str)

            # Update trend
            self.selected_trend.set(extracted_data['trend'])

            # Flash the GUI to indicate new data
            self.flash_gui_for_new_data()

            # Show notification
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            print(f"[{timestamp}] Auto-captured: Seat {extracted_data['winning_seat']}, Trend: {extracted_data['trend']}")

        except Exception as e:
            print(f"Error updating GUI with captured data: {e}")

    def auto_save_captured_data(self, extracted_data):
        """Automatically save captured data"""
        try:
            # Validate prediction if we have one
            actual_seat = extracted_data['winning_seat']
            self.validate_prediction(actual_seat)

            # Prepare card data
            cards = []
            for card in extracted_data['cards']:
                color = card['color']
                if color == 'UnknownColor':
                    color = 'Red' if card['suit_name'] in ['Heart', 'Diamond'] else 'Black'
                cards.append((card['value'], color))

            # Save to database
            self.save_data_to_database(actual_seat, cards, extracted_data['trend'])

            # Update session count
            current_count = self.current_session_count.get() + 1
            self.current_session_count.set(current_count)

            # Update last win info
            self.update_last_win_info(actual_seat, cards, extracted_data['trend'])

            # Auto-trigger prediction if we have enough entries
            if current_count >= 60:
                threading.Thread(target=self.train_and_predict, daemon=True).start()

            # Clear input fields
            self.clear_input()

            # Update status
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            self.status_var.set(f"Auto-capture: [{timestamp}] Data auto-saved! Count: {current_count}")

            # Update visual display
            self.update_visual_display()

            print(f"[{timestamp}] Auto-saved: Seat {actual_seat}, Cards: {cards}, Trend: {extracted_data['trend']}")

        except Exception as e:
            print(f"Error auto-saving captured data: {e}")
            self.status_var.set(f"Auto-capture: Auto-save failed - {str(e)}")

    def trigger_immediate_capture(self):
        """Trigger an immediate screenshot capture"""
        if not self.capture_running:
            # If auto-capture is not running, do a one-time capture
            self.capture_and_process()
        else:
            # If auto-capture is running, force an immediate capture
            self.last_capture_time = 0  # Reset timer to force immediate capture

    def start_intelligent_game_mode(self):
        """Start the intelligent game mode - one button does everything"""
        if self.game_mode_active:
            print("Intelligent game mode already active")
            return

        # Initialize game mode
        self.game_mode_active = True
        self.learning_phase = True
        self.playing_phase = False
        self.learning_start_time = time.time()
        self.learning_samples = 0
        self.adaptation_timer = time.time()

        # Start the intelligent game mode thread
        self.game_mode_thread = threading.Thread(target=self.intelligent_game_loop, daemon=True)
        self.game_mode_thread.start()

        print("🎮 Intelligent Game Mode ACTIVATED!")
        self.status_var.set("🎮 GAME ON: Learning phase started - Analyzing game patterns...")

        # Update button states and status
        if hasattr(self, 'game_on_btn'):
            self.game_on_btn.configure(state=tk.DISABLED, text="🎮 GAME ACTIVE", bg="#28a745")
        if hasattr(self, 'game_off_btn'):
            self.game_off_btn.configure(state=tk.NORMAL)
        if hasattr(self, 'game_mode_status'):
            self.game_mode_status.configure(text="🎮 ACTIVE: Learning game patterns...", fg="#28a745")

    def stop_intelligent_game_mode(self):
        """Stop the intelligent game mode"""
        self.game_mode_active = False
        self.learning_phase = False
        self.playing_phase = False

        # Stop all related processes
        if self.detection_running:
            self.stop_continuous_monitoring()
        if self.capture_running:
            self.stop_auto_capture()

        if self.game_mode_thread and self.game_mode_thread.is_alive():
            self.game_mode_thread.join(timeout=2)

        print("🎮 Intelligent Game Mode DEACTIVATED")
        self.status_var.set("🎮 GAME OFF: All systems stopped")

        # Update button states and status
        if hasattr(self, 'game_on_btn'):
            self.game_on_btn.configure(state=tk.NORMAL, text="🎮 GAME ON", bg="#007bff")
        if hasattr(self, 'game_off_btn'):
            self.game_off_btn.configure(state=tk.DISABLED)
        if hasattr(self, 'game_mode_status'):
            self.game_mode_status.configure(text="🎮 Ready to start intelligent game mode", fg="#495057")

    def intelligent_game_loop(self):
        """Main intelligent game loop that manages learning and playing phases"""
        try:
            # Phase 1: Initial Setup and Calibration
            self.phase_1_setup()

            # Phase 2: Learning Phase
            self.phase_2_learning()

            # Phase 3: Playing Phase
            self.phase_3_playing()

        except Exception as e:
            print(f"Error in intelligent game loop: {e}")
            self.root.after(0, lambda: self.status_var.set(f"🎮 GAME ERROR: {str(e)}"))
        finally:
            # Cleanup
            self.game_mode_active = False
            self.root.after(0, lambda: self.stop_intelligent_game_mode())

    def phase_1_setup(self):
        """Phase 1: Automatic setup and calibration"""
        self.root.after(0, lambda: self.status_var.set("🎮 Phase 1: Setting up game detection..."))

        # Step 1: Find and calibrate game window
        setup_attempts = 0
        max_setup_attempts = 10

        while setup_attempts < max_setup_attempts and self.game_mode_active:
            if self.find_game_window():
                print("✅ Game window detected automatically")
                break
            else:
                # Try to capture and create template
                img = self.capture_screenshot()
                if img is not None:
                    template_name = f"auto_game_template_{int(time.time())}"
                    if self.create_game_template(img, template_name):
                        self.game_window_coords = CROP_BOX
                        print("✅ Game template created from current view")
                        break

                setup_attempts += 1
                time.sleep(2)

        if setup_attempts >= max_setup_attempts:
            raise Exception("Could not detect or calibrate game window")

        # Step 2: Start monitoring systems
        self.root.after(0, lambda: self.start_continuous_monitoring())
        time.sleep(1)  # Allow monitoring to start

        self.root.after(0, lambda: self.start_auto_capture())
        time.sleep(1)  # Allow capture to start

        print("✅ Phase 1 Complete: Game detection and monitoring active")

    def phase_2_learning(self):
        """Phase 2: Learning phase - collect data and learn patterns"""
        self.root.after(0, lambda: self.status_var.set("🎮 Phase 2: Learning game patterns..."))

        learning_start = time.time()
        last_progress_update = 0

        while (time.time() - learning_start < LEARNING_PHASE_DURATION and
               self.learning_samples < MIN_LEARNING_SAMPLES * 3 and  # Learn more for better accuracy
               self.game_mode_active):

            # Update progress every 10 seconds
            elapsed = time.time() - learning_start
            if elapsed - last_progress_update >= 10:
                progress = min(100, (elapsed / LEARNING_PHASE_DURATION) * 100)
                samples_progress = min(100, (self.learning_samples / (MIN_LEARNING_SAMPLES * 3)) * 100)
                overall_progress = max(progress, samples_progress)

                self.root.after(0, lambda p=overall_progress: self.status_var.set(
                    f"🎮 Learning: {p:.1f}% complete, {self.learning_samples} samples collected"))
                last_progress_update = elapsed

            # Analyze current learning progress
            self.analyze_learning_progress()

            # Adaptive learning - adjust thresholds based on OCR success
            self.adapt_learning_parameters()

            time.sleep(5)  # Check every 5 seconds

        # Evaluate learning success
        if self.learning_samples >= MIN_LEARNING_SAMPLES:
            print(f"✅ Phase 2 Complete: Learned from {self.learning_samples} samples")
            self.learning_phase = False
            self.playing_phase = True
        else:
            raise Exception(f"Insufficient learning data: only {self.learning_samples} samples collected")

    def phase_3_playing(self):
        """Phase 3: Playing phase - make predictions and adapt"""
        self.root.after(0, lambda: self.status_var.set("🎮 Phase 3: Playing mode - Making predictions..."))

        last_prediction_time = 0
        last_adaptation_time = time.time()

        while self.game_mode_active:
            current_time = time.time()

            # Make predictions if we have enough data
            if (self.current_session_count.get() >= MIN_LEARNING_SAMPLES and
                current_time - last_prediction_time >= 30):  # Predict every 30 seconds

                self.root.after(0, lambda: self.train_and_predict())
                last_prediction_time = current_time

            # Adaptive learning - re-evaluate and adapt
            if current_time - last_adaptation_time >= ADAPTATION_INTERVAL:
                self.adapt_playing_parameters()
                last_adaptation_time = current_time

            # Update status with current performance
            accuracy = self.get_current_accuracy()
            session_count = self.current_session_count.get()

            self.root.after(0, lambda a=accuracy, s=session_count: self.status_var.set(
                f"🎮 PLAYING: {s} games, {a:.1f}% accuracy - AI learning and predicting"))

            time.sleep(10)  # Update every 10 seconds

    def analyze_learning_progress(self):
        """Analyze the progress of the learning phase"""
        try:
            # Get current session data
            current_count = self.current_session_count.get()

            if current_count > self.learning_samples:
                new_samples = current_count - self.learning_samples
                self.learning_samples = current_count

                # Analyze OCR accuracy
                self.analyze_ocr_performance()

                # Analyze timing patterns
                self.analyze_game_timing()

                # Store learning progress
                self.game_intelligence['adaptation_history'].append({
                    'timestamp': time.time(),
                    'phase': 'learning',
                    'samples': self.learning_samples,
                    'ocr_accuracy': self.get_ocr_accuracy(),
                    'game_timing': self.get_average_game_timing()
                })

                print(f"📊 Learning progress: {self.learning_samples} samples, "
                      f"OCR: {self.get_ocr_accuracy():.1f}%")

        except Exception as e:
            print(f"Error analyzing learning progress: {e}")

    def adapt_learning_parameters(self):
        """Adapt learning parameters based on current performance"""
        try:
            ocr_accuracy = self.get_ocr_accuracy()

            # Adjust confidence thresholds based on OCR performance
            if ocr_accuracy < 50:
                # Poor OCR - lower thresholds, increase learning time
                global CONFIDENCE_THRESHOLD_LEARNING
                CONFIDENCE_THRESHOLD_LEARNING = max(50, CONFIDENCE_THRESHOLD_LEARNING - 5)
                print(f"🔧 Adapted: Lowered learning threshold to {CONFIDENCE_THRESHOLD_LEARNING}%")

            elif ocr_accuracy > 80:
                # Good OCR - can increase thresholds
                CONFIDENCE_THRESHOLD_LEARNING = min(80, CONFIDENCE_THRESHOLD_LEARNING + 2)
                print(f"🔧 Adapted: Raised learning threshold to {CONFIDENCE_THRESHOLD_LEARNING}%")

        except Exception as e:
            print(f"Error adapting learning parameters: {e}")

    def adapt_playing_parameters(self):
        """Adapt playing parameters based on current performance"""
        try:
            accuracy = self.get_current_accuracy()

            # Adjust prediction confidence based on accuracy
            if accuracy < 60:
                # Poor accuracy - be more conservative
                global CONFIDENCE_THRESHOLD_PLAYING
                CONFIDENCE_THRESHOLD_PLAYING = min(95, CONFIDENCE_THRESHOLD_PLAYING + 5)
                print(f"🔧 Adapted: Raised playing threshold to {CONFIDENCE_THRESHOLD_PLAYING}%")

            elif accuracy > 80:
                # Good accuracy - can be more aggressive
                CONFIDENCE_THRESHOLD_PLAYING = max(75, CONFIDENCE_THRESHOLD_PLAYING - 2)
                print(f"🔧 Adapted: Lowered playing threshold to {CONFIDENCE_THRESHOLD_PLAYING}%")

            # Store adaptation
            self.game_intelligence['adaptation_history'].append({
                'timestamp': time.time(),
                'phase': 'playing',
                'accuracy': accuracy,
                'threshold_adjusted': CONFIDENCE_THRESHOLD_PLAYING
            })

        except Exception as e:
            print(f"Error adapting playing parameters: {e}")

    def analyze_ocr_performance(self):
        """Analyze OCR performance and accuracy"""
        try:
            # This would analyze recent OCR results
            # For now, we'll estimate based on successful auto-saves vs manual entries
            total_entries = self.current_session_count.get()
            if total_entries > 0:
                # Estimate OCR success rate (this is a simplified calculation)
                estimated_accuracy = min(95, 60 + (total_entries * 2))  # Improves with more data
                self.game_intelligence['ocr_accuracy']['current'] = estimated_accuracy
                return estimated_accuracy
            return 50
        except Exception as e:
            print(f"Error analyzing OCR performance: {e}")
            return 50

    def analyze_game_timing(self):
        """Analyze game timing patterns"""
        try:
            # Analyze timing between captures/saves
            current_time = time.time()
            if hasattr(self, 'last_game_timing'):
                interval = current_time - self.last_game_timing
                if 'intervals' not in self.game_intelligence['game_timing']:
                    self.game_intelligence['game_timing']['intervals'] = []

                self.game_intelligence['game_timing']['intervals'].append(interval)

                # Keep only recent intervals
                if len(self.game_intelligence['game_timing']['intervals']) > 20:
                    self.game_intelligence['game_timing']['intervals'] = \
                        self.game_intelligence['game_timing']['intervals'][-20:]

            self.last_game_timing = current_time
        except Exception as e:
            print(f"Error analyzing game timing: {e}")

    def get_ocr_accuracy(self):
        """Get current OCR accuracy estimate"""
        try:
            return self.game_intelligence['ocr_accuracy'].get('current', 50)
        except:
            return 50

    def get_average_game_timing(self):
        """Get average game timing"""
        try:
            intervals = self.game_intelligence['game_timing'].get('intervals', [])
            if intervals:
                return sum(intervals) / len(intervals)
            return 30  # Default estimate
        except:
            return 30

    def get_current_accuracy(self):
        """Get current prediction accuracy"""
        try:
            if self.session_accuracy['total'] > 0:
                return (self.session_accuracy['correct'] / self.session_accuracy['total']) * 100
            return 0
        except:
            return 0

    def should_auto_save_intelligent(self, extracted_data):
        """Enhanced auto-save logic for intelligent mode"""
        if not extracted_data or extracted_data.get("error"):
            return False

        # Use different thresholds based on current phase
        if self.learning_phase:
            threshold = CONFIDENCE_THRESHOLD_LEARNING
        else:
            threshold = CONFIDENCE_THRESHOLD_PLAYING

        # Basic validation
        if not self.should_auto_save(extracted_data):
            return False

        # Additional intelligent checks
        ocr_confidence = self.estimate_ocr_confidence(extracted_data)

        return ocr_confidence >= threshold

    def estimate_ocr_confidence(self, extracted_data):
        """Estimate OCR confidence based on data quality"""
        try:
            confidence = 100

            # Check winning seat
            if extracted_data['winning_seat'] not in ['A', 'B', 'C']:
                confidence -= 30

            # Check cards
            for card in extracted_data['cards']:
                if card['value'] in ['N/A', '']:
                    confidence -= 15
                if card['suit_name'] == 'UnknownSuit':
                    confidence -= 10

            # Check trend
            if extracted_data['trend'] not in KNOWN_TRENDS:
                confidence -= 20

            return max(0, confidence)
        except:
            return 0

    def update_seat_changing_table(self, current_win, previous_win):
        """Update seat_changing table based on win transition"""
        if not previous_win:
            return

        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        # Create a row with the transition (e.g., "AB" means A to B)
        row = {"A": None, "B": None, "C": None}
        transition = previous_win + current_win
        row[previous_win] = transition

        cursor.execute("""
            INSERT INTO seat_changing (A, B, C, timestamp)
            VALUES (?, ?, ?, datetime('now', 'localtime'))
        """, (row["A"], row["B"], row["C"]))

        conn.commit()
        conn.close()

    def save_data_to_database(self, winning_seat, cards, trend):
        """Save data to database - helper method for auto-save"""
        try:
            # Connect to database
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Insert into backend_data
            cursor.execute("""
                INSERT INTO backend_data (winning_seat, card1, card2, card3, color1, color2, color3, trend, timestamp, session_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), ?)
            """, (
                winning_seat,
                cards[0][0], cards[1][0], cards[2][0],
                cards[0][1], cards[1][1], cards[2][1],
                trend,
                1  # Default session ID
            ))

            # Update winfail table
            winfail_row = []
            for seat in ['A', 'B', 'C']:
                if seat == winning_seat:
                    winfail_row.append("Win")
                else:
                    winfail_row.append("Fail")

            cursor.execute("""
                INSERT INTO winfail (A, B, C, timestamp)
                VALUES (?, ?, ?, datetime('now', 'localtime'))
            """, tuple(winfail_row))

            # Get previous winning seat for seat_changing table
            cursor.execute("SELECT winning_seat FROM backend_data ORDER BY id DESC LIMIT 1 OFFSET 1")
            result = cursor.fetchone()
            previous_win = result[0] if result else None

            # Update seat_changing table if we have a previous win
            if previous_win:
                self.update_seat_changing_table(winning_seat, previous_win)

            conn.commit()
            conn.close()

            return True
        except Exception as e:
            print(f"Error saving to database: {e}")
            return False

    def get_current_day_data(self):
        """Get data from today 5:00 AM onwards"""
        try:
            conn = sqlite3.connect(DB_PATH)

            # Calculate cutoff time (today 5:00 AM)
            now = datetime.datetime.now()
            if now.hour < CURRENT_DAY_CUTOFF_HOUR:
                # If before 5 AM, use yesterday 5 AM as cutoff
                cutoff_date = now.date() - datetime.timedelta(days=1)
            else:
                # If after 5 AM, use today 5 AM as cutoff
                cutoff_date = now.date()

            cutoff_datetime = datetime.datetime.combine(cutoff_date, datetime.time(CURRENT_DAY_CUTOFF_HOUR, 0, 0))
            cutoff_str = cutoff_datetime.strftime("%Y-%m-%d %H:%M:%S")

            # Load current day data
            backend_query = f"SELECT * FROM backend_data WHERE timestamp >= '{cutoff_str}' ORDER BY id DESC"
            winfail_query = f"SELECT * FROM winfail WHERE timestamp >= '{cutoff_str}' ORDER BY id DESC"
            seat_query = f"SELECT * FROM seat_changing WHERE timestamp >= '{cutoff_str}' ORDER BY id DESC"

            backend_df = pd.read_sql_query(backend_query, conn)
            winfail_df = pd.read_sql_query(winfail_query, conn)
            seat_df = pd.read_sql_query(seat_query, conn)

            conn.close()

            return {
                'backend_data': backend_df,
                'winfail': winfail_df,
                'seat_changing': seat_df
            }
        except Exception as e:
            print(f"Error loading current day data: {str(e)}")
            return None

    def get_previous_data(self):
        """Get data from before today 5:00 AM"""
        try:
            conn = sqlite3.connect(DB_PATH)

            # Calculate cutoff time (today 5:00 AM)
            now = datetime.datetime.now()
            if now.hour < CURRENT_DAY_CUTOFF_HOUR:
                cutoff_date = now.date() - datetime.timedelta(days=1)
            else:
                cutoff_date = now.date()

            cutoff_datetime = datetime.datetime.combine(cutoff_date, datetime.time(CURRENT_DAY_CUTOFF_HOUR, 0, 0))
            cutoff_str = cutoff_datetime.strftime("%Y-%m-%d %H:%M:%S")

            # Load previous data
            backend_query = f"SELECT * FROM backend_data WHERE timestamp < '{cutoff_str}' ORDER BY id DESC"
            winfail_query = f"SELECT * FROM winfail WHERE timestamp < '{cutoff_str}' ORDER BY id DESC"
            seat_query = f"SELECT * FROM seat_changing WHERE timestamp < '{cutoff_str}' ORDER BY id DESC"

            backend_df = pd.read_sql_query(backend_query, conn)
            winfail_df = pd.read_sql_query(winfail_query, conn)
            seat_df = pd.read_sql_query(seat_query, conn)

            conn.close()

            return {
                'backend_data': backend_df,
                'winfail': winfail_df,
                'seat_changing': seat_df
            }
        except Exception as e:
            print(f"Error loading previous data: {str(e)}")
            return None

    def check_similarity_rule_80(self, current_data, previous_data):
        """
        Check if previous data has 80%+ similarity with last 60 bets
        Based on winfail and seat_changing tables only
        """
        if not current_data or not previous_data:
            return False

        current_backend = current_data['backend_data']
        current_winfail = current_data['winfail']
        current_seat = current_data['seat_changing']

        previous_backend = previous_data['backend_data']
        previous_winfail = previous_data['winfail']
        previous_seat = previous_data['seat_changing']

        if len(current_backend) < 60:
            return False

        # Get last 60 records from current data
        current_60_backend = current_backend.iloc[:60]

        # 1. Compare winning seats (winfail table equivalent)
        current_60_seats = current_60_backend['winning_seat'].tolist()

        # Find best matching 60-bet window in previous data
        best_similarity = 0

        if len(previous_backend) >= 60:
            for start_idx in range(len(previous_backend) - 59):
                previous_window = previous_backend.iloc[start_idx:start_idx+60]
                previous_seats = previous_window['winning_seat'].tolist()

                # Calculate seat similarity
                seat_matches = sum(1 for i in range(60) if current_60_seats[i] == previous_seats[i])
                seat_similarity = (seat_matches / 60) * 100

                # Calculate seat transition similarity
                transition_similarity = self.calculate_transition_similarity(
                    current_60_seats, previous_seats
                )

                # Combined similarity (weighted average)
                combined_similarity = (seat_similarity * 0.6) + (transition_similarity * 0.4)

                if combined_similarity > best_similarity:
                    best_similarity = combined_similarity

        # Cache the similarity result
        cache_key = f"similarity_{len(current_backend)}_{len(previous_backend)}"
        self.similarity_cache[cache_key] = {
            'similarity': best_similarity,
            'timestamp': datetime.datetime.now(),
            'meets_threshold': best_similarity >= SIMILARITY_THRESHOLD
        }

        print(f"Similarity check: {best_similarity:.2f}% (threshold: {SIMILARITY_THRESHOLD}%)")
        return best_similarity >= SIMILARITY_THRESHOLD

    def calculate_transition_similarity(self, current_seats, previous_seats):
        """Calculate similarity in seat transition patterns"""
        if len(current_seats) < 2 or len(previous_seats) < 2:
            return 0

        # Extract transitions (A->B, B->C, etc.)
        current_transitions = []
        previous_transitions = []

        for i in range(len(current_seats) - 1):
            current_transitions.append(current_seats[i] + current_seats[i+1])

        for i in range(len(previous_seats) - 1):
            previous_transitions.append(previous_seats[i] + previous_seats[i+1])

        # Calculate transition pattern similarity
        matches = 0
        total = min(len(current_transitions), len(previous_transitions))

        for i in range(total):
            if current_transitions[i] == previous_transitions[i]:
                matches += 1

        return (matches / total) * 100 if total > 0 else 0

    def analyze_patterns(self):
        """Analyze patterns in the data for prediction with advanced AI"""
        # Get current day and previous data
        current_data = self.get_current_day_data()
        previous_data = self.get_previous_data()

        if not current_data or len(current_data['backend_data']) < 10:
            print("Not enough current data for pattern analysis")
            return

        # Check similarity rule 80
        use_previous_data = False
        if previous_data and len(previous_data['backend_data']) >= 60:
            use_previous_data = self.check_similarity_rule_80(current_data, previous_data)
            print(f"Previous data inclusion: {use_previous_data}")

        # Combine data if similarity rule is met
        if use_previous_data:
            combined_backend = pd.concat([current_data['backend_data'], previous_data['backend_data']], ignore_index=True)
            combined_winfail = pd.concat([current_data['winfail'], previous_data['winfail']], ignore_index=True)
            combined_seat = pd.concat([current_data['seat_changing'], previous_data['seat_changing']], ignore_index=True)
        else:
            combined_backend = current_data['backend_data']
            combined_winfail = current_data['winfail']
            combined_seat = current_data['seat_changing']

        # Advanced pattern analysis
        self.analyze_repeat_patterns_advanced(combined_backend)
        self.analyze_block_patterns_advanced(combined_backend)
        self.analyze_card_influence_advanced(combined_backend)
        self.analyze_visual_patterns_advanced(combined_winfail)
        self.analyze_seat_changing_patterns(combined_seat, combined_backend)
        self.analyze_ngram_patterns(combined_backend)
        self.analyze_geometric_patterns(combined_winfail)

        # Detect anomalies and concept drift
        if ADVANCED_AI_AVAILABLE:
            self.detect_anomalies(combined_backend)
            self.update_drift_detection(combined_backend)

        # Save pattern memory
        self.save_pattern_memory()

    def analyze_repeat_patterns_advanced(self, df):
        """Advanced analysis of repeat patterns with AI"""
        if len(df) < 10:
            return

        seats = df['winning_seat'].tolist()

        # Enhanced repeat analysis
        repeat_sequences = []
        current_seat = None
        current_count = 0

        for seat in seats:
            if seat == current_seat:
                current_count += 1
            else:
                if current_count > 1:
                    repeat_sequences.append({
                        'seat': current_seat,
                        'length': current_count,
                        'position': len(repeat_sequences)
                    })
                current_seat = seat
                current_count = 1

        # Add final sequence if it's a repeat
        if current_count > 1:
            repeat_sequences.append({
                'seat': current_seat,
                'length': current_count,
                'position': len(repeat_sequences)
            })

        # Analyze repeat intervals and patterns
        for seat in ['A', 'B', 'C']:
            seat_repeats = [r for r in repeat_sequences if r['seat'] == seat]

            if seat_repeats:
                lengths = [r['length'] for r in seat_repeats]
                intervals = []

                # Calculate intervals between repeats
                for i in range(1, len(seat_repeats)):
                    interval = seat_repeats[i]['position'] - seat_repeats[i-1]['position']
                    intervals.append(interval)

                # Store advanced repeat patterns
                self.pattern_memory['repeat_patterns'][seat] = {
                    'avg_length': np.mean(lengths),
                    'std_length': np.std(lengths),
                    'max_length': max(lengths),
                    'min_length': min(lengths),
                    'avg_interval': np.mean(intervals) if intervals else 0,
                    'std_interval': np.std(intervals) if intervals else 0,
                    'total_repeats': len(seat_repeats),
                    'repeat_probability': len(seat_repeats) / len(seats) if seats else 0,
                    'recent_trend': self.calculate_recent_repeat_trend(seats, seat)
                }

    def calculate_recent_repeat_trend(self, seats, target_seat):
        """Calculate recent trend for repeat patterns"""
        if len(seats) < 20:
            return 0

        recent_seats = seats[:20]  # Last 20 bets
        repeat_count = 0

        for i in range(1, len(recent_seats)):
            if recent_seats[i] == recent_seats[i-1] == target_seat:
                repeat_count += 1

        return repeat_count / 20

    def analyze_block_patterns_advanced(self, df):
        """Advanced analysis of block patterns"""
        if len(df) < 20:
            return

        seats = df['winning_seat'].tolist()

        # Track blocks for each seat
        for seat in ['A', 'B', 'C']:
            blocks = []
            current_block = 0

            for s in seats:
                if s == seat:
                    if current_block >= BLOCK_THRESHOLD:
                        blocks.append(current_block)
                    current_block = 0
                else:
                    current_block += 1

            # Add current block if it's significant
            if current_block >= BLOCK_THRESHOLD:
                blocks.append(current_block)

            if blocks:
                self.pattern_memory['block_patterns'][seat] = {
                    'avg_length': np.mean(blocks),
                    'std_length': np.std(blocks),
                    'max_length': max(blocks),
                    'min_length': min(blocks),
                    'total_blocks': len(blocks),
                    'current_block_length': current_block,
                    'is_currently_blocked': current_block >= BLOCK_THRESHOLD,
                    'block_probability': len(blocks) / len(seats) if seats else 0,
                    'expected_break_soon': self.predict_block_break(blocks, current_block)
                }

    def predict_block_break(self, historical_blocks, current_block):
        """Predict if a block is likely to break soon"""
        if not historical_blocks or current_block < BLOCK_THRESHOLD:
            return False

        avg_block = np.mean(historical_blocks)
        std_block = np.std(historical_blocks)

        # If current block is significantly longer than average, likely to break
        return current_block > (avg_block + std_block)

    def analyze_card_influence_advanced(self, df):
        """Advanced analysis of card influence on winning patterns"""
        if len(df) < 20:
            return

        # Card reappearance analysis
        card_combinations = {}
        card_trends = {}

        for _, row in df.iterrows():
            seat = row['winning_seat']
            cards = tuple(sorted([row['card1'], row['card2'], row['card3']]))
            trend = row['trend']

            # Track card combinations
            if cards not in card_combinations:
                card_combinations[cards] = {'A': 0, 'B': 0, 'C': 0, 'occurrences': []}
            card_combinations[cards][seat] += 1
            card_combinations[cards]['occurrences'].append({
                'seat': seat,
                'timestamp': row.get('timestamp', ''),
                'trend': trend
            })

            # Track trend patterns
            if trend not in card_trends:
                card_trends[trend] = {'A': 0, 'B': 0, 'C': 0}
            card_trends[trend][seat] += 1

        # Analyze significant patterns
        significant_cards = {}
        for cards, data in card_combinations.items():
            total = sum(data[seat] for seat in ['A', 'B', 'C'])
            if total >= 3:  # Minimum occurrences
                max_seat = max(['A', 'B', 'C'], key=lambda s: data[s])
                confidence = (data[max_seat] / total) * 100

                if confidence >= 70:  # High confidence threshold
                    significant_cards[cards] = {
                        'preferred_seat': max_seat,
                        'confidence': confidence,
                        'total_occurrences': total,
                        'last_occurrence': data['occurrences'][-1] if data['occurrences'] else None
                    }

        self.pattern_memory['card_influence'] = {
            'significant_combinations': significant_cards,
            'trend_preferences': card_trends,
            'reappearance_rate': len(significant_cards) / len(card_combinations) if card_combinations else 0
        }

    def analyze_visual_patterns_advanced(self, winfail_df):
        """Advanced analysis of visual patterns in winfail table"""
        if len(winfail_df) < 20:
            return

        # Convert winfail data to seat sequence
        seats = []
        for _, row in winfail_df.iterrows():
            if row.get('A') == 'Win':
                seats.append('A')
            elif row.get('B') == 'Win':
                seats.append('B')
            elif row.get('C') == 'Win':
                seats.append('C')

        # Analyze geometric patterns
        patterns = {
            'diagonals': self.find_diagonal_patterns(seats),
            'l_shapes': self.find_l_patterns(seats),
            'v_shapes': self.find_v_patterns(seats),
            'z_shapes': self.find_z_patterns(seats),
            'lines': self.find_line_patterns(seats),
            'alphabets': self.find_alphabet_patterns(seats)
        }

        self.pattern_memory['visual_patterns'] = patterns

    def find_diagonal_patterns(self, seats):
        """Find diagonal patterns like A->B->C or C->B->A"""
        diagonals = []
        for i in range(len(seats) - 2):
            if seats[i:i+3] == ['A', 'B', 'C']:
                diagonals.append(('A->B->C', i))
            elif seats[i:i+3] == ['C', 'B', 'A']:
                diagonals.append(('C->B->A', i))
        return diagonals

    def find_l_patterns(self, seats):
        """Find L-shaped patterns"""
        l_patterns = []
        for i in range(len(seats) - 2):
            # L patterns: AA->B, BB->C, CC->A, etc.
            if seats[i] == seats[i+1] and seats[i+1] != seats[i+2]:
                pattern = f"{seats[i]}{seats[i+1]}->{seats[i+2]}"
                l_patterns.append((pattern, i))
        return l_patterns

    def find_v_patterns(self, seats):
        """Find V-shaped patterns"""
        v_patterns = []
        for i in range(len(seats) - 2):
            # V patterns: A->B->A, B->C->B, etc.
            if seats[i] == seats[i+2] and seats[i] != seats[i+1]:
                pattern = f"{seats[i]}->{seats[i+1]}->{seats[i+2]}"
                v_patterns.append((pattern, i))
        return v_patterns

    def find_z_patterns(self, seats):
        """Find Z-shaped patterns"""
        z_patterns = []
        for i in range(len(seats) - 3):
            # Z patterns: A->A->B->B, etc.
            if (seats[i] == seats[i+1] and seats[i+2] == seats[i+3] and
                seats[i] != seats[i+2]):
                pattern = f"{seats[i]}{seats[i+1]}->{seats[i+2]}{seats[i+3]}"
                z_patterns.append((pattern, i))
        return z_patterns

    def find_line_patterns(self, seats):
        """Find straight line patterns"""
        lines = []
        for i in range(len(seats) - 2):
            # Horizontal lines: AAA, BBB, CCC
            if seats[i] == seats[i+1] == seats[i+2]:
                lines.append((f"{seats[i]}-line", i))
        return lines

    def find_alphabet_patterns(self, seats):
        """Find alphabet-like patterns"""
        alphabets = []
        # This would be expanded based on specific alphabet patterns observed
        # For now, implementing basic patterns
        for i in range(len(seats) - 4):
            sequence = seats[i:i+5]
            # Example: A pattern could be A->B->A->B->A
            if sequence == ['A', 'B', 'A', 'B', 'A']:
                alphabets.append(('ABABA-pattern', i))
        return alphabets

    def analyze_seat_changing_patterns(self, seat_df, backend_df):
        """Analyze seat changing patterns"""
        if len(seat_df) < 10 or len(backend_df) < 10:
            return

        # Analyze transition frequencies
        transitions = {'A': {}, 'B': {}, 'C': {}}

        for _, row in seat_df.iterrows():
            for seat in ['A', 'B', 'C']:
                transition = row.get(seat)
                if transition and len(transition) == 2:
                    from_seat, to_seat = transition[0], transition[1]
                    if to_seat not in transitions[from_seat]:
                        transitions[from_seat][to_seat] = 0
                    transitions[from_seat][to_seat] += 1

        # Calculate transition probabilities
        transition_probs = {}
        for from_seat in transitions:
            total = sum(transitions[from_seat].values())
            if total > 0:
                transition_probs[from_seat] = {
                    to_seat: count / total
                    for to_seat, count in transitions[from_seat].items()
                }

        self.pattern_memory['seat_change_patterns'] = {
            'transition_counts': transitions,
            'transition_probabilities': transition_probs,
            'most_likely_transitions': self.get_most_likely_transitions(transition_probs)
        }

    def get_most_likely_transitions(self, transition_probs):
        """Get the most likely transitions for each seat"""
        most_likely = {}
        for from_seat, probs in transition_probs.items():
            if probs:
                most_likely[from_seat] = max(probs.items(), key=lambda x: x[1])
        return most_likely

    def analyze_ngram_patterns(self, df):
        """Analyze N-gram patterns in winning sequences"""
        if len(df) < 10:
            return

        seats = df['winning_seat'].tolist()

        # Analyze 2-grams and 3-grams
        ngrams = {
            '2-grams': {},
            '3-grams': {},
            '4-grams': {}
        }

        # 2-grams
        for i in range(len(seats) - 1):
            gram = seats[i] + seats[i+1]
            ngrams['2-grams'][gram] = ngrams['2-grams'].get(gram, 0) + 1

        # 3-grams
        for i in range(len(seats) - 2):
            gram = seats[i] + seats[i+1] + seats[i+2]
            ngrams['3-grams'][gram] = ngrams['3-grams'].get(gram, 0) + 1

        # 4-grams
        for i in range(len(seats) - 3):
            gram = seats[i] + seats[i+1] + seats[i+2] + seats[i+3]
            ngrams['4-grams'][gram] = ngrams['4-grams'].get(gram, 0) + 1

        # Find most frequent patterns
        frequent_patterns = {}
        for gram_type, grams in ngrams.items():
            if grams:
                frequent_patterns[gram_type] = sorted(
                    grams.items(), key=lambda x: x[1], reverse=True
                )[:5]  # Top 5 patterns

        self.pattern_memory['ngram_patterns'] = frequent_patterns

    def analyze_geometric_patterns(self, winfail_df):
        """Analyze geometric patterns in the winfail grid"""
        if len(winfail_df) < 9:  # Need at least 3x3 grid
            return

        # Create a grid representation
        grid = []
        for _, row in winfail_df.iterrows():
            grid_row = []
            for seat in ['A', 'B', 'C']:
                grid_row.append(1 if row.get(seat) == 'Win' else 0)
            grid.append(grid_row)

        # Analyze patterns in the grid
        patterns = {
            'horizontal_lines': self.find_horizontal_lines(grid),
            'vertical_lines': self.find_vertical_lines(grid),
            'diagonal_lines': self.find_diagonal_lines(grid),
            'corners': self.find_corner_patterns(grid),
            'centers': self.find_center_patterns(grid)
        }

        self.pattern_memory['geometric_patterns'] = patterns

    def find_horizontal_lines(self, grid):
        """Find horizontal line patterns in the grid"""
        lines = []
        for i, row in enumerate(grid):
            if sum(row) == 3:  # All three seats won in this row
                lines.append(('full_horizontal', i))
            elif sum(row) == 2:  # Two seats won
                lines.append(('partial_horizontal', i))
        return lines

    def find_vertical_lines(self, grid):
        """Find vertical line patterns in the grid"""
        lines = []
        if len(grid) < 3:
            return lines

        for col in range(3):  # A, B, C columns
            for start_row in range(len(grid) - 2):
                if (grid[start_row][col] == 1 and
                    grid[start_row + 1][col] == 1 and
                    grid[start_row + 2][col] == 1):
                    lines.append(('vertical_line', col, start_row))
        return lines

    def find_diagonal_lines(self, grid):
        """Find diagonal line patterns in the grid"""
        diagonals = []
        if len(grid) < 3:
            return diagonals

        # Main diagonal (top-left to bottom-right)
        for start_row in range(len(grid) - 2):
            if (grid[start_row][0] == 1 and
                grid[start_row + 1][1] == 1 and
                grid[start_row + 2][2] == 1):
                diagonals.append(('main_diagonal', start_row))

        # Anti-diagonal (top-right to bottom-left)
        for start_row in range(len(grid) - 2):
            if (grid[start_row][2] == 1 and
                grid[start_row + 1][1] == 1 and
                grid[start_row + 2][0] == 1):
                diagonals.append(('anti_diagonal', start_row))

        return diagonals

    def find_corner_patterns(self, grid):
        """Find corner patterns in the grid"""
        corners = []
        if len(grid) < 2:
            return corners

        # Check for corner patterns (L-shapes)
        for i in range(len(grid) - 1):
            # Top-left corner
            if grid[i][0] == 1 and grid[i][1] == 1 and grid[i+1][0] == 1:
                corners.append(('top_left_corner', i))
            # Top-right corner
            if grid[i][1] == 1 and grid[i][2] == 1 and grid[i+1][2] == 1:
                corners.append(('top_right_corner', i))

        return corners

    def find_center_patterns(self, grid):
        """Find center-focused patterns"""
        centers = []
        if len(grid) < 3:
            return centers

        for i in range(len(grid) - 2):
            # Cross pattern with center
            if (grid[i+1][1] == 1 and  # Center
                grid[i][1] == 1 and    # Top
                grid[i+2][1] == 1 and  # Bottom
                grid[i+1][0] == 1 and  # Left
                grid[i+1][2] == 1):    # Right
                centers.append(('cross_pattern', i))

        return centers

    def detect_anomalies(self, df):
        """Detect anomalies in the data using advanced AI"""
        if not ADVANCED_AI_AVAILABLE or len(df) < 20:
            return

        try:
            # Prepare features for anomaly detection
            features = []
            seats = df['winning_seat'].tolist()

            for i in range(len(seats) - 10):
                window = seats[i:i+10]
                feature_vector = [
                    window.count('A'),
                    window.count('B'),
                    window.count('C'),
                    len(set(window)),  # Diversity
                    self.calculate_entropy(window)  # Entropy
                ]
                features.append(feature_vector)

            if len(features) > 10:
                # Fit anomaly detector
                self.anomaly_detector.fit(features)

                # Detect anomalies in recent data
                recent_features = features[-5:]  # Last 5 windows
                anomaly_scores = self.anomaly_detector.decision_function(recent_features)

                # Store anomaly information
                self.pattern_memory['anomalies'] = {
                    'recent_scores': anomaly_scores.tolist(),
                    'threshold': np.percentile(anomaly_scores, 10),  # Bottom 10% as anomalies
                    'anomaly_detected': any(score < np.percentile(anomaly_scores, 10) for score in anomaly_scores[-3:])
                }

        except Exception as e:
            print(f"Error in anomaly detection: {e}")

    def calculate_entropy(self, sequence):
        """Calculate entropy of a sequence"""
        if not sequence:
            return 0

        counts = {}
        for item in sequence:
            counts[item] = counts.get(item, 0) + 1

        entropy = 0
        total = len(sequence)
        for count in counts.values():
            p = count / total
            if p > 0:
                entropy -= p * np.log2(p)

        return entropy

    def update_drift_detection(self, df):
        """Update drift detection with new data"""
        if not ADVANCED_AI_AVAILABLE or len(df) < 10:
            return

        try:
            # Calculate recent accuracy or error rate
            if hasattr(self, 'prediction_history') and self.prediction_history:
                recent_predictions = self.prediction_history[-10:]
                error_rate = sum(1 for p in recent_predictions if not p.get('correct', False)) / len(recent_predictions)

                # Update drift detector
                self.drift_detector.update(error_rate)

                # Check for drift
                if self.drift_detector.drift_detected:
                    print("Concept drift detected! Patterns may have changed.")
                    self.pattern_memory['drift_detected'] = {
                        'timestamp': datetime.datetime.now().isoformat(),
                        'error_rate': error_rate,
                        'action_needed': True
                    }

                    # Clear similarity cache when drift is detected
                    self.similarity_cache.clear()

        except Exception as e:
            print(f"Error in drift detection: {e}")

    def analyze_repeat_patterns(self, df):
        """Analyze patterns of repeats in the data"""
        if len(df) < 10:
            return

        # Get winning seats
        seats = df['winning_seat'].tolist()

        # Find repeats
        repeats = []
        current_repeat = 1

        for i in range(1, len(seats)):
            if seats[i] == seats[i-1]:
                current_repeat += 1
            else:
                if current_repeat > 1:
                    repeats.append((seats[i-1], current_repeat))
                current_repeat = 1

        # Add the last repeat if there is one
        if current_repeat > 1:
            repeats.append((seats[-1], current_repeat))

        # Analyze repeat frequencies
        repeat_counts = {'A': [], 'B': [], 'C': []}
        for seat, count in repeats:
            repeat_counts[seat].append(count)

        # Calculate average repeat lengths
        for seat in repeat_counts:
            if repeat_counts[seat]:
                avg_repeat = sum(repeat_counts[seat]) / len(repeat_counts[seat])
                self.pattern_memory['repeat_patterns'][seat] = {
                    'avg_length': avg_repeat,
                    'counts': repeat_counts[seat],
                    'total_repeats': len(repeat_counts[seat])
                }

    def analyze_block_patterns(self, df):
        """Analyze patterns of blocks (seats not winning for 7+ rounds)"""
        if len(df) < 20:
            return

        # Get winning seats
        seats = df['winning_seat'].tolist()

        # Track last win for each seat
        last_win = {'A': -1, 'B': -1, 'C': -1}
        blocks = {'A': [], 'B': [], 'C': []}

        for i, seat in enumerate(seats):
            # Update last win for current seat
            last_win[seat] = i

            # Check for blocks (7+ rounds without winning)
            for s in ['A', 'B', 'C']:
                if s != seat and i - last_win[s] >= 7:
                    blocks[s].append(i - last_win[s])

        # Store block patterns
        for seat in blocks:
            if blocks[seat]:
                self.pattern_memory['block_patterns'][seat] = {
                    'avg_length': sum(blocks[seat]) / len(blocks[seat]) if blocks[seat] else 0,
                    'counts': blocks[seat],
                    'total_blocks': len(blocks[seat])
                }

    def analyze_card_influence(self, df):
        """Analyze how cards influence winning seats"""
        if len(df) < 20:
            return

        # Look for patterns in card combinations
        card_wins = {}

        for _, row in df.iterrows():
            seat = row['winning_seat']
            cards = (row['card1'], row['card2'], row['card3'])
            trend = row['trend']

            # Track wins by card combination
            key = str(cards)
            if key not in card_wins:
                card_wins[key] = {'A': 0, 'B': 0, 'C': 0}
            card_wins[key][seat] += 1

            # Track wins by trend
            if trend not in self.pattern_memory['card_influence']:
                self.pattern_memory['card_influence'][trend] = {'A': 0, 'B': 0, 'C': 0}
            self.pattern_memory['card_influence'][trend][seat] += 1

        # Find significant card combinations
        significant_combos = {}
        for combo, wins in card_wins.items():
            total = sum(wins.values())
            if total >= 3:  # Only consider combinations that appear at least 3 times
                max_seat = max(wins, key=wins.get)
                max_pct = (wins[max_seat] / total) * 100
                if max_pct >= 70:  # Only consider combinations with strong bias
                    significant_combos[combo] = {
                        'seat': max_seat,
                        'confidence': max_pct,
                        'occurrences': total
                    }

        self.pattern_memory['card_influence']['significant_combos'] = significant_combos

    def analyze_visual_patterns(self, df):
        """Analyze visual patterns in the winfail table"""
        if len(df) < 20:
            return

        # Look for diagonal patterns
        diagonals = []
        for i in range(len(df) - 3):
            # Check for diagonal A -> B -> C
            if (df.iloc[i]['A'] == 'Win' and
                df.iloc[i+1]['B'] == 'Win' and
                df.iloc[i+2]['C'] == 'Win'):
                diagonals.append(('A->B->C', i))

            # Check for diagonal C -> B -> A
            if (df.iloc[i]['C'] == 'Win' and
                df.iloc[i+1]['B'] == 'Win' and
                df.iloc[i+2]['A'] == 'Win'):
                diagonals.append(('C->B->A', i))

        # Look for L shapes
        l_shapes = []
        for i in range(len(df) - 3):
            # Check for L shape A -> A -> B
            if (df.iloc[i]['A'] == 'Win' and
                df.iloc[i+1]['A'] == 'Win' and
                df.iloc[i+2]['B'] == 'Win'):
                l_shapes.append(('A->A->B', i))

            # Add more L shape patterns here

        # Store visual patterns
        self.pattern_memory['visual_patterns'] = {
            'diagonals': diagonals,
            'l_shapes': l_shapes
        }

    def train_models(self):
        """Train ML models on the data"""
        data = self.load_data(limit=1000)  # Load last 1000 records
        if not data or len(data['backend_data']) < 60:
            print("Not enough data for training")
            return False

        # First analyze patterns in the data
        self.analyze_patterns()

        # Prepare features and target
        df = data['backend_data']
        winfail_df = data['winfail']

        # Feature engineering with advanced pattern recognition
        features = []
        for i in range(len(df) - 60):
            window = df.iloc[i:i+60]

            # Basic features
            feature_row = [
                # Count of each seat in the window
                sum(window['winning_seat'] == 'A'),
                sum(window['winning_seat'] == 'B'),
                sum(window['winning_seat'] == 'C'),

                # Last 5 winning seats (more context)
                *[1 if s == 'A' else (2 if s == 'B' else 3) for s in window.iloc[:5]['winning_seat']],

                # Repeat features
                # Is the last win a repeat?
                1 if i >= 1 and window.iloc[0]['winning_seat'] == window.iloc[1]['winning_seat'] else 0,

                # How many repeats in the last 10 bets?
                sum(1 for j in range(1, min(10, len(window))) if window.iloc[j-1]['winning_seat'] == window.iloc[j]['winning_seat']),

                # Block features
                # How many bets since each seat last won
                i - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'A'] + [-1]),
                i - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'B'] + [-1]),
                i - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'C'] + [-1]),
            ]

            # Card features
            # Add the trend of the last winning hand
            trends = ['High Card', 'Pair', 'Sequence', 'Colour', 'Pure Sequence', 'Tilt']
            for trend in trends:
                feature_row.append(1 if window.iloc[0]['trend'] == trend else 0)

            features.append(feature_row)

        # Target is the next winning seat
        targets = [1 if s == 'A' else (2 if s == 'B' else 3) for s in df.iloc[60:60+len(features)]['winning_seat']]

        if len(features) < 10 or len(targets) < 10:
            print("Not enough processed data for training")
            return False

        # Split data
        X_train, X_test, y_train, y_test = train_test_split(features, targets, test_size=0.2, random_state=42)

        # Scale features
        scaler = StandardScaler()
        X_train = scaler.fit_transform(X_train)
        X_test = scaler.transform(X_test)

        # Train models with more advanced configurations
        models = {
            'random_forest': RandomForestClassifier(
                n_estimators=200,
                max_depth=None,
                min_samples_split=2,
                min_samples_leaf=1,
                random_state=42
            ),
            'gradient_boosting': GradientBoostingClassifier(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=5,
                random_state=42
            ),
            'neural_network': MLPClassifier(
                hidden_layer_sizes=(200, 100, 50),
                activation='relu',
                solver='adam',
                alpha=0.0001,
                batch_size='auto',
                learning_rate='adaptive',
                max_iter=2000,
                random_state=42
            )
        }

        # Train and evaluate models
        model_accuracies = {}
        for name, model in models.items():
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            model_accuracies[name] = accuracy
            print(f"{name} accuracy: {accuracy:.4f}")

        # Save the best model
        best_model_name = max(model_accuracies, key=model_accuracies.get)
        self.best_model = models[best_model_name]
        self.scaler = scaler
        self.feature_format = len(features[0])  # Save feature format for prediction

        # Save pattern memory with model info
        self.pattern_memory['best_model'] = {
            'name': best_model_name,
            'accuracy': model_accuracies[best_model_name],
            'feature_count': len(features[0])
        }
        self.save_pattern_memory()

        return True

    def predict_next_win(self):
        """Predict the next winning seat"""
        data = self.load_data(limit=1000)
        if not data or len(data['backend_data']) < 60:
            return None, None, 0

        df = data['backend_data']

        # Prepare features for prediction (same as in training)
        window = df.iloc[:60]

        # Basic features
        features = [
            # Count of each seat in the window
            sum(window['winning_seat'] == 'A'),
            sum(window['winning_seat'] == 'B'),
            sum(window['winning_seat'] == 'C'),

            # Last 5 winning seats (more context)
            *[1 if s == 'A' else (2 if s == 'B' else 3) for s in window.iloc[:5]['winning_seat']],

            # Repeat features
            # Is the last win a repeat?
            1 if len(window) >= 2 and window.iloc[0]['winning_seat'] == window.iloc[1]['winning_seat'] else 0,

            # How many repeats in the last 10 bets?
            sum(1 for j in range(1, min(10, len(window))) if window.iloc[j-1]['winning_seat'] == window.iloc[j]['winning_seat']),

            # Block features
            # How many bets since each seat last won
            0 - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'A'] + [-1]),
            0 - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'B'] + [-1]),
            0 - max([j for j in range(len(window)) if window.iloc[j]['winning_seat'] == 'C'] + [-1]),
        ]

        # Card features
        # Add the trend of the last winning hand
        trends = ['High Card', 'Pair', 'Sequence', 'Colour', 'Pure Sequence', 'Tilt']
        for trend in trends:
            features.append(1 if window.iloc[0]['trend'] == trend else 0)

        # Check if we have the right number of features
        if hasattr(self, 'feature_format') and len(features) != self.feature_format:
            print(f"Feature mismatch: expected {self.feature_format}, got {len(features)}")
            # Pad with zeros if needed
            while len(features) < self.feature_format:
                features.append(0)
            # Truncate if too many
            if len(features) > self.feature_format:
                features = features[:self.feature_format]

        # Scale features
        if hasattr(self, 'scaler') and hasattr(self, 'best_model'):
            features = self.scaler.transform([features])
            prediction = self.best_model.predict(features)[0]
            probabilities = self.best_model.predict_proba(features)[0]

            # Convert prediction to seat
            primary_seat = 'A' if prediction == 1 else ('B' if prediction == 2 else 'C')

            # Get second highest probability for secondary prediction
            sorted_probs = sorted(enumerate(probabilities), key=lambda x: x[1], reverse=True)
            secondary_idx = sorted_probs[1][0] + 1  # +1 because our classes are 1, 2, 3
            secondary_seat = 'A' if secondary_idx == 1 else ('B' if secondary_idx == 2 else 'C')

            # Calculate confidence
            confidence = probabilities[prediction-1] * 100

            # Apply special rules from the requirements

            # Check for repeat pattern
            last_seat = window.iloc[0]['winning_seat']
            repeat_confidence = self.calculate_repeat_confidence(window)

            # Rule: If repeat is highly confident (90%+ confirmed) → make repeat seat primary
            if repeat_confidence >= 90:
                primary_seat = last_seat
                # Select next best seat as secondary
                if primary_seat == secondary_seat:
                    # Find the third best seat
                    tertiary_idx = sorted_probs[2][0] + 1
                    secondary_seat = 'A' if tertiary_idx == 1 else ('B' if tertiary_idx == 2 else 'C')

            # Rule: If repeat is uncertain (below 90%) → make repeat seat secondary
            elif repeat_confidence > 0:
                if primary_seat != last_seat:
                    secondary_seat = last_seat

            # Check for blocks (seats that haven't won for 7+ rounds)
            blocks = self.detect_blocks(window)
            if blocks:
                # Avoid predicting blocked seats if possible
                if primary_seat in blocks and secondary_seat not in blocks:
                    # Swap primary and secondary
                    primary_seat, secondary_seat = secondary_seat, primary_seat
                elif primary_seat in blocks and secondary_seat in blocks:
                    # Find a non-blocked seat
                    available_seats = [s for s in ['A', 'B', 'C'] if s not in blocks]
                    if available_seats:
                        primary_seat = available_seats[0]

            return primary_seat, secondary_seat, confidence
        else:
            return None, None, 0

    def calculate_repeat_confidence(self, window):
        """Calculate confidence in a repeat pattern"""
        if len(window) < 10:
            return 0

        last_seat = window.iloc[0]['winning_seat']

        # Check if we're in a repeat streak
        current_streak = 1
        for i in range(1, len(window)):
            if window.iloc[i]['winning_seat'] == last_seat:
                current_streak += 1
            else:
                break

        # If already in a streak, higher chance of continuing
        if current_streak > 1:
            # Look at historical data for this seat
            if last_seat in self.pattern_memory['repeat_patterns']:
                pattern = self.pattern_memory['repeat_patterns'][last_seat]
                avg_length = pattern.get('avg_length', 0)

                # If current streak is less than average, higher chance of continuing
                if current_streak < avg_length:
                    return min(95, 70 + (current_streak * 5))
                else:
                    # Decreasing chance as we exceed average
                    return max(30, 90 - ((current_streak - avg_length) * 10))

            # No historical data, use heuristic
            return min(90, 50 + (current_streak * 10))

        # Not in a streak, check for patterns that lead to repeats
        # This would be more sophisticated in a full implementation
        return 30  # Base chance of repeat

    def detect_blocks(self, window):
        """Detect seats that are in a block (7+ rounds without winning)"""
        if len(window) < 7:
            return []

        blocks = []
        last_win = {'A': -1, 'B': -1, 'C': -1}

        # Find last win for each seat
        for i, row in window.iterrows():
            seat = row['winning_seat']
            if last_win[seat] == -1:
                last_win[seat] = i

        # Check for blocks
        for seat, idx in last_win.items():
            if idx == -1 or idx >= 7:  # Never won or won more than 7 bets ago
                blocks.append(seat)

        return blocks

    def setup_gui(self):
        """Set up the GUI"""
        self.root = tk.Tk()
        self.root.title("PolitePredict - Card Game Analyzer")
        self.root.geometry("1000x800")
        self.root.configure(bg="#f0f0f0")

        # Variables for GUI
        self.selected_cards = []
        self.winning_seat = tk.StringVar()
        self.selected_trend = tk.StringVar()
        self.primary_prediction = tk.StringVar(value="(Auto after 60 entries)")
        self.secondary_prediction = tk.StringVar(value="(Auto after 60 entries)")
        self.prediction_accuracy = tk.StringVar(value="(%)")
        self.current_session_count = tk.IntVar(value=0)

        # Create main frames
        self.create_header_frame()
        self.create_input_frame()
        self.create_prediction_frame()
        self.create_visual_frame()
        self.create_status_bar()

        # Start data processing thread
        threading.Thread(target=self.process_data_queue, daemon=True).start()

        # Schedule updates
        self.root.after(UPDATE_INTERVAL, self.scheduled_update)

    def create_header_frame(self):
        """Create the header frame with title and buttons"""
        header_frame = tk.Frame(self.root, bg="#007acc", pady=10)
        header_frame.pack(fill=tk.X)

        tk.Label(header_frame, text="PolitePredict", font=("Arial", 18, "bold"), bg="#007acc", fg="white").pack(side=tk.LEFT, padx=20)

        button_frame = tk.Frame(header_frame, bg="#007acc")
        button_frame.pack(side=tk.RIGHT, padx=20)

        tk.Button(button_frame, text="Train Now", command=self.train_and_predict, width=12, bg="#28a745", fg="white").pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="Delete Entries", command=self.delete_entries, width=12, bg="#dc3545", fg="white").pack(side=tk.LEFT, padx=5)
        tk.Button(button_frame, text="New Session", command=self.new_session, width=12, bg="#ffc107", fg="black").pack(side=tk.LEFT, padx=5)

    def create_input_frame(self):
        """Create the input frame for manual data entry"""
        input_frame = tk.LabelFrame(self.root, text="Data Entry", font=("Arial", 12), padx=10, pady=10, bg="#f8f9fa")
        input_frame.pack(fill=tk.X, padx=20, pady=10)

        # Last winning info display
        last_win_frame = tk.Frame(input_frame, bg="#e9ecef", relief=tk.RAISED, bd=1)
        last_win_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(last_win_frame, text="Last Win Info:", font=("Arial", 10, "bold"), bg="#e9ecef").pack(side=tk.LEFT, padx=5)
        self.last_win_info = tk.StringVar(value="No data yet")
        tk.Label(last_win_frame, textvariable=self.last_win_info, font=("Arial", 9), bg="#e9ecef", fg="#495057").pack(side=tk.LEFT, padx=5)

        # Screenshot display frame
        screenshot_frame = tk.Frame(input_frame, bg="#f8f9fa")
        screenshot_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(screenshot_frame, text="Last Screenshot:", font=("Arial", 10, "bold"), bg="#f8f9fa").pack(anchor=tk.W)
        self.screenshot_label = tk.Label(screenshot_frame, text="No screenshot captured", bg="white",
                                       relief=tk.SUNKEN, bd=1, width=40, height=10)
        self.screenshot_label.pack(pady=5)

        # Winning seat selection
        seat_frame = tk.Frame(input_frame, bg="#f8f9fa")
        seat_frame.pack(fill=tk.X, pady=5)

        tk.Label(seat_frame, text="Winning Seat:", font=("Arial", 10, "bold"), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)
        for seat in ['A', 'B', 'C']:
            tk.Radiobutton(seat_frame, text=seat, variable=self.winning_seat, value=seat, bg="#f8f9fa").pack(side=tk.LEFT, padx=10)

        # Card selection
        cards_frame = tk.Frame(input_frame, bg="#f8f9fa")
        cards_frame.pack(fill=tk.X, pady=5)

        tk.Label(cards_frame, text="Cards:", font=("Arial", 10, "bold"), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)

        # Card values
        values_frame = tk.Frame(cards_frame, bg="#f8f9fa")
        values_frame.pack(side=tk.LEFT, padx=10)

        self.card_values = []
        self.card_suits = []

        for i in range(3):
            card_frame = tk.Frame(values_frame, bg="#f8f9fa")
            card_frame.pack(side=tk.TOP, pady=2)

            value_var = tk.StringVar()
            suit_var = tk.StringVar()
            color_var = tk.StringVar(value="black")  # Default color

            # Card value dropdown
            values = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A']
            value_dropdown = ttk.Combobox(card_frame, textvariable=value_var, values=values, width=5)
            value_dropdown.pack(side=tk.LEFT, padx=2)

            # Card suit dropdown
            suits = ['♠ (Hukam)', '♣ (Chirya)', '♥ (Paan)', '♦ (Eant)']
            suit_dropdown = ttk.Combobox(card_frame, textvariable=suit_var, values=suits, width=12)
            suit_dropdown.pack(side=tk.LEFT, padx=2)

            # Color selection (automatically set based on suit)
            suit_var.trace_add("write", lambda *args, s=suit_var, c=color_var: self.update_card_color(s, c))

            self.card_values.append(value_var)
            self.card_suits.append(suit_var)

        # Trend selection
        trend_frame = tk.Frame(input_frame, bg="#f8f9fa")
        trend_frame.pack(fill=tk.X, pady=5)

        tk.Label(trend_frame, text="Trend:", font=("Arial", 10, "bold"), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)

        trends = ['High Card', 'Pair', 'Sequence', 'Colour', 'Pure Sequence', 'Tilt']
        trend_dropdown = ttk.Combobox(trend_frame, textvariable=self.selected_trend, values=trends, width=15)
        trend_dropdown.pack(side=tk.LEFT, padx=10)

        # Save button
        button_frame = tk.Frame(input_frame, bg="#f8f9fa")
        button_frame.pack(fill=tk.X, pady=10)

        # Row 0: Intelligent Game Mode (Main Control)
        game_mode_buttons = tk.Frame(button_frame, bg="#f8f9fa")
        game_mode_buttons.pack(fill=tk.X, pady=5)

        self.game_on_btn = tk.Button(game_mode_buttons, text="🎮 GAME ON", command=self.start_intelligent_game_mode,
                 width=20, bg="#007bff", fg="white", font=("Arial", 12, "bold"))
        self.game_on_btn.pack(side=tk.LEFT, padx=5)

        self.game_off_btn = tk.Button(game_mode_buttons, text="🛑 GAME OFF", command=self.stop_intelligent_game_mode,
                 width=15, bg="#dc3545", fg="white", font=("Arial", 12, "bold"), state=tk.DISABLED)
        self.game_off_btn.pack(side=tk.LEFT, padx=5)

        # Game mode status display
        self.game_mode_status = tk.Label(game_mode_buttons, text="🎮 Ready to start intelligent game mode",
                                       font=("Arial", 10), bg="#f8f9fa", fg="#495057")
        self.game_mode_status.pack(side=tk.LEFT, padx=10)

        # Row 1: Basic controls
        basic_buttons = tk.Frame(button_frame, bg="#f8f9fa")
        basic_buttons.pack(fill=tk.X, pady=2)

        tk.Button(basic_buttons, text="Save Data", command=self.save_data, width=12, bg="#28a745", fg="white").pack(side=tk.LEFT, padx=3)
        tk.Button(basic_buttons, text="Clear", command=self.clear_input, width=12, bg="#6c757d", fg="white").pack(side=tk.LEFT, padx=3)
        tk.Button(basic_buttons, text="Capture Screenshot", command=self.capture_and_process, width=15, bg="#17a2b8", fg="white").pack(side=tk.LEFT, padx=3)
        tk.Button(basic_buttons, text="Train Now", command=self.train_and_predict, width=12, bg="#ffc107", fg="black").pack(side=tk.LEFT, padx=3)

        # Row 2: Auto-detection controls
        auto_buttons = tk.Frame(button_frame, bg="#f8f9fa")
        auto_buttons.pack(fill=tk.X, pady=2)

        tk.Button(auto_buttons, text="🎯 Calibrate Game", command=self.calibrate_game_detection,
                 width=15, bg="#17a2b8", fg="white").pack(side=tk.LEFT, padx=3)

        self.start_monitoring_btn = tk.Button(auto_buttons, text="▶️ Start Auto-Monitor", command=self.start_continuous_monitoring,
                 width=18, bg="#20c997", fg="white")
        self.start_monitoring_btn.pack(side=tk.LEFT, padx=3)

        self.stop_monitoring_btn = tk.Button(auto_buttons, text="⏹️ Stop Auto-Monitor", command=self.stop_continuous_monitoring,
                 width=17, bg="#dc3545", fg="white", state=tk.DISABLED)
        self.stop_monitoring_btn.pack(side=tk.LEFT, padx=3)

        tk.Button(auto_buttons, text="New Session", command=self.new_session, width=12, bg="#6c757d", fg="white").pack(side=tk.LEFT, padx=3)

        # Row 3: Auto-capture controls
        capture_buttons = tk.Frame(button_frame, bg="#f8f9fa")
        capture_buttons.pack(fill=tk.X, pady=2)

        self.start_capture_btn = tk.Button(capture_buttons, text="📷 Start Auto-Capture", command=self.start_auto_capture,
                 width=18, bg="#fd7e14", fg="white")
        self.start_capture_btn.pack(side=tk.LEFT, padx=3)

        self.stop_capture_btn = tk.Button(capture_buttons, text="⏹️ Stop Auto-Capture", command=self.stop_auto_capture,
                 width=17, bg="#dc3545", fg="white", state=tk.DISABLED)
        self.stop_capture_btn.pack(side=tk.LEFT, padx=3)

        tk.Button(capture_buttons, text="📸 Capture Now", command=self.trigger_immediate_capture,
                 width=15, bg="#6f42c1", fg="white").pack(side=tk.LEFT, padx=3)

        # Auto-detection status indicator
        self.auto_status_frame = tk.Frame(capture_buttons, bg="#e9ecef", relief=tk.RAISED, bd=1)
        self.auto_status_frame.pack(side=tk.RIGHT, padx=5, fill=tk.Y)

        tk.Label(self.auto_status_frame, text="Auto:", font=("Arial", 9, "bold"), bg="#e9ecef").pack(side=tk.LEFT, padx=2)
        self.auto_status_indicator = tk.Label(self.auto_status_frame, text="OFF", font=("Arial", 9),
                                            bg="#e9ecef", fg="#dc3545")
        self.auto_status_indicator.pack(side=tk.LEFT, padx=2)

        # Auto-capture status indicator
        tk.Label(self.auto_status_frame, text="Capture:", font=("Arial", 9, "bold"), bg="#e9ecef").pack(side=tk.LEFT, padx=(10,2))
        self.capture_status_indicator = tk.Label(self.auto_status_frame, text="OFF", font=("Arial", 9),
                                                bg="#e9ecef", fg="#dc3545")
        self.capture_status_indicator.pack(side=tk.LEFT, padx=2)

    def create_prediction_frame(self):
        """Create the prediction display frame"""
        prediction_frame = tk.LabelFrame(self.root, text="Predictions & Accuracy", font=("Arial", 12), padx=10, pady=10, bg="#f8f9fa")
        prediction_frame.pack(fill=tk.X, padx=20, pady=10)

        # Primary prediction
        primary_frame = tk.Frame(prediction_frame, bg="#f8f9fa")
        primary_frame.pack(fill=tk.X, pady=5)

        tk.Label(primary_frame, text="Primary Prediction:", font=("Arial", 10, "bold"), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)
        tk.Label(primary_frame, textvariable=self.primary_prediction, font=("Arial", 10), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)

        # Secondary prediction
        secondary_frame = tk.Frame(prediction_frame, bg="#f8f9fa")
        secondary_frame.pack(fill=tk.X, pady=5)

        tk.Label(secondary_frame, text="Secondary Prediction:", font=("Arial", 10, "bold"), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)
        tk.Label(secondary_frame, textvariable=self.secondary_prediction, font=("Arial", 10), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)

        # Confidence
        confidence_frame = tk.Frame(prediction_frame, bg="#f8f9fa")
        confidence_frame.pack(fill=tk.X, pady=5)

        tk.Label(confidence_frame, text="Confidence:", font=("Arial", 10, "bold"), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)
        self.prediction_confidence = tk.StringVar(value="N/A")
        tk.Label(confidence_frame, textvariable=self.prediction_confidence, font=("Arial", 10), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)

        # Session accuracy
        session_acc_frame = tk.Frame(prediction_frame, bg="#f8f9fa")
        session_acc_frame.pack(fill=tk.X, pady=5)

        tk.Label(session_acc_frame, text="Session Accuracy:", font=("Arial", 10, "bold"), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)
        self.session_accuracy_display = tk.StringVar(value="0/0 (0%)")
        tk.Label(session_acc_frame, textvariable=self.session_accuracy_display, font=("Arial", 10), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)

        # Total accuracy
        total_acc_frame = tk.Frame(prediction_frame, bg="#f8f9fa")
        total_acc_frame.pack(fill=tk.X, pady=5)

        tk.Label(total_acc_frame, text="Total Accuracy:", font=("Arial", 10, "bold"), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)
        self.total_accuracy_display = tk.StringVar(value="0/0 (0%)")
        tk.Label(total_acc_frame, textvariable=self.total_accuracy_display, font=("Arial", 10), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)

        # Session count
        session_frame = tk.Frame(prediction_frame, bg="#f8f9fa")
        session_frame.pack(fill=tk.X, pady=5)

        tk.Label(session_frame, text="Current Session Count:", font=("Arial", 10, "bold"), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)
        tk.Label(session_frame, textvariable=self.current_session_count, font=("Arial", 10), bg="#f8f9fa").pack(side=tk.LEFT, padx=5)

    def create_visual_frame(self):
        """Create the visual pattern display frame"""
        visual_frame = tk.LabelFrame(self.root, text="Visual Patterns", font=("Arial", 12), padx=10, pady=10, bg="#f8f9fa")
        visual_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Canvas for drawing patterns
        self.canvas = tk.Canvas(visual_frame, bg="white", highlightthickness=1, highlightbackground="#ddd")
        self.canvas.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def create_status_bar(self):
        """Create the status bar at the bottom"""
        self.status_var = tk.StringVar(value="Ready")
        status_bar = tk.Label(self.root, textvariable=self.status_var, bd=1, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def update_card_color(self, suit_var, color_var):
        """Update card color based on selected suit"""
        suit = suit_var.get()
        if '♥' in suit or '♦' in suit:  # Hearts or Diamonds
            color_var.set("red")
        else:  # Spades or Clubs
            color_var.set("black")

    def save_data(self):
        """Save entered data to database"""
        if not self.winning_seat.get():
            messagebox.showerror("Error", "Please select a winning seat")
            return

        if not self.selected_trend.get():
            messagebox.showerror("Error", "Please select a trend")
            return

        # Collect card data
        cards = []
        for i in range(3):
            value = self.card_values[i].get()
            suit = self.card_suits[i].get().split()[0]  # Get just the symbol

            if not value or not suit:
                messagebox.showerror("Error", f"Please select value and suit for card {i+1}")
                return

            # Determine color based on suit
            color = "red" if suit in ['♥', '♦'] else "black"
            cards.append((value, color))

        try:
            # Connect to database
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Check if we have a prediction to validate
            actual_seat = self.winning_seat.get()
            self.validate_prediction(actual_seat)

            # Insert into backend_data
            cursor.execute("""
                INSERT INTO backend_data (winning_seat, card1, card2, card3, color1, color2, color3, trend, timestamp, session_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), ?)
            """, (
                actual_seat,
                cards[0][0], cards[1][0], cards[2][0],
                cards[0][1], cards[1][1], cards[2][1],
                self.selected_trend.get(),
                1  # Default session ID
            ))

            # Update winfail table
            winfail_row = []
            for seat in ['A', 'B', 'C']:
                if seat == actual_seat:
                    winfail_row.append("Win")
                else:
                    winfail_row.append("Fail")

            cursor.execute("""
                INSERT INTO winfail (A, B, C, timestamp)
                VALUES (?, ?, ?, datetime('now', 'localtime'))
            """, tuple(winfail_row))

            # Get previous winning seat for seat_changing table
            cursor.execute("SELECT winning_seat FROM backend_data ORDER BY id DESC LIMIT 1 OFFSET 1")
            result = cursor.fetchone()
            previous_win = result[0] if result else None

            # Update seat_changing table if we have a previous win
            if previous_win:
                self.update_seat_changing_table(actual_seat, previous_win)

            conn.commit()
            conn.close()

            # Update session count
            current_count = self.current_session_count.get() + 1
            self.current_session_count.set(current_count)

            # Update last win info display
            self.update_last_win_info(actual_seat, cards, self.selected_trend.get())

            # Auto-trigger prediction if we have 60+ entries
            if current_count >= 60:
                self.train_and_predict()

            # Clear input fields
            self.clear_input()

            # Update status
            self.status_var.set(f"Data saved successfully. Session count: {current_count}")

            # Update visual display
            self.update_visual_display()

        except Exception as e:
            messagebox.showerror("Database Error", str(e))
            self.status_var.set(f"Error: {str(e)}")

    def update_last_win_info(self, seat, cards, trend):
        """Update the last winning info display"""
        try:
            card_strs = []
            for value, color in cards:
                card_strs.append(f"{value}({color})")

            cards_str = ", ".join(card_strs)
            info_text = f"Seat {seat}, Cards: [{cards_str}], Trend: {trend}"
            self.last_win_info.set(info_text)
        except Exception as e:
            print(f"Error updating last win info: {e}")
            self.last_win_info.set("Error displaying last win info")

    def validate_prediction(self, actual_seat):
        """Enhanced validation of prediction against actual result"""
        # Check if we have a prediction to validate
        if not hasattr(self, 'last_primary') or not hasattr(self, 'last_secondary'):
            return

        # Check if prediction was correct
        primary_correct = self.last_primary == actual_seat
        secondary_correct = self.last_secondary == actual_seat
        any_correct = primary_correct or secondary_correct

        # Update session accuracy
        if any_correct:
            self.session_accuracy['correct'] += 1
            self.total_accuracy['correct'] += 1
        self.session_accuracy['total'] += 1
        self.total_accuracy['total'] += 1

        # Update accuracy displays
        self.update_accuracy_displays()

        # Store prediction result for drift detection
        prediction_result = {
            'primary': self.last_primary,
            'secondary': self.last_secondary,
            'actual': actual_seat,
            'correct': any_correct,
            'timestamp': datetime.datetime.now().isoformat()
        }
        self.prediction_history.append(prediction_result)

        # Keep only recent history
        if len(self.prediction_history) > 100:
            self.prediction_history = self.prediction_history[-100:]

        # Update pattern memory
        if 'prediction_accuracy' not in self.pattern_memory:
            self.pattern_memory['prediction_accuracy'] = {
                'overall': [],
                'primary': [],
                'secondary': [],
                'any': []
            }

        # Add result (1 for correct, 0 for incorrect)
        self.pattern_memory['prediction_accuracy']['primary'].append(1 if primary_correct else 0)
        self.pattern_memory['prediction_accuracy']['secondary'].append(1 if secondary_correct else 0)
        self.pattern_memory['prediction_accuracy']['any'].append(1 if any_correct else 0)

        # Calculate overall accuracy
        if self.pattern_memory['prediction_accuracy']['primary']:
            primary_accuracy = sum(self.pattern_memory['prediction_accuracy']['primary']) / len(self.pattern_memory['prediction_accuracy']['primary'])
            secondary_accuracy = sum(self.pattern_memory['prediction_accuracy']['secondary']) / len(self.pattern_memory['prediction_accuracy']['secondary'])
            any_accuracy = sum(self.pattern_memory['prediction_accuracy']['any']) / len(self.pattern_memory['prediction_accuracy']['any'])

            overall_accuracy = (primary_accuracy * 0.7) + (secondary_accuracy * 0.3)
            self.pattern_memory['prediction_accuracy']['overall'].append(overall_accuracy)

        # Log the prediction result
        self.pattern_memory['prediction_log'].append({
            'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'primary': self.last_primary,
            'secondary': self.last_secondary,
            'actual': actual_seat,
            'primary_correct': primary_correct,
            'secondary_correct': secondary_correct,
            'any_correct': any_correct
        })

        # Update drift detection
        if ADVANCED_AI_AVAILABLE and self.drift_detector:
            self.drift_detector.update(1 if not any_correct else 0)
            if self.drift_detector.drift_detected:
                print("Concept drift detected! Retraining models...")
                threading.Thread(target=self.train_models, daemon=True).start()

        # Save pattern memory
        self.save_pattern_memory()

        # Auto-retrain if accuracy is consistently low
        if len(self.prediction_history) >= 10:
            recent_accuracy = sum(1 for p in self.prediction_history[-10:] if p['correct']) / 10
            if recent_accuracy < 0.5:  # Less than 50% accuracy in last 10 predictions
                print("Auto-retraining due to low recent accuracy")
                threading.Thread(target=self.train_models, daemon=True).start()

    def update_accuracy_displays(self):
        """Update the accuracy display variables"""
        # Session accuracy
        if self.session_accuracy['total'] > 0:
            session_pct = (self.session_accuracy['correct'] / self.session_accuracy['total']) * 100
            session_text = f"{self.session_accuracy['correct']}/{self.session_accuracy['total']} ({session_pct:.1f}%)"
        else:
            session_text = "0/0 (0%)"
        self.session_accuracy_display.set(session_text)

        # Total accuracy
        if self.total_accuracy['total'] > 0:
            total_pct = (self.total_accuracy['correct'] / self.total_accuracy['total']) * 100
            total_text = f"{self.total_accuracy['correct']}/{self.total_accuracy['total']} ({total_pct:.1f}%)"
        else:
            total_text = "0/0 (0%)"
        self.total_accuracy_display.set(total_text)

    def clear_input(self):
        """Clear all input fields"""
        self.winning_seat.set("")
        self.selected_trend.set("")
        for i in range(3):
            self.card_values[i].set("")
            self.card_suits[i].set("")

    def capture_and_process(self):
        """Capture screenshot and process it with OCR"""
        self.status_var.set("Capturing and processing screenshot...")
        self.root.update_idletasks()

        img_bgr = self.capture_screenshot()
        if img_bgr is None:
            messagebox.showerror("Error", "Failed to capture screenshot.")
            self.status_var.set("Screenshot capture failed.")
            return

        # Store screenshot for display
        self.store_screenshot(img_bgr)

        # Process the image with OCR
        extracted_data = self.extract_data_from_image(img_bgr)

        if extracted_data and not extracted_data.get("error"):
            # Update GUI with extracted data
            self.winning_seat.set(extracted_data['winning_seat'])

            if len(extracted_data['cards']) == 3:
                for i in range(3):
                    self.card_values[i].set(extracted_data['cards'][i]['value'])

                    # Map suit name to GUI string
                    suit_symbol_to_gui_str = {
                        'Spade': '♠ (Hukam)', 'Club': '♣ (Chirya)',
                        'Heart': '♥ (Paan)', 'Diamond': '♦ (Eant)',
                        'UnknownSuit': ''
                    }
                    gui_suit_str = suit_symbol_to_gui_str.get(extracted_data['cards'][i]['suit_name'], "")
                    self.card_suits[i].set(gui_suit_str)
            else:
                messagebox.showwarning("OCR Data Incomplete", "Could not extract all 3 cards reliably.")

            self.selected_trend.set(extracted_data['trend'])
            self.status_var.set("Screenshot processed. Please VERIFY and save.")
            messagebox.showinfo("OCR Result", "Data extracted. Please review carefully before saving.")
        else:
            error_msg = extracted_data.get("error", "Unknown error during OCR.") if extracted_data else "OCR failed."
            messagebox.showerror("OCR Error", f"Could not extract data automatically: {error_msg}\nPlease enter data manually.")
            self.status_var.set(f"OCR failed: {error_msg}")

    def store_screenshot(self, img_bgr):
        """Store screenshot for display and management"""
        try:
            # Convert BGR to RGB for PIL
            img_rgb = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(img_rgb)

            # Resize for display
            display_size = (300, 200)
            pil_image.thumbnail(display_size, Image.Resampling.LANCZOS)

            # Convert to PhotoImage for tkinter
            photo = ImageTk.PhotoImage(pil_image)

            # Store in history
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_data = {
                'timestamp': timestamp,
                'image': photo,
                'pil_image': pil_image
            }

            self.screenshot_history.append(screenshot_data)

            # Keep only last N screenshots
            if len(self.screenshot_history) > MAX_SCREENSHOTS_STORED:
                self.screenshot_history.pop(0)

            # Update display if screenshot display exists
            if hasattr(self, 'screenshot_label'):
                self.screenshot_label.configure(image=photo)
                self.screenshot_label.image = photo  # Keep a reference

        except Exception as e:
            print(f"Error storing screenshot: {e}")

    def train_and_predict(self):
        """Train models and make prediction"""
        self.status_var.set("Training models...")

        # Train in a separate thread to avoid freezing the GUI
        def train_thread():
            success = self.train_models()
            if success:
                primary, secondary, confidence = self.predict_next_win()

                # Store predictions for later validation
                self.last_primary = primary
                self.last_secondary = secondary
                self.last_confidence = confidence

                # Update GUI from main thread
                self.root.after(0, lambda: self.primary_prediction.set(f"Primary: {primary}"))
                self.root.after(0, lambda: self.secondary_prediction.set(f"Secondary: {secondary}"))
                self.root.after(0, lambda: self.prediction_confidence.set(f"{confidence:.2f}%"))
                self.root.after(0, lambda: self.status_var.set("Prediction complete"))
            else:
                self.root.after(0, lambda: self.status_var.set("Training failed - not enough data"))

        threading.Thread(target=train_thread, daemon=True).start()

    def delete_entries(self):
        """Delete recent entries from database"""
        try:
            # Ask how many entries to delete
            num_to_delete = simpledialog.askinteger("Delete Entries",
                                                  "How many recent entries do you want to delete?",
                                                  minvalue=1, maxvalue=10)

            if not num_to_delete:
                return

            # Ask for confirmation
            response = messagebox.askyesno("Delete Entries",
                                         f"Are you sure you want to delete the last {num_to_delete} entries? This cannot be undone.")
            if not response:
                return

            # Connect to the database
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()

            # Get the IDs to delete from backend_data
            cursor.execute(f"SELECT id FROM backend_data ORDER BY id DESC LIMIT {num_to_delete}")
            ids_to_delete = [row[0] for row in cursor.fetchall()]

            if not ids_to_delete:
                messagebox.showinfo("No Data", "No entries found to delete.")
                conn.close()
                return

            # Delete from backend_data
            for id_to_delete in ids_to_delete:
                cursor.execute("DELETE FROM backend_data WHERE id = ?", (id_to_delete,))

            # Delete from winfail
            cursor.execute(f"DELETE FROM winfail WHERE id IN (SELECT id FROM winfail ORDER BY id DESC LIMIT {num_to_delete})")

            # Delete from seat_changing
            cursor.execute(f"DELETE FROM seat_changing WHERE id IN (SELECT id FROM seat_changing ORDER BY id DESC LIMIT {num_to_delete})")

            conn.commit()
            conn.close()

            # Update session count
            current_count = self.current_session_count.get() - num_to_delete
            if current_count < 0:
                current_count = 0
            self.current_session_count.set(current_count)

            # Update visual display
            self.update_visual_display()

            self.status_var.set(f"Deleted {num_to_delete} entries. Session count: {current_count}")

        except Exception as e:
            messagebox.showerror("Delete Error", str(e))
            self.status_var.set(f"Error: {str(e)}")

    def new_session(self):
        """Start a new session"""
        response = messagebox.askyesno("New Session",
                                     "Are you sure you want to start a new session? This will reset the session counter and accuracy.")
        if not response:
            return

        # Reset session counter
        self.current_session_count.set(0)

        # Reset session accuracy
        self.session_accuracy = {'correct': 0, 'total': 0}
        self.update_accuracy_displays()

        # Clear predictions
        self.primary_prediction.set("(Auto after 60 entries)")
        self.secondary_prediction.set("(Auto after 60 entries)")
        self.prediction_confidence.set("N/A")

        # Clear last win info
        self.last_win_info.set("No data yet")

        self.status_var.set("New session started")

    def update_visual_display(self):
        """Update the visual pattern display"""
        # Clear canvas
        self.canvas.delete("all")

        # Load recent data
        data = self.load_data(limit=20)  # Show last 20 records
        if not data or len(data['winfail']) == 0:
            return

        # Draw winfail pattern
        winfail_df = data['winfail']

        # Constants for drawing
        circle_radius = 12
        h_spacing = 40
        v_spacing = 30
        margin_x = 50
        margin_y = 50

        # Draw grid
        for i, row in enumerate(winfail_df.itertuples()):
            y = margin_y + i * v_spacing

            # Draw row label (bet number)
            self.canvas.create_text(margin_x - 20, y, text=str(i+1), font=("Arial", 8))

            # Draw circles for each seat
            for j, seat in enumerate(['A', 'B', 'C']):
                x = margin_x + j * h_spacing

                # Draw seat label on first row
                if i == 0:
                    self.canvas.create_text(x, margin_y - 20, text=seat, font=("Arial", 10, "bold"))

                # Draw circle
                color = "green" if getattr(row, seat) == "Win" else "lightgray"
                self.canvas.create_oval(x-circle_radius, y-circle_radius, x+circle_radius, y+circle_radius,
                                       fill=color, outline="black")

    def process_data_queue(self):
        """Process data from the queue (for background tasks)"""
        try:
            while not self.data_queue.empty():
                item = self.data_queue.get_nowait()
                # Process the item
                # ...
        except Exception as e:
            print(f"Error processing queue: {str(e)}")

        # Schedule next processing
        self.root.after(100, self.process_data_queue)

    def scheduled_update(self):
        """Perform scheduled updates"""
        # Update visual display
        self.update_visual_display()

        # Schedule next update
        self.root.after(UPDATE_INTERVAL, self.scheduled_update)

    def run(self):
        """Run the application"""
        self.root.mainloop()


if __name__ == "__main__":
    try:
        print("Starting PolitePredict application...")
        app = PolitePredict()
        print("Starting main loop...")
        app.run()
    except Exception as e:
        print(f"Error starting application: {str(e)}")
        import traceback
        traceback.print_exc()
